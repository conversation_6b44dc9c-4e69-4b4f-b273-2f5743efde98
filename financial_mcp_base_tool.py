"""
MCP工具基类
提供统一的工具接口和通用功能
替代Agent-Zero的Tool基类，适配MCP服务器环境
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime


class BaseTool:
    """MCP工具基类
    
    提供统一的工具接口和通用功能
    替代Agent-Zero的Tool基类
    """
    
    def __init__(self, name: str = ""):
        self.name = name or self.__class__.__name__
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"mcp.tools.{self.name}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    async def execute(self, **kwargs) -> str:
        """执行工具功能
        
        Args:
            **kwargs: 工具参数
            
        Returns:
            str: 执行结果
        """
        raise NotImplementedError("子类必须实现execute方法")
    
    def format_response(self, data: Dict[str, Any], template: str = "") -> str:
        """格式化响应数据
        
        Args:
            data: 原始数据
            template: 格式化模板
            
        Returns:
            str: 格式化后的字符串
        """
        if not data:
            return "❌ 数据为空"
        
        # 检查API错误
        if data.get('errorcode') != 0:
            error_msg = data.get('reason', data.get('errmsg', '未知错误'))
            return f"❌ API调用失败: {error_msg}"
        
        # 如果有自定义模板，使用模板格式化
        if template:
            try:
                return template.format(**data)
            except Exception as e:
                self.logger.warning(f"模板格式化失败: {e}")
        
        # 默认格式化
        return self._default_format(data)
    
    def _default_format(self, data: Dict[str, Any]) -> str:
        """默认格式化方法"""
        return f"✅ 查询成功\n数据: {data}"
    
    def handle_error(self, error: Exception) -> str:
        """处理错误
        
        Args:
            error: 异常对象
            
        Returns:
            str: 错误信息
        """
        error_msg = str(error)
        self.logger.error(f"工具执行错误: {error_msg}")
        return f"❌ 执行失败: {error_msg}"
    
    def validate_stock_codes(self, codes: str) -> tuple[bool, str]:
        """验证股票代码格式
        
        Args:
            codes: 股票代码字符串
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if not codes or not codes.strip():
            return False, "股票代码不能为空"
        
        import re
        # 验证股票代码格式：6位数字.SZ/SH
        pattern = r'^\d{6}\.(SZ|SH)(,\d{6}\.(SZ|SH))*$'
        if not re.match(pattern, codes.upper()):
            return False, "股票代码格式错误，应为：600519.SH 或 000858.SZ"
        
        return True, ""
    
    def validate_date_range(self, start_date: str, end_date: str) -> tuple[bool, str]:
        """验证日期范围
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            
            if start > end:
                return False, "开始日期不能大于结束日期"
            
            # 检查日期是否过于久远
            now = datetime.now()
            if (now - start).days > 365 * 5:  # 5年
                return False, "查询时间范围过长，建议不超过5年"
            
            return True, ""
            
        except ValueError:
            return False, "日期格式错误，应为：YYYY-MM-DD"
    
    def get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def log_execution(self, action: str, params: Dict[str, Any] = None):
        """记录执行日志
        
        Args:
            action: 执行动作
            params: 参数信息
        """
        params_str = f", 参数: {params}" if params else ""
        self.logger.info(f"[{self.name}] 执行: {action}{params_str}")


class FinancialBaseTool(BaseTool):
    """金融工具基类
    
    提供金融数据相关的通用功能
    """
    
    def __init__(self, name: str = ""):
        super().__init__(name)
        self.api_client = None
    
    def _initialize_api_client(self):
        """初始化API客户端"""
        if not self.api_client:
            from clients.ifind_client import FinancialAPIClient
            self.api_client = FinancialAPIClient()
            self.logger.info("金融API客户端初始化成功")
    
    def extract_stock_codes(self, text: str) -> str:
        """从文本中提取股票代码"""
        import re
        
        # 匹配标准股票代码格式
        pattern = r'\d{6}\.(SZ|SH|sz|sh)'
        matches = re.findall(pattern, text, re.IGNORECASE)
        
        if matches:
            codes = []
            for match in re.finditer(pattern, text, re.IGNORECASE):
                full_code = match.group(0).upper()
                codes.append(full_code)
            return ','.join(codes)
        
        # 尝试从股票名称映射
        stock_mapping = {
            '贵州茅台': '600519.SH',
            '五粮液': '000858.SZ',
            '比亚迪': '002594.SZ',
            '平安银行': '000001.SZ',
            '山西汾酒': '600809.SH',
            '招商银行': '600036.SH',
            '中国平安': '601318.SH',
            '宁德时代': '300750.SZ',
            '腾讯控股': '00700.HK',
            '阿里巴巴': '09988.HK'
        }
        
        for name, code in stock_mapping.items():
            if name in text:
                return code
        
        return ""
    
    def detect_query_type(self, query: str) -> str:
        """检测查询类型"""
        query_lower = query.lower()
        
        # 技术指标关键词
        tech_keywords = ['macd', 'rsi', 'kdj', 'ma', '技术指标', '均线', '指标分析']
        if any(keyword in query_lower for keyword in tech_keywords):
            return "technical_indicators"
        
        # 历史数据关键词
        history_keywords = ['历史', '走势', '过去', '月', '年', '日']
        if any(keyword in query_lower for keyword in history_keywords):
            return "history"
        
        # 财务报表关键词
        report_keywords = ['财报', '报表', '财务', '利润', '资产', '现金流']
        if any(keyword in query_lower for keyword in report_keywords):
            return "financial_report"
        
        # 默认为实时查询
        return "real_time"
    
    def format_financial_data(self, data: Dict[str, Any], data_type: str = "real_time") -> str:
        """格式化金融数据"""
        if data.get('errorcode') != 0:
            error_msg = data.get('reason', data.get('errmsg', '未知错误'))
            return f"❌ 查询失败: {error_msg}"
        
        tables = data.get('data', {}).get('tables', [])
        if not tables or not tables[0].get('table'):
            return "❌ 未获取到数据"
        
        table_data = tables[0]['table']
        if not isinstance(table_data, list) or not table_data:
            return "❌ 数据格式错误"
        
        # 根据数据类型选择格式化方法
        if data_type == "real_time":
            return self._format_real_time_data(table_data[0])
        elif data_type == "history":
            return self._format_history_data(table_data)
        elif data_type == "technical_indicators":
            return self._format_technical_data(table_data)
        else:
            return self._format_basic_data(table_data[0])
    
    def _format_real_time_data(self, data: Dict[str, Any]) -> str:
        """格式化实时行情数据"""
        return f"""
📊 **实时行情数据**

🏷️ **股票代码**: {data.get('code', 'N/A')}
💰 **最新价**: {data.get('latest', 'N/A')}
📈 **昨收价**: {data.get('preClose', 'N/A')}
📊 **涨跌额**: {data.get('change', 'N/A')}
📈 **涨跌幅**: {data.get('changeRatio', 'N/A')}
📦 **成交量**: {data.get('volume', 'N/A')}
💵 **成交额**: {data.get('amount', 'N/A')}

⏰ **查询时间**: {self.get_current_timestamp()}
"""
    
    def _format_history_data(self, data: List[Dict[str, Any]]) -> str:
        """格式化历史数据"""
        if not data:
            return "❌ 历史数据为空"
        
        result = "📈 **历史行情数据**\n\n"
        for i, record in enumerate(data[-5:]):  # 显示最近5条记录
            result += f"**{record.get('date', 'N/A')}**: "
            result += f"开盘 {record.get('open', 'N/A')}, "
            result += f"收盘 {record.get('close', 'N/A')}, "
            result += f"涨跌幅 {record.get('changeRatio', 'N/A')}\n"
        
        result += f"\n⏰ **查询时间**: {self.get_current_timestamp()}"
        return result
    
    def _format_technical_data(self, data: List[Dict[str, Any]]) -> str:
        """格式化技术指标数据"""
        if not data:
            return "❌ 技术指标数据为空"
        
        latest_data = data[-1] if data else {}
        result = "📊 **技术指标分析**\n\n"
        
        # 显示主要技术指标
        indicators = ['MACD', 'RSI', 'KDJ_K', 'MA']
        for indicator in indicators:
            if indicator in latest_data:
                result += f"**{indicator}**: {latest_data[indicator]}\n"
        
        result += f"\n⏰ **分析时间**: {self.get_current_timestamp()}"
        return result
    
    def _format_basic_data(self, data: Dict[str, Any]) -> str:
        """格式化基础数据"""
        return f"""
📋 **基础数据**

🏷️ **股票代码**: {data.get('code', 'N/A')}
💰 **总股本**: {data.get('totalShares', 'N/A')}
💵 **总市值**: {data.get('totalCapital', 'N/A')}
📊 **市盈率**: {data.get('pe_ttm', 'N/A')}
📈 **市净率**: {data.get('pb', 'N/A')}

⏰ **查询时间**: {self.get_current_timestamp()}
"""
