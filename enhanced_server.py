#!/usr/bin/env python3
"""
金融分析MCP服务器 - 增强版
提供完整的金融数据分析功能，包括VWAP、多周期MA等高级功能
"""

import asyncio
import logging
import os
import sys
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import uvicorn
from fastapi import FastAPI
from fastmcp import FastMCP
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入工具类
from clients.ifind_client import FinancialAPIClient
from tools.base_tool import FinancialBaseTool

# 创建FastMCP服务器实例
mcp_server = FastMCP(
    name="Enhanced Financial Analysis MCP Server",
    instructions="""
    专业金融数据分析MCP服务器 - 增强版，提供以下功能：
    
    🏦 核心功能：
    - 实时股票行情查询
    - 历史数据分析
    - 技术指标计算（MACD、RSI、KDJ等50+指标）
    - VWAP（成交量加权平均价）分析
    - 多周期移动平均线（支持任意周期）
    - 财务报表数据
    - 基础数据查询
    
    📊 支持的技术指标：
    - 趋势类：MACD、MA(任意周期)、EXPMA、DMA、TRIX
    - 震荡类：RSI、KDJ、CCI、WR、ROC
    - 成交量：OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD
    - 支撑阻力：BOLL、CDP、MIKE
    - 波动率：ATR、STD、BIAS
    - 特殊指标：VWAP（成交量加权平均价）
    
    🎯 智能识别：
    - 自动识别股票代码和名称
    - 支持自然语言查询
    - 智能查询类型检测
    - VWAP专项识别
    
    数据源：同花顺iFinD专业金融数据
    """
)

# 全局API客户端实例
api_client = None

def get_api_client() -> FinancialAPIClient:
    """获取API客户端实例"""
    global api_client
    if api_client is None:
        api_client = FinancialAPIClient()
        logger.info("金融API客户端初始化成功")
    return api_client

# 创建工具实例
financial_tool = FinancialBaseTool("enhanced_financial_data")

def extract_ma_periods(query: str) -> List[str]:
    """从查询中提取MA周期"""
    periods = []
    
    # 匹配数字+日/天的模式，如"25日均线"、"55天移动平均"
    patterns = [
        r'(\d+)日[均移动平均线]',
        r'(\d+)天[均移动平均线]',
        r'MA(\d+)',
        r'ma(\d+)',
        r'(\d+)日MA',
        r'(\d+)天MA'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        periods.extend(matches)
    
    # 去重并排序
    periods = list(set(periods))
    periods.sort(key=int)
    
    return periods

def format_vwap_result(result: Dict[str, Any], codes: str, query_type: str) -> str:
    """格式化VWAP查询结果"""
    if result.get('errorcode') != 0:
        error_msg = result.get('reason', result.get('errmsg', '未知错误'))
        return f"❌ VWAP查询失败: {error_msg}"
    
    tables = result.get('data', {}).get('tables', [])
    if not tables or not tables[0].get('table'):
        return "❌ 未获取到VWAP数据"
    
    table_data = tables[0]['table']
    if not isinstance(table_data, list):
        table_data = [table_data]
    
    if query_type == "real_time":
        data = table_data[0] if table_data else {}
        return f"""
📊 **VWAP实时数据**

🏷️ **股票代码**: {data.get('code', codes)}
💰 **最新价**: {data.get('latest', 'N/A')}
📊 **VWAP**: {data.get('vwap', 'N/A')}
📦 **成交量**: {data.get('volume', 'N/A')}
💵 **成交额**: {data.get('amount', 'N/A')}

⏰ **查询时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    else:
        # 历史VWAP数据
        result_text = f"📈 **VWAP历史数据** ({codes})\n\n"
        for i, record in enumerate(table_data[-5:]):  # 显示最近5条记录
            result_text += f"**{record.get('date', 'N/A')}**: "
            result_text += f"收盘 {record.get('close', 'N/A')}, "
            result_text += f"VWAP {record.get('vwap', 'N/A')}\n"
        
        result_text += f"\n⏰ **查询时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        return result_text

@mcp_server.tool()
async def get_stock_real_time(
    codes: str,
    indicators: str = "latest,preClose,change,changeRatio,volume,amount"
) -> str:
    """
    获取股票实时行情数据
    
    Args:
        codes: 股票代码，支持多个代码用逗号分隔，如 "600519.SH,000858.SZ"
        indicators: 查询指标，如 "latest,preClose,change,changeRatio,volume"
    
    Returns:
        JSON格式的实时行情数据
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("实时行情查询", {"codes": codes, "indicators": indicators})
        
        # 调用API
        client = get_api_client()
        result = await client.get_real_time_quotation(codes, indicators)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "real_time")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_stock_history(
    codes: str,
    start_date: str,
    end_date: str,
    indicators: str = "preClose,open,high,low,close,volume,changeRatio"
) -> str:
    """
    获取股票历史行情数据
    
    Args:
        codes: 股票代码，如 "600519.SH"
        start_date: 开始日期，格式 YYYY-MM-DD
        end_date: 结束日期，格式 YYYY-MM-DD
        indicators: 查询指标
    
    Returns:
        历史行情数据
    """
    try:
        # 验证参数
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"
        
        is_valid, error_msg = financial_tool.validate_date_range(start_date, end_date)
        if not is_valid:
            return f"❌ 日期范围错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("历史数据查询", {
            "codes": codes, 
            "start_date": start_date, 
            "end_date": end_date
        })
        
        # 调用API
        client = get_api_client()
        result = await client.get_history_quotation(codes, start_date, end_date, indicators)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "history")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_stock_basic_data(
    codes: str,
    indicators: str = "totalShares,totalCapital,mv,pb,pe_ttm"
) -> str:
    """
    获取股票基础数据
    
    Args:
        codes: 股票代码，如 "600519.SH"
        indicators: 查询指标
    
    Returns:
        基础数据信息
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("基础数据查询", {"codes": codes, "indicators": indicators})
        
        # 调用API
        client = get_api_client()
        result = await client.get_basic_data(codes, indicators)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "basic")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_financial_report(
    codes: str,
    report_type: str = "",
    period: str = ""
) -> str:
    """
    获取财务报表数据
    
    Args:
        codes: 股票代码，如 "600519.SH"
        report_type: 报表类型
        period: 报告期间
    
    Returns:
        财务报表数据
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("财务报表查询", {
            "codes": codes, 
            "report_type": report_type, 
            "period": period
        })
        
        # 调用API
        client = get_api_client()
        result = await client.get_financial_report(codes, report_type, period)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "financial_report")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_vwap_data(
    codes: str,
    query_type: str = "real_time",
    start_date: str = "",
    end_date: str = ""
) -> str:
    """
    获取VWAP（成交量加权平均价）数据

    Args:
        codes: 股票代码，如 "600519.SH"
        query_type: 查询类型，"real_time"或"history"
        start_date: 开始日期（历史查询用）
        end_date: 结束日期（历史查询用）

    Returns:
        VWAP数据分析结果
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"

        # 记录执行日志
        financial_tool.log_execution("VWAP查询", {
            "codes": codes,
            "query_type": query_type,
            "start_date": start_date,
            "end_date": end_date
        })

        client = get_api_client()

        if query_type == "real_time":
            # 实时VWAP查询
            result = await client.get_real_time_quotation(codes, "latest,volume,amount,vwap")
        else:
            # 历史VWAP查询
            if not start_date:
                end_date = datetime.now().strftime("%Y-%m-%d")
                start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

            result = await client.get_history_quotation(codes, start_date, end_date, "close,volume,amount,vwap")

        # 格式化VWAP结果
        return format_vwap_result(result, codes, query_type)

    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def analyze_technical_indicators(
    query: str = "",
    codes: str = "",
    indicators: str = "MACD,RSI,KDJ",
    period: str = "1M"
) -> str:
    """
    技术指标分析（增强版）

    Args:
        query: 自然语言查询，如 "分析贵州茅台的MACD指标"
        codes: 股票代码，如 "600519.SH"
        indicators: 技术指标，如 "MACD,RSI,KDJ"
        period: 分析周期，如 "1M"（1个月）

    Returns:
        技术指标分析结果
    """
    try:
        # 如果提供了query，从中提取股票代码
        if query and not codes:
            codes = financial_tool.extract_stock_codes(query)

        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码（如600519.SH）或股票名称"

        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"

        # 检查是否有多周期MA查询
        ma_periods = extract_ma_periods(query) if query else []
        if ma_periods:
            # 构建多周期MA指标
            ma_indicators = [f"MA{period}" for period in ma_periods]
            # 移除原有的MA指标，添加多周期MA
            other_indicators = [ind for ind in indicators.split(',') if not ind.strip().upper().startswith('MA')]
            indicators = ','.join(other_indicators + ma_indicators)

        # 计算时间范围
        end_date = datetime.now()
        if period == "1M":
            start_date = end_date - timedelta(days=30)
        elif period == "3M":
            start_date = end_date - timedelta(days=90)
        elif period == "6M":
            start_date = end_date - timedelta(days=180)
        elif period == "1Y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)

        # 记录执行日志
        financial_tool.log_execution("技术指标分析", {
            "query": query,
            "codes": codes,
            "indicators": indicators,
            "period": period,
            "ma_periods": ma_periods
        })

        # 调用API
        client = get_api_client()
        result = await client.get_technical_indicators(
            codes=codes,
            starttime=start_date.strftime("%Y-%m-%d"),
            endtime=end_date.strftime("%Y-%m-%d"),
            indicators=indicators
        )

        # 格式化返回结果
        return _format_enhanced_technical_result(result, codes, indicators, period, ma_periods)

    except Exception as e:
        return financial_tool.handle_error(e)

def _format_enhanced_technical_result(result: Dict[str, Any], codes: str,
                                    indicators: str, period: str, ma_periods: List[str]) -> str:
    """格式化增强版技术指标结果"""
    if result.get('errorcode') != 0:
        error_msg = result.get('reason', result.get('errmsg', '未知错误'))
        return f"❌ 技术指标查询失败: {error_msg}"

    tables = result.get('data', {}).get('tables', [])
    if not tables or not tables[0].get('table'):
        return "❌ 未获取到技术指标数据"

    table_data = tables[0]['table']
    if not isinstance(table_data, list):
        table_data = [table_data]

    if not table_data:
        return "❌ 技术指标数据为空"

    latest_data = table_data[-1]

    result_text = f"""
📊 **技术指标分析报告**

🏷️ **股票代码**: {latest_data.get('code', codes)}
📅 **分析日期**: {latest_data.get('date', datetime.now().strftime('%Y-%m-%d'))}
⏱️ **分析周期**: {period}

📈 **技术指标数据**:
"""

    # 显示主要技术指标
    main_indicators = ['MACD', 'RSI', 'KDJ_K', 'KDJ_D', 'KDJ_J']
    for indicator in main_indicators:
        if indicator in latest_data:
            result_text += f"- **{indicator}**: {latest_data[indicator]}\n"

    # 显示多周期MA
    if ma_periods:
        result_text += f"\n📊 **多周期移动平均线**:\n"
        for period_num in ma_periods:
            ma_key = f"MA{period_num}"
            if ma_key in latest_data:
                result_text += f"- **{period_num}日均线**: {latest_data[ma_key]}\n"

    # 显示成交量指标
    volume_indicators = ['OBV', 'VR', 'VRSI', 'VMACD', 'VMA']
    volume_data = [ind for ind in volume_indicators if ind in latest_data]
    if volume_data:
        result_text += f"\n📦 **成交量指标**:\n"
        for indicator in volume_data:
            result_text += f"- **{indicator}**: {latest_data[indicator]}\n"

    # 生成交易信号
    signals = _generate_trading_signals(latest_data)
    if signals:
        result_text += f"\n🎯 **交易信号**:\n{signals}"

    result_text += f"\n⏰ **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    return result_text

def _generate_trading_signals(data: Dict[str, Any]) -> str:
    """生成交易信号"""
    signals = []

    # MACD信号
    macd = data.get('MACD')
    if macd:
        try:
            macd_val = float(macd)
            if macd_val > 0:
                signals.append("🟢 MACD金叉，看涨信号")
            elif macd_val < 0:
                signals.append("🔴 MACD死叉，看跌信号")
        except:
            pass

    # RSI信号
    rsi = data.get('RSI')
    if rsi:
        try:
            rsi_val = float(rsi)
            if rsi_val > 70:
                signals.append("⚠️ RSI超买，注意回调风险")
            elif rsi_val < 30:
                signals.append("💡 RSI超卖，可能反弹机会")
        except:
            pass

    # KDJ信号
    kdj_k = data.get('KDJ_K')
    kdj_d = data.get('KDJ_D')
    if kdj_k and kdj_d:
        try:
            k_val = float(kdj_k)
            d_val = float(kdj_d)
            if k_val > d_val and k_val > 50:
                signals.append("📈 KDJ金叉向上，买入信号")
            elif k_val < d_val and k_val < 50:
                signals.append("📉 KDJ死叉向下，卖出信号")
        except:
            pass

    return '\n'.join([f"  {signal}" for signal in signals]) if signals else "暂无明确信号"

@mcp_server.tool()
async def financial_smart_query(
    query: str,
    query_type: str = "auto"
) -> str:
    """
    智能金融查询（增强版）

    Args:
        query: 自然语言查询，如 "查询贵州茅台的实时行情"
        query_type: 查询类型，auto为自动检测

    Returns:
        查询结果
    """
    try:
        # 提取股票代码
        codes = financial_tool.extract_stock_codes(query)
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码或股票名称"

        # 检查是否是VWAP查询
        if 'VWAP' in query.upper() or '成交量加权平均价' in query:
            if '历史' in query or '走势' in query:
                return await get_vwap_data(codes=codes, query_type="history")
            else:
                return await get_vwap_data(codes=codes, query_type="real_time")

        # 检查是否是多周期MA查询
        ma_periods = extract_ma_periods(query)
        if ma_periods:
            indicators = f"MACD,RSI,KDJ,{','.join([f'MA{p}' for p in ma_periods])}"
            return await analyze_technical_indicators(query=query, codes=codes, indicators=indicators)

        # 自动检测查询类型
        if query_type == "auto":
            query_type = financial_tool.detect_query_type(query)

        # 记录执行日志
        financial_tool.log_execution("智能查询", {
            "query": query,
            "detected_type": query_type,
            "codes": codes
        })

        # 根据查询类型调用相应功能
        if query_type == "technical_indicators":
            return await analyze_technical_indicators(query=query, codes=codes)
        elif query_type == "history":
            # 默认查询最近1个月的历史数据
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            return await get_stock_history(codes=codes, start_date=start_date, end_date=end_date)
        elif query_type == "financial_report":
            return await get_financial_report(codes=codes)
        else:
            # 默认返回实时行情
            return await get_stock_real_time(codes=codes)

    except Exception as e:
        return financial_tool.handle_error(e)

# 创建FastAPI应用
app = FastAPI(title="Enhanced Financial MCP Server", version="1.1.0")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        client = get_api_client()
        health_status = client.health_check()
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.1.0",
            "features": [
                "实时行情查询",
                "历史数据分析",
                "技术指标计算",
                "VWAP分析",
                "多周期MA",
                "智能查询识别"
            ],
            "api_status": health_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

# 集成MCP服务器到FastAPI
app.mount("/mcp", mcp_server.create_app())

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("IFIND_REFRESH_TOKEN"):
        logger.warning("未设置IFIND_REFRESH_TOKEN环境变量，请在.env文件中配置")

    logger.info("🚀 启动增强版金融分析MCP服务器...")
    logger.info("📡 SSE端点: http://localhost:8080/mcp/sse")
    logger.info("🏥 健康检查: http://localhost:8080/health")
    logger.info("✨ 新功能: VWAP分析、多周期MA、增强交易信号")

    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
