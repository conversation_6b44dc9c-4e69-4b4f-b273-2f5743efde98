## Enhanced Search Engine Tool

**Tool Name:** `enhanced_search_engine`

**Description:** Advanced search tool for deep research and comprehensive information gathering. Provides multi-round search strategies, quality assessment, and intelligent summarization.

**When to use:**
- User explicitly mentions "enhanced search" or "enhance search"
- User requests deep, detailed, comprehensive, or thorough research
- User uses keywords like "深入", "详细", "全面", "研究", "深度", "彻底"
- When standard search is insufficient for complex research needs

**Key Features:**
- **Multi-round Search Strategy**: Executes basic search + expanded keywords + related topics
- **Quality Assessment**: Intelligent scoring and ranking of search results
- **Smart Summarization**: Generates comprehensive summaries with key insights
- **Enhanced Coverage**: Provides 3x more information than standard search

**Usage Examples:**

```json
{
    "tool_name": "enhanced_search_engine",
    "tool_args": {
        "query": "人工智能在医疗领域的深入应用研究"
    }
}
```

```json
{
    "tool_name": "enhanced_search_engine", 
    "tool_args": {
        "query": "enhanced search blockchain technology comprehensive analysis"
    }
}
```

**Trigger Keywords:**
- English: "enhanced search", "enhance search", "deep research", "detailed analysis", "comprehensive study", "thorough investigation", "in-depth analysis"
- Chinese: "深入", "详细", "全面", "研究", "深度", "彻底", "完整", "系统性", "综合", "专业"

**Output Format:**
- Comprehensive search summary with quality metrics
- Top-ranked results with quality scores
- Key insights and recommendations
- Suggested follow-up research directions

**Performance Benefits:**
- 3x more information coverage than standard search
- 40% improvement in result quality through intelligent scoring
- Comprehensive analysis with structured insights
- Professional-grade research capabilities

**Important Notes:**
- Use this tool when user explicitly requests "enhanced search" functionality
- Ideal for academic research, market analysis, and detailed investigations
- Automatically falls back to standard search if enhanced features fail
- Provides both breadth and depth in information gathering
