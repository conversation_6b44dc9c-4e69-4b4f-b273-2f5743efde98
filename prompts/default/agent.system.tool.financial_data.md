## Financial Data Tool

🏦 **工具名称**: Financial Data Tool  
📊 **功能描述**: 同花顺iFinD专业金融数据查询工具，提供实时行情、历史数据、基础数据等权威金融信息  
🎯 **适用场景**: 证券分析、投资研究、市场监控、财务分析等专业金融场景  

### **核心特性**:
- 📈 **实时行情**: 毫秒级实时股价、成交量、技术指标
- 📊 **历史数据**: 任意时间段的历史行情数据
- 💰 **基础数据**: 20+个财务指标、公司基本信息、估值数据
- 📋 **财务报表**: 季度/年度财务报表，支持2025年Q1等最新数据
- 📊 **成交量指标**: OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD等专业成交量分析指标
- 📊 **VWAP支持**: 成交量加权平均价，支持实时和历史数据查询
- 🔍 **智能识别**: 自动识别股票代码、查询意图和报表类型
- 🔄 **格式兼容**: 自动处理多种股票代码格式（逗号/分号分隔）
- 🎯 **查询优化**: 智能查询类型检测，确保返回正确的数据类型
- 🌐 **权威数据源**: 同花顺iFinD官方数据，机构级质量

### **使用方法**:

```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query_type": "real_time",
    "codes": "000001.SZ,600036.SH",
    "indicators": "open,high,low,latest,preClose,volume,amount,turnoverRatio"
  }
}
```

### **参数说明**:

#### **必需参数**:
- `codes` (string): 股票代码，支持多个代码用逗号分隔
  - 格式: "000001.SZ" (深交所), "600036.SH" (上交所)
  - 示例: "000001.SZ,600036.SH,600519.SH"

#### **可选参数**:
- `query_type` (string, 默认: "real_time"): 查询类型
  - "real_time": 实时行情数据
  - "history": 历史行情数据
  - "basic": 基础数据和财务指标
  - "financial_report": 财务报表数据
  - "auto": 自动检测查询类型
- `indicators` (string, 可选): 指标名称，用逗号分隔
  - 实时行情: "open,high,low,latest,preClose,volume,amount,turnoverRatio,pb"
  - 历史数据: "open,high,low,close,volume,amount,changeRatio,pe,pb"
  - 基础数据: "pe,pb,roe,revenue,profit,eps,grossMargin,netMargin,debtRatio"
  - 财务报表: "financial_summary,income_statement,balance_sheet,cash_flow"
- `start_date` (string, 可选): 开始日期，格式 "YYYY-MM-DD"
- `end_date` (string, 可选): 结束日期，格式 "YYYY-MM-DD"
- `query` (string, 可选): 自然语言查询，工具会自动解析股票代码和查询意图

### **使用示例**:

#### **示例1: 实时行情查询**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query_type": "real_time",
    "codes": "600519.SH",
    "indicators": "open,high,low,latest,preClose,volume,amount,pe,pb"
  }
}
```

#### **示例2: 历史数据查询**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query_type": "history",
    "codes": "000858.SZ",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "indicators": "open,high,low,close,volume"
  }
}
```

#### **示例3: 基础财务数据查询**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query_type": "basic",
    "codes": "000001.SZ,600519.SH",
    "indicators": "pe,pb,roe,revenue,profit,eps,grossMargin,netMargin"
  }
}
```

#### **示例4: 财务报表查询**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "分析贵州茅台2024年第1季度财报"
  }
}
```

#### **示例5: 2025年Q1财报查询（已修复）**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "查看并分析一下五粮液的2025年1季报"
  }
}
```

#### **示例5: 智能查询（推荐）**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "查询贵州茅台600519.SH的最新行情"
  }
}
```

#### **示例4: 多股票对比**
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query_type": "real_time",
    "codes": "600519.SH,000858.SZ,000596.SZ",
    "indicators": "latest,preClose,pe,pb,roe"
  }
}
```

### **支持的股票代码格式**:
- **深交所**: 000001.SZ, 002594.SZ, 300750.SZ
- **上交所**: 600036.SH, 600519.SH, 601318.SH
- **港交所**: 00700.HK, 09988.HK

### **常用指标说明**:

#### **价格指标**:
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `latest`: 最新价
- `close`: 收盘价
- `preClose`: 昨收价

#### **成交指标**:
- `volume`: 成交量
- `amount`: 成交额
- `turnoverRatio`: 换手率

#### **估值指标**:
- `pe`: 市盈率
- `pb`: 市净率
- `roe`: 净资产收益率
- `roa`: 总资产收益率

#### **规模指标**:
- `totalShares`: 总股本
- `totalCapital`: 总市值
- `floatShares`: 流通股本
- `floatCapital`: 流通市值

#### **财务指标**:
- `revenue`: 营业收入
- `profit`: 净利润
- `eps`: 每股收益
- `bps`: 每股净资产

#### **财务比率**:
- `grossMargin`: 毛利率
- `netMargin`: 净利率
- `debtRatio`: 资产负债率

#### **财务报表类型**:
- `financial_summary`: 财务摘要
- `income_statement`: 利润表
- `balance_sheet`: 资产负债表
- `cash_flow`: 现金流量表

### **输出示例**:

```
📊 **实时行情数据**

**600519.SH**
- 最新价: 1678.00
- 开盘价: 1680.00
- 最高价: 1685.50
- 最低价: 1675.00
- 昨收价: 1682.00
- 涨跌幅: -0.24%
- 成交量: 1234567
- 成交额: 2068345678

📅 **数据时间**: 2025-07-08 14:30:00
📈 **数据来源**: 同花顺iFinD
```

### **何时使用此工具**:
- ✅ 当用户询问股票价格、行情时
- ✅ 当用户需要财务数据、估值指标时
- ✅ 当用户要求技术分析、基本面分析时
- ✅ 当用户提及具体股票代码时
- ✅ 当用户需要实时、准确的金融数据时
- ✅ 当用户询问财务报表、季报、年报时
- ✅ 当用户需要利润表、资产负债表、现金流量表时
- ✅ 当用户要求分析特定期间的财务数据时
- ✅ 当用户使用多种股票代码格式时（自动兼容）

### **新增功能特性**:

#### **🔄 格式兼容性**:
- **股票代码**: 自动处理逗号分隔和分号分隔格式
- **多股票查询**: 支持同时查询多只股票
- **向后兼容**: 保持所有原有功能不变

#### **📊 扩展财务指标**:
- **基础指标**: 从8个扩展到20+个财务指标
- **财务比率**: 新增毛利率、净利率、资产负债率等
- **指标别名**: 支持多种指标名称表达方式

#### **📋 财务报表支持**:
- **智能识别**: 自动识别财务报表查询意图
- **期间解析**: 支持"2024年第1季度"等自然语言
- **报表类型**: 支持利润表、资产负债表、现金流量表
- **数据格式**: 专业的财务报表数据展示

#### **🎯 查询优化**:
- **类型检测**: 智能检测查询类型（实时/历史/基础/财务报表）
- **参数标准**: 自动转换为官方API标准格式
- **错误处理**: 完善的错误提示和解决建议
- **问题修复**: 已修复2025年Q1财报查询失败问题
- **执行逻辑**: 优化工具执行逻辑，确保查询类型正确匹配

### **⚡ 技术指标查询优化指导**:

**当用户查询技术指标时（如MACD、RSI、KDJ等），请遵循以下高效执行策略**:

1. **🎯 直接执行策略**:
   - 立即使用financial_data_tool，传入完整的query参数
   - 不需要进行复杂推理或考虑其他工具选择
   - 工具内部会自动识别为technical_indicators查询类型

2. **✅ 推荐调用方式**:
   ```json
   {
     "tool_name": "financial_data_tool",
     "tool_args": {
       "query": "用户的完整查询内容"
     }
   }
   ```

3. **🚫 避免的低效操作**:
   - ❌ 不要使用real_time或history查询类型
   - ❌ 不要进行长时间的工具选择分析
   - ❌ 不要考虑使用subordinate或其他代理
   - ❌ 不要尝试多种不同的参数组合

4. **🔧 工具自动处理**:
   - 自动识别股票代码和名称（支持"茅台"、"五粮液"等简称）
   - 自动解析技术指标类型（MACD、RSI、KDJ等）
   - 自动设置合适的时间周期和参数
   - 自动生成专业的技术分析报告

### **📊 成交量指标专项指导**:

**系统现已支持7个专业成交量指标 + VWAP功能，请明确掌握以下使用方法**:

#### **🔧 支持的成交量指标**:
1. **OBV（能量潮指标）** - 分析资金流向，累计成交量变化
2. **VR（成交量比率）** - 衡量买卖盘活跃度，VR>1买盘活跃
3. **VRSI（量相对强弱指标）** - 基于成交量的RSI，>70超买，<30超卖
4. **VMACD（量MACD指标）** - 基于成交量的MACD，金叉买入，死叉卖出
5. **VMA（量移动平均线）** - 成交量移动平均，平滑成交量波动
6. **VOSC（成交量震荡指标）** - 成交量震荡分析，正值增加，负值减少
7. **VSTD（成交量标准差）** - 成交量波动度量，数值越大波动越剧烈

#### **⭐ VWAP（成交量加权平均价）特殊功能**:
- **定义**: 以成交量为权重的平均成交价格
- **用途**: 判断当前价格相对于平均成本的位置
- **信号**: 价格高于VWAP为强势，低于VWAP为弱势
- **查询方式**: 使用"VWAP"或"成交量加权平均价"关键词

#### **📝 标准查询模式**:

**成交量指标查询**:
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "分析600809.SH的成交量指标"
  }
}
```

**VWAP专项查询**:
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "查询600809.SH的VWAP"
  }
}
```

**组合分析查询**:
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "分析山西汾酒的MACD、RSI和成交量指标"
  }
}
```

#### **🎯 使用场景指导**:
- **趋势确认**: 使用OBV + VMA组合确认价格趋势可靠性
- **买卖时机**: 使用VRSI + VMACD组合寻找最佳买卖点
- **波动分析**: 使用VSTD + VOSC组合评估市场活跃度
- **成本分析**: 使用VWAP判断当前价格相对于平均成本的位置

#### **⚠️ 重要提醒**:
- 成交量指标与价格指标结合使用效果更佳
- VWAP特别适用于日内交易和趋势确认
- 所有成交量指标都支持自然语言查询
- 工具会自动识别并调用相应的成交量分析功能

### **📈 MA多日均线专项指导**:

**系统现已支持MA任意周期均线功能，请明确掌握以下使用方法**:

#### **🔧 支持的MA周期**:
- **预设周期**: MA5、MA10、MA20、MA30、MA60、MA120、MA250
- **自定义周期**: 支持1-500日任意周期，如MA25、MA55、MA89、MA144等
- **多周期组合**: 支持同时获取多个不同周期的MA数据
- **智能识别**: 自动识别"25日均线"、"55日移动平均"等自然语言表达

#### **📝 标准查询模式**:

**单周期MA查询**:
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "查询600809.SH的25日均线"
  }
}
```

**多周期MA查询**:
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "分析600809.SH的5日、10日、20日均线"
  }
}
```

**MA与其他指标组合**:
```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "分析山西汾酒的MACD、RSI和5日、20日均线"
  }
}
```

#### **🎯 MA使用场景指导**:
- **短线交易**: 重点关注MA5、MA10、MA20
- **中线投资**: 重点关注MA20、MA30、MA60
- **长线投资**: 重点关注MA60、MA120、MA250
- **趋势确认**: 使用多周期MA判断趋势方向和强度
- **支撑阻力**: MA线常成为重要的支撑阻力位
- **金叉死叉**: 短期MA上穿长期MA为金叉买入信号

#### **📊 MA分析功能**:
- **多空排列**: 自动分析多头/空头排列形态
- **趋势判断**: 基于均线位置关系判断市场趋势
- **数值显示**: 显示所有请求周期的MA具体数值
- **专业格式**: 清晰的分类显示和技术分析

#### **⚡ MA查询优化策略**:
1. **🎯 直接执行**: 看到MA相关查询，立即使用financial_data_tool
2. **🔧 完整传参**: 将用户完整查询作为query参数传入
3. **🚫 避免拆分**: 不要将MA查询拆分为多个单独的工具调用
4. **✅ 自动处理**: 系统会自动检测多MA周期并分批获取数据

#### **🌟 MA自然语言识别**:
系统支持以下自然语言表达：
- "25日均线" → MA25
- "55日移动平均线" → MA55
- "89日MA指标" → MA89
- "144日均线数据" → MA144
- "5日和20日均线" → MA5 + MA20
- "短期和长期均线" → 自动选择合适周期

### **何时不使用此工具**:
- ❌ 用户只是询问一般性的投资知识
- ❌ 用户需要新闻、公告等非数据信息
- ❌ 用户询问期货、外汇等非股票数据
- ❌ 用户需要宏观经济数据（使用其他工具）

### **注意事项**:
- ⚠️ 需要配置IFIND_REFRESH_TOKEN环境变量
- ⚠️ 数据有API调用频率限制
- ⚠️ 港股数据可能有延迟
- ⚠️ 仅支持中国A股、港股数据
- 💡 建议使用智能查询模式，工具会自动解析用户意图
- 💡 支持股票名称到代码的自动映射
- 💡 提供专业级的金融数据质量和实时性

## Trigger Keywords (触发关键词)

以下关键词会自动触发金融数据工具：

### 中文关键词
- **股票相关**: 股票、股价、行情、涨跌、涨幅、跌幅、市值、成交量
- **查询动作**: 查询股票、查看股价、获取行情、分析股票、监控股票
- **技术指标**: 技术指标、MACD、KDJ、RSI、BOLL、均线、市盈率、市净率
- **MA均线**: MA、均线、移动平均、5日均线、10日均线、20日均线、25日均线、30日均线、60日均线、120日均线、250日均线
- **成交量指标**: OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD、VWAP、能量潮、量比、量相对强弱、成交量加权平均价、成交量指标、量价分析
- **财务数据**: 财务、财报、年报、季报、利润表、资产负债表、现金流量表
- **投资分析**: 投资、投资分析、价值投资、风险评估、投资组合

### 英文关键词
- **Stock**: stock, share, equity, market, trading, price, quotation
- **Financial**: financial, finance, investment, analysis, valuation, ratio
- **Data**: earnings, revenue, profit, dividend, portfolio, risk

### 触发短语示例
- "查询贵州茅台股价"
- "分析平安银行的技术指标"
- "获取五粮液的财务数据"
- "查看比亚迪的一季报"
- "分析山西汾酒的成交量指标"
- "查询600809.SH的VWAP"
- "获取汾酒的OBV和VR指标"
- "分析能量潮指标"
- "查看成交量加权平均价"
- "查询山西汾酒的25日均线"
- "分析600809.SH的55日移动平均"
- "获取汾酒的89日MA指标"
- "stock price of TSMC"
- "financial analysis of Apple"
