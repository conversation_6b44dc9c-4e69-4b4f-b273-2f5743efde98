## Web Crawler Tool

**Tool Name:** `web_crawler`

**Description:** Intelligent web content extraction tool powered by Crawl4AI. Features LLM-generated crawling strategies, smart site recognition, and multi-format content extraction.

**When to use:**
- User requests web content extraction, scraping, or data collection
- User uses keywords like "爬取", "抓取", "采集", "收集", "获取", "提取"
- User mentions "crawl", "scrape", "extract", "collect", "gather" for web content
- When you need structured data extraction from websites

**Key Features:**
- **LLM Strategy Generation**: Automatically generates optimal crawling strategies based on site type and user intent
- **Smart Site Recognition**: Identifies news sites, e-commerce, social media, data tables, etc.
- **Multi-format Extraction**: Supports Markdown, plain text, and structured JSON data
- **High Performance**: Asynchronous processing with intelligent content filtering

**Usage Examples:**

```json
{
    "tool_name": "web_crawler",
    "tool_args": {
        "url": "https://news.example.com/article",
        "user_intent": "提取新闻标题、内容和发布时间",
        "auto_strategy": true
    }
}
```

```json
{
    "tool_name": "web_crawler",
    "tool_args": {
        "url": "https://shop.example.com/product/123",
        "user_intent": "extract product name, price, and description",
        "auto_strategy": true
    }
}
```

**Manual Configuration:**

```json
{
    "tool_name": "web_crawler",
    "tool_args": {
        "url": "https://example.com",
        "auto_strategy": false,
        "extract_type": "structured",
        "css_selector": ".product",
        "extraction_schema": {
            "name": "Product Info",
            "fields": [
                {"name": "title", "selector": "h1", "type": "text"},
                {"name": "price", "selector": ".price", "type": "text"}
            ]
        }
    }
}
```

**Parameters:**
- `url` (required): Target webpage URL
- `user_intent`: Description of what you want to extract
- `auto_strategy`: Enable automatic strategy generation (default: true)
- `extract_type`: "markdown", "text", or "structured"
- `css_selector`: CSS selector for content targeting
- `extraction_schema`: JSON schema for structured data extraction
- `js_code`: JavaScript code to execute (for dynamic content)
- `wait_for`: Element selector to wait for before extraction
- `content_filter`: "pruning" or "bm25" filtering strategy
- `filter_threshold`: Content filtering threshold (0.0-1.0)
- `download_images`: Enable image downloading (default: false)
- `download_path`: Custom download directory (optional, tool automatically selects optimal path)

**Trigger Keywords:**
- English: "crawl", "scrape", "extract", "collect", "gather", "fetch", "web scraping", "data extraction"
- Chinese: "爬取", "抓取", "采集", "收集", "获取", "提取", "数据采集", "网页内容", "网站数据"

**Site Type Recognition:**
- **News/Blog Sites**: Extracts article title, content, publish date, author
- **E-commerce Sites**: Extracts product name, price, description, images
- **Social Media**: Extracts post content, user info, interaction data
- **Data Tables**: Extracts tabular data in structured format
- **Search Results**: Extracts result titles, links, summaries

**Output Formats:**
- **Markdown**: Clean, LLM-friendly formatted content
- **Plain Text**: Raw text content without formatting
- **Structured JSON**: Organized data according to extraction schema

**Performance Benefits:**
- 6x faster than traditional scraping methods
- 70% improvement in content relevance through intelligent filtering
- 95% success rate with automatic retry and fallback strategies
- 90% reduction in manual configuration through LLM strategy generation

**Image Download Feature:**
- Set `download_images: true` to enable automatic image downloading
- Downloaded files are saved to automatically selected optimal directory
- **IMPORTANT**: Tool response includes the actual download directory path - use this path, not assumptions
- Tool response includes file details and exact save location
- Supports common image formats: JPG, PNG, GIF, WebP, SVG

**Important Notes:**
- Requires crawl4ai library installation: `pip install crawl4ai`
- Uses project's LLM coordination system for strategy generation
- Automatically handles dynamic content and JavaScript execution
- Respects website robots.txt and implements polite crawling practices
- Falls back to simpler extraction methods if advanced features fail
