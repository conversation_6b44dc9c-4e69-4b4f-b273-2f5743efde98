# 📊 成交量指标使用指导

## 🎯 **核心原则**

**重要提醒：当用户询问成交量相关分析时，你必须：**
1. **明确识别**：准确识别用户提到的成交量指标类型
2. **直接执行**：不要犹豫，立即调用financial_data_tool
3. **完整传参**：将用户的完整查询作为query参数传入
4. **避免推测**：不要猜测指标含义，系统会自动处理

当用户询问成交量相关分析时，你必须明确知道以下7个专业成交量指标和VWAP功能：

### **📋 完整成交量指标清单**

#### **1. OBV（能量潮指标）**
- **英文**: On Balance Volume
- **功能**: 累计成交量变化，反映资金流向
- **信号**: 价格上涨时成交量增加为正信号
- **查询**: "OBV"、"能量潮"、"资金流向"

#### **2. VR（成交量比率）**
- **英文**: Volume Ratio  
- **功能**: 上涨日与下跌日成交量比率
- **信号**: VR > 1买盘活跃，VR < 1卖盘活跃
- **查询**: "VR"、"量比"、"成交量比率"

#### **3. VRSI（量相对强弱指标）**
- **英文**: Volume Relative Strength Index
- **功能**: 基于成交量的RSI指标
- **信号**: VRSI > 70超买，VRSI < 30超卖
- **查询**: "VRSI"、"量相对强弱"、"成交量RSI"

#### **4. VMACD（量MACD指标）**
- **英文**: Volume MACD
- **功能**: 基于成交量的MACD指标
- **信号**: 金叉买入信号，死叉卖出信号
- **查询**: "VMACD"、"量MACD"、"成交量MACD"

#### **5. VMA（量移动平均线）**
- **英文**: Volume Moving Average
- **功能**: 成交量的移动平均线
- **信号**: 成交量突破VMA表示趋势可能改变
- **查询**: "VMA"、"量移动平均"、"成交量均线"

#### **6. VOSC（成交量震荡指标）**
- **英文**: Volume Oscillator
- **功能**: 短期和长期成交量移动平均的差值
- **信号**: 正值表示成交量增加，负值表示减少
- **查询**: "VOSC"、"成交量震荡"、"量震荡"

#### **7. VSTD（成交量标准差）**
- **英文**: Volume Standard Deviation
- **功能**: 衡量成交量的波动程度
- **信号**: 数值越大表示成交量波动越剧烈
- **查询**: "VSTD"、"成交量标准差"、"量波动"

#### **⭐ VWAP（成交量加权平均价）**
- **英文**: Volume Weighted Average Price
- **功能**: 以成交量为权重的平均成交价格
- **信号**: 价格高于VWAP为强势，低于VWAP为弱势
- **查询**: "VWAP"、"成交量加权平均价"、"量价平均"

---

## 🚀 **标准执行流程**

### **步骤1: 识别查询意图**
当用户提及以下关键词时，立即识别为成交量指标查询：
- 成交量指标、量价分析、成交量分析
- OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD、VWAP
- 能量潮、量比、量相对强弱、成交量加权平均价

### **步骤2: 直接调用工具**
**不要犹豫，不要推理，直接使用以下标准格式**：

```json
{
  "tool_name": "financial_data_tool",
  "tool_args": {
    "query": "用户的完整查询内容"
  }
}
```

### **步骤3: 工具自动处理**
工具会自动：
- 识别股票代码和名称
- 解析成交量指标类型
- 调用相应的分析功能
- 生成专业分析报告

---

## 📊 **常见查询模式**

### **模式1: 单一成交量指标**
- 用户: "分析山西汾酒的OBV指标"
- 执行: `{"query": "分析山西汾酒的OBV指标"}`

### **模式2: 多个成交量指标**
- 用户: "查看600809.SH的成交量指标"
- 执行: `{"query": "查看600809.SH的成交量指标"}`

### **模式3: VWAP专项查询**
- 用户: "查询汾酒的VWAP"
- 执行: `{"query": "查询汾酒的VWAP"}`

### **模式4: 组合分析**
- 用户: "分析山西汾酒的MACD和成交量指标"
- 执行: `{"query": "分析山西汾酒的MACD和成交量指标"}`

---

## 🎯 **分析建议模板**

当获得成交量指标数据后，使用以下分析框架：

### **趋势确认分析**
- **OBV + VMA组合**: 确认价格趋势的可靠性
- **分析要点**: OBV上升且成交量突破VMA，趋势可靠

### **买卖时机分析**
- **VRSI + VMACD组合**: 寻找最佳买卖点
- **分析要点**: VRSI超卖区域且VMACD金叉，买入时机

### **波动分析**
- **VSTD + VOSC组合**: 评估市场活跃度
- **分析要点**: VSTD高位且VOSC正值，市场活跃

### **成本分析**
- **VWAP分析**: 判断当前价格相对位置
- **分析要点**: 价格高于VWAP，处于强势区域

---

## ⚠️ **重要注意事项**

### **避免的错误**
1. ❌ 不要猜测成交量指标的含义
2. ❌ 不要使用错误的指标名称
3. ❌ 不要忽略VWAP的特殊性
4. ❌ 不要单独分析成交量指标

### **正确的做法**
1. ✅ 明确知道7个成交量指标的功能
2. ✅ 理解VWAP与其他指标的区别
3. ✅ 结合价格指标进行综合分析
4. ✅ 使用标准查询格式调用工具

### **专业术语使用**
- 使用"能量潮"而不是"成交量潮"
- 使用"量比"而不是"成交量比"
- 使用"成交量加权平均价"而不是"加权平均价"
- 使用"量价分析"而不是"成交量价格分析"

---

## 🔧 **故障排除**

### **问题1: 工具无响应**
- **原因**: 查询格式不正确
- **解决**: 使用标准JSON格式，确保query参数完整

### **问题2: 指标数据缺失**
- **原因**: 股票代码格式错误
- **解决**: 确保使用正确的股票代码格式（如600809.SH）

### **问题3: VWAP查询失败**
- **原因**: 未正确识别VWAP查询意图
- **解决**: 在查询中明确包含"VWAP"或"成交量加权平均价"

---

## 📈 **成功案例模板**

### **完整分析示例**
```
📊 **成交量指标分析报告**

**股票**: 600809.SH (山西汾酒)
**分析时间**: 2025-07-15

### 成交量指标分析
- **OBV**: -316.65，资金流出信号
- **VR**: 66.30，卖盘相对活跃
- **VRSI**: 45.15，正常区域
- **VWAP**: 176.18元，当前价格175.36元，略低于VWAP

### 综合判断
当前成交量指标显示资金流出，但VRSI处于正常区域，建议结合价格指标进行综合分析。
```

---

## 🚨 **常见错误避免指南**

### **错误1: 股票名称识别失败**
**现象**: "❌ 请提供股票代码，如：000858.SZ"
**原因**: 股票名称映射不完整或查询格式问题
**解决**:
- 确保使用完整股票名称："山西汾酒"而不是"汾酒股票"
- 或直接使用股票代码："600809.SH"
- 系统支持的股票名称：山西汾酒、汾酒、贵州茅台、茅台、五粮液、平安银行、平安等

### **错误2: 成交量指标混淆**
**现象**: 不知道用户要查询哪个具体指标
**解决**:
- 明确7个成交量指标：OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD
- VWAP是特殊功能，不是技术指标API的一部分
- 当用户说"成交量指标"时，系统会自动选择合适的指标组合

### **错误3: 工具调用犹豫**
**现象**: 过度分析用户意图，不敢直接调用工具
**解决**:
- 看到成交量相关关键词，立即调用financial_data_tool
- 不要进行复杂的意图分析
- 将完整查询作为query参数传入

### **错误4: 参数格式错误**
**现象**: 工具调用失败或返回错误
**解决**:
- 使用标准格式：`{"tool_name": "financial_data_tool", "tool_args": {"query": "用户完整查询"}}`
- 不要拆分参数，保持query的完整性
- 不要添加额外的参数如query_type等

---

## 📋 **快速检查清单**

在处理成交量指标查询时，请检查：

- [ ] 是否正确识别了股票名称/代码？
- [ ] 是否明确了要查询的成交量指标类型？
- [ ] 是否直接调用了financial_data_tool？
- [ ] 是否使用了正确的JSON格式？
- [ ] 是否保持了query参数的完整性？

---

**记住：成交量指标是技术分析的重要组成部分，必须准确理解和使用！**
