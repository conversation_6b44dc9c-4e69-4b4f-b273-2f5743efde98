### code_execution_tool

execute terminal commands python nodejs code for computation or software tasks
place code in "code" arg; escape carefully and indent properly
select "runtime" arg: "terminal" "python" "nodejs" "output" "reset"
select "session" number, 0 default, others for multitasking
if code runs long, use "output" to wait, "reset" to kill process
use "pip" "npm" "apt-get" in "terminal" to install packages
to output, use print() or console.log()
if tool outputs error, adjust code before retrying; knowledge_tool can help
important: check code for placeholders or demo data; replace with real variables; don't reuse snippets
don't use with other tools except thoughts; wait for response before using others
check dependencies before running code
output may end with [SYSTEM: ...] information comming from framework, not terminal

**IMPORTANT FILE PATH GUIDELINES:**
- Current working directory: /mnt/e/AI/agent-zero_091
- For file operations, use these safe paths:
  - work_dir: ./work_dir/ (project working directory)
  - reports: ./work_dir/reports/ (for generated reports)
  - temp files: /tmp/ (temporary files)
  - user home: /home/<USER>/ (user directory)
- NEVER use /root/ paths (permission denied in WSL)
- Always create directories with mkdir -p before writing files
- Use relative paths starting with ./ when possible

**FILE WRITING PERFORMANCE TIPS:**
- For large files (>1000 lines), use efficient writing methods
- Add print statements to show progress during file operations
- Use buffered writing for large content: with open(file, 'w', buffering=8192)
- For HTML files, consider writing in chunks or using templates
- Always print file path and size after successful creation
usage:

1 execute python code

~~~json
{
    "thoughts": [
        "Need to do...",
        "I can use...",
        "Then I can...",
    ],
    "headline": "Executing Python code to check current directory",
    "tool_name": "code_execution_tool",
    "tool_args": {
        "runtime": "python",
        "session": 0,
        "code": "import os\nprint(os.getcwd())",
    }
}
~~~

2 execute terminal command
~~~json
{
    "thoughts": [
        "Need to do...",
        "Need to install...",
    ],
    "headline": "Installing zip package via terminal",
    "tool_name": "code_execution_tool",
    "tool_args": {
        "runtime": "terminal",
        "session": 0,
        "code": "apt-get install zip",
    }
}
~~~

2.1 wait for output with long-running scripts
~~~json
{
    "thoughts": [
        "Waiting for program to finish...",
    ],
    "headline": "Waiting for long-running program to complete",
    "tool_name": "code_execution_tool",
    "tool_args": {
        "runtime": "output",
        "session": 0,
    }
}
~~~

2.2 create and write files (use correct paths and efficient methods)
~~~json
{
    "thoughts": [
        "Need to create a report file...",
        "Will use work_dir for safe file operations...",
        "For large files, will use efficient writing with progress indicators..."
    ],
    "headline": "Creating report file in work directory",
    "tool_name": "code_execution_tool",
    "tool_args": {
        "runtime": "python",
        "session": 0,
        "code": "import os\nfrom datetime import datetime\n\nprint('Starting file creation...')\n\n# Create directory if needed\nos.makedirs('./work_dir/reports', exist_ok=True)\nprint('Directory created/verified')\n\n# Prepare content efficiently\ncontent = f'<html><body><h1>Report</h1><p>Generated: {datetime.now()}</p></body></html>'\nprint(f'Content prepared ({len(content)} characters)')\n\n# Write file to safe location with buffering\nfile_path = './work_dir/reports/my_report.html'\nwith open(file_path, 'w', encoding='utf-8', buffering=8192) as f:\n    f.write(content)\n    f.flush()  # Ensure content is written\n\nprint(f'✅ File created: {os.path.abspath(file_path)}')\nprint(f'   File size: {os.path.getsize(file_path)} bytes')"
    }
}
~~~

2.3 reset terminal
~~~json
{
    "thoughts": [
        "code_execution_tool not responding...",
    ],
    "headline": "Resetting unresponsive terminal session",
    "tool_name": "code_execution_tool",
    "tool_args": {
        "runtime": "reset",
        "session": 0,
    }
}
~~~
