# Agent-Zero 扩展开发快速参考

## 🚀 快速开始

### **1. 创建基础扩展**
```python
# python/extensions/message_loop_prompts_after/_80_my_extension.py
from python.helpers.extension import Extension

class MyExtension(Extension):
    async def execute(self, loop_data, **kwargs):
        try:
            # 获取用户输入
            user_input = loop_data.user_message.message if loop_data.user_message else ""
            
            # 处理逻辑
            result = f"处理结果: {user_input}"
            
            # 添加到系统提示
            loop_data.system.append(result)
            
        except Exception as e:
            self.agent.context.log.log("warning", f"扩展失败: {e}")
```

### **2. 创建工具扩展**
```python
# python/tools/my_tool.py
from python.helpers.tool import Tool, Response

class MyTool(Tool):
    async def execute(self, **kwargs):
        param = kwargs.get('param', '')
        result = f"工具处理: {param}"
        return Response(message=result, data=result)
```

### **3. 注册工具**
```markdown
<!-- prompts/default/agent.system.tool.my_tool.md -->
# 我的工具 (my_tool)
## 参数
- `param`: 输入参数
## 触发关键词
- 我的工具, 自定义处理
```

```markdown
<!-- prompts/default/agent.system.tools.md -->
{{ include './agent.system.tool.my_tool.md' }}
```

## 📁 扩展类型和位置

| 扩展类型 | 目录 | 执行时机 | 用途 |
|---------|------|----------|------|
| **系统提示** | `system_prompt/` | 构建系统提示时 | 修改系统提示内容 |
| **消息前处理** | `message_loop_prompts_before/` | 消息处理前 | 预处理用户输入 |
| **消息后处理** | `message_loop_prompts_after/` | 消息处理后 | 增强系统提示 |
| **响应流处理** | `response_stream/` | 响应生成时 | 处理实时响应 |

## 🔧 常用代码片段

### **获取用户输入**
```python
def _get_user_input(self, loop_data):
    # 方法1: 从loop_data获取
    if loop_data.user_message and loop_data.user_message.message:
        return loop_data.user_message.message.strip()
    
    # 方法2: 从agent获取
    if hasattr(self.agent, 'last_user_message'):
        return self.agent.last_user_message.strip()
    
    # 方法3: 从历史获取
    if hasattr(self.agent, 'history'):
        for msg in reversed(self.agent.history.messages):
            if not msg.ai and msg.content:
                return msg.content.strip()
    
    return None
```

### **添加系统提示**
```python
# 添加简单文本
loop_data.system.append("提示内容")

# 添加格式化内容
content = f"""
## 标题
- 项目1: {value1}
- 项目2: {value2}
"""
loop_data.system.append(content)
```

### **错误处理模板**
```python
async def execute(self, **kwargs):
    try:
        # 扩展逻辑
        pass
    except Exception as e:
        self.agent.context.log.log(
            type="warning",
            content=f"{self.__class__.__name__} 失败: {e}"
        )
```

### **条件执行**
```python
async def execute(self, loop_data, **kwargs):
    # 检查条件
    if not self._should_execute(loop_data):
        return
    
    # 执行逻辑
    await self._do_work(loop_data)

def _should_execute(self, loop_data):
    user_input = loop_data.user_message.message if loop_data.user_message else ""
    return "关键词" in user_input
```

## 🎯 工具开发模板

### **基础工具**
```python
from python.helpers.tool import Tool, Response

class MyTool(Tool):
    def __init__(self, agent, name="my_tool", method=None, args=None, message="", **kwargs):
        super().__init__(agent, name, method, args or {}, message, **kwargs)
    
    async def execute(self, **kwargs):
        try:
            # 获取参数
            param1 = kwargs.get('param1', '')
            param2 = kwargs.get('param2', '')
            
            # 处理逻辑
            result = self._process(param1, param2)
            
            # 返回结果
            return Response(
                message=f"处理完成: {result}",
                data=result
            )
        except Exception as e:
            return Response(
                message=f"处理失败: {str(e)}",
                data=""
            )
    
    def _process(self, param1, param2):
        return f"{param1} + {param2}"
```

### **异步工具**
```python
import asyncio
import aiohttp

class AsyncTool(Tool):
    async def execute(self, **kwargs):
        url = kwargs.get('url', '')
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    data = await response.text()
                    
            return Response(
                message=f"获取数据成功: {len(data)} 字符",
                data=data
            )
        except Exception as e:
            return Response(
                message=f"获取失败: {str(e)}",
                data=""
            )
```

## 📊 调试技巧

### **日志记录**
```python
# 信息日志
self.agent.context.log.log("info", "扩展开始执行")

# 警告日志
self.agent.context.log.log("warning", "发现潜在问题")

# 错误日志
self.agent.context.log.log("error", "扩展执行失败")

# 调试输出
print(f"Debug: {variable_name} = {variable_value}")
```

### **状态检查**
```python
# 检查属性存在
if hasattr(self.agent, 'history'):
    # 使用 agent.history

# 检查方法存在
if hasattr(loop_data, 'user_message'):
    # 使用 loop_data.user_message

# 安全获取属性
value = getattr(obj, 'attr', default_value)
```

### **性能监控**
```python
import time

async def execute(self, **kwargs):
    start_time = time.time()
    
    try:
        # 扩展逻辑
        pass
    finally:
        execution_time = time.time() - start_time
        if execution_time > 1.0:  # 超过1秒记录警告
            self.agent.context.log.log(
                "warning", 
                f"扩展执行时间过长: {execution_time:.2f}s"
            )
```

## 🧪 测试模板

### **基础测试**
```python
import unittest
from unittest.mock import Mock

class TestMyExtension(unittest.TestCase):
    def setUp(self):
        self.mock_agent = Mock()
        self.mock_agent.context.log.log = Mock()
        
        self.mock_loop_data = Mock()
        self.mock_loop_data.system = []
        self.mock_loop_data.user_message = Mock()
        self.mock_loop_data.user_message.message = "测试输入"
    
    async def test_basic_functionality(self):
        from my_extension import MyExtension
        
        extension = MyExtension(self.mock_agent)
        await extension.execute(loop_data=self.mock_loop_data)
        
        # 验证结果
        self.assertTrue(len(self.mock_loop_data.system) > 0)
```

## 📋 命名规范

### **文件命名**
- 扩展文件: `_XX_extension_name.py`
- 工具文件: `tool_name.py`
- 测试文件: `test_extension_name.py`

### **类命名**
- 扩展类: `ExtensionName` (PascalCase)
- 工具类: `ToolName` (PascalCase)

### **方法命名**
- 公共方法: `method_name` (snake_case)
- 私有方法: `_method_name` (snake_case with underscore)

## ⚡ 性能优化

### **避免阻塞**
```python
# ❌ 阻塞操作
time.sleep(1)
requests.get(url)

# ✅ 异步操作
await asyncio.sleep(1)
async with aiohttp.ClientSession() as session:
    await session.get(url)
```

### **缓存结果**
```python
class CachedExtension(Extension):
    def __init__(self, agent):
        super().__init__(agent)
        self._cache = {}
    
    async def execute(self, **kwargs):
        cache_key = self._get_cache_key(kwargs)
        
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        result = await self._compute_result(kwargs)
        self._cache[cache_key] = result
        return result
```

### **限制执行频率**
```python
import time

class RateLimitedExtension(Extension):
    def __init__(self, agent):
        super().__init__(agent)
        self.last_execution = 0
        self.min_interval = 5  # 最小间隔5秒
    
    async def execute(self, **kwargs):
        now = time.time()
        if now - self.last_execution < self.min_interval:
            return  # 跳过执行
        
        self.last_execution = now
        # 执行扩展逻辑
```

## 🔒 安全检查

### **输入验证**
```python
def _validate_input(self, user_input):
    # 长度检查
    if len(user_input) > 10000:
        return False
    
    # 内容检查
    dangerous_patterns = ['<script', 'javascript:', 'data:']
    for pattern in dangerous_patterns:
        if pattern in user_input.lower():
            return False
    
    return True
```

### **权限检查**
```python
def _check_permissions(self):
    # 检查用户权限
    if not hasattr(self.agent, 'user_permissions'):
        return False
    
    required_permission = 'extension_execute'
    return required_permission in self.agent.user_permissions
```

---

**快速参考版本**: 1.0  
**最后更新**: 2025-01-13  
**配套文档**: EXTENSION_DEVELOPMENT_GUIDE.md
