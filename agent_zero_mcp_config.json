{"mcpServers": {"financial-analysis": {"type": "sse", "url": "http://localhost:8880/mcp/sse", "description": "专业金融数据分析MCP服务器，提供实时行情、技术指标分析等功能", "category": "financial", "enabled": true, "init_timeout": 10, "tool_timeout": 30, "sse_read_timeout": 300, "confidence_threshold": 0.6, "keywords": ["股票", "股价", "行情", "涨跌", "涨幅", "跌幅", "市值", "成交量", "成交额", "开盘价", "收盘价", "最高价", "最低价", "昨收价", "换手率", "振幅", "查询股票", "查询股价", "查询行情", "查看股票", "查看股价", "查看行情", "获取股票", "获取股价", "获取行情", "股票查询", "股价查询", "行情查询", "查看证券", "查询证券", "获取证券", "分析股票", "分析证券", "分析行情", "监控股票", "监控行情", "跟踪股票", "跟踪行情", "了解股票", "关注股票", "查看财务", "查询财务", "获取财务", "分析财务", "股票分析", "证券分析", "技术指标", "技术分析", "K线", "MACD", "KDJ", "RSI", "BOLL", "均线", "移动平均", "布林带", "相对强弱", "随机指标", "指数平滑", "市盈率", "市净率", "PE", "PB", "ROE", "ROA", "净资产收益率", "资产收益率", "每股收益", "每股净资产", "净利润", "营业收入", "毛利率", "净利率", "财务", "财报", "年报", "季报", "财务指标", "财务分析", "基本面", "估值", "总股本", "流通股", "总市值", "流通市值", "股东权益", "负债率", "资产负债表", "利润表", "现金流量表", "所有者权益", "营业利润", "VWAP", "成交量加权平均价", "OBV", "VR", "VRSI", "VMACD", "VMA", "VOSC", "VSTD", "MA5", "MA10", "MA20", "MA30", "MA60", "MA120", "MA250", "5日均线", "10日均线", "20日均线", "30日均线", "60日均线", "120日均线", "250日均线", "25日均线", "55日均线", "移动平均线", "均线分析", "均线系统", "贵州茅台", "五粮液", "比亚迪", "平安银行", "山西汾酒", "招商银行", "中国平安", "宁德时代", "腾讯控股", "阿里巴巴", "中国石油", "工商银行", "建设银行", "中国银行", "农业银行", "中国石化", "中国移动", "中国联通", "万科A", "格力电器", "美的集团", "海康威视", "东方财富", "同花顺", "茅台", "平安", "招行", "宁德", "腾讯", "阿里", "万科", "格力", "美的"], "trigger_patterns": ["\\d{6}\\.(SZ|SH|sz|sh)", "\\d{5}\\.(HK|hk)", "\\d{6}[.](SZ|SH|sz|sh)", "\\d{5}[.](HK|hk)"], "stock_names": ["贵州茅台", "五粮液", "比亚迪", "平安银行", "山西汾酒", "招商银行", "中国平安", "宁德时代", "腾讯控股", "阿里巴巴", "中国石油", "工商银行", "建设银行", "中国银行", "农业银行", "中国石化", "中国移动", "中国联通", "万科A", "格力电器", "美的集团", "海康威视", "东方财富", "同花顺", "茅台", "平安", "招行", "宁德", "腾讯", "阿里", "万科", "格力", "美的"], "query_types": ["real_time", "history", "basic_data", "financial_report", "technical_indicators", "smart_query", "vwap"], "technical_indicators": ["MACD", "RSI", "KDJ", "BOLL", "MA", "EXPMA", "DMA", "TRIX", "CCI", "WR", "ROC", "OBV", "VR", "VRSI", "VMACD", "VMA", "VOSC", "VSTD", "CDP", "MIKE", "ATR", "STD", "BIAS", "VWAP"], "usage_examples": ["查询贵州茅台的实时行情", "分析五粮液的MACD指标", "获取比亚迪的25日均线数据", "查看招商银行的财务报表", "分析平安银行的成交量指标", "获取宁德时代的VWAP数据"], "recommendation_message": {"template": "💡 检测到您可能需要专业金融数据（置信度: {confidence}）\n   触发关键词: {keywords}\n\n   建议使用 MCP金融分析服务器，它可以提供：\n   • 实时股票行情数据\n   • 历史价格和技术指标\n   • 财务指标和基本面数据\n   • 专业投资分析支持\n   \n   🔗 MCP服务器已自动连接，直接询问即可使用", "confidence_threshold": 0.6}, "tools": [{"name": "get_stock_real_time", "description": "获取股票实时行情数据", "keywords": ["实时", "行情", "股价", "最新价", "涨跌"]}, {"name": "get_stock_history", "description": "获取股票历史行情数据", "keywords": ["历史", "走势", "过去", "月", "年", "日"]}, {"name": "get_stock_basic_data", "description": "获取股票基础数据", "keywords": ["基础", "市值", "股本", "市盈率", "市净率"]}, {"name": "get_financial_report", "description": "获取财务报表数据", "keywords": ["财报", "报表", "财务", "利润", "资产", "现金流"]}, {"name": "analyze_technical_indicators", "description": "技术指标分析", "keywords": ["技术指标", "MACD", "RSI", "KDJ", "均线", "指标分析"]}, {"name": "financial_smart_query", "description": "智能金融查询", "keywords": ["查询", "分析", "获取", "查看", "了解", "关注"]}]}}}