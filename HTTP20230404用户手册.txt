iFinD HTTP API 1.0用户手册
版本	时间	更新说明	
1.0	2021-12-29	初版发布	
iFinD HTTP API产品说明
iFinD HTTP API是对过去各语言SDK形式的一个补充，用户可以以API形式直接向同花顺服务器发
送HTTP请求，运行环境不再需要下载SDK，从而使用户摆脱设备、语言、环境的限制。
TOKEN获取与更新
接口鉴权方案分为长期的refresh_token和短期的access_token。
refresh_token在账号有效期内永久有效，refresh_token只用来请求当前有效的access_token或者
获取一个新的access_token。refresh_token只能通过超级命令客户端“工具-refresh_token更新”更
新。refresh_token更新后，所有环境过去的的refresh_token、access_token均会失效，更新
refresh_token相当于更改HTTP接口的账号密码。
access_token用来直接向同花顺服务器请求数据。access_token会在初次生成的七天后失效。单个
access_token最多支持20个IP。失效前三天重新获取access_token会刷新失效时间。
获取当前有效的access_token
请求参数
项目	传参说明	
URL	https://ft.10jqka.com.cn/api/v1/get_access_token	
requestMethod	POST/GET	
requestHeaders	{"Content-Type":"application/json","refresh_token":user_refresh_token}	
注：refresh_token放BODY也可
获取一个新的access_token
获取一个新的access_token会造成所有旧的access_token失效
请求参数
项目	传参说明	
URL	https://ft.10jqka.com.cn/api/v1/update_access_token	
requestMethod	POST/GET	
requestHeaders	{"Content-Type":"application/json","refresh_token":user_refresh_token}	
示例——使用python请求当前有效的access_token
import requests 
import json 
getAccessTokenUrl = 'https://ft.10jqka.com.cn/api/v1/get_access_token' 
refreshToken = 
'eyJzaWduX3RpbWUiOiIyMDIxLTEyduX3RpbWUiOiIyMjI1In0=.eyJ1aWQiOiIxMDYxMDUwMDMifQ==.F4CBB 
BC230969B0F220F9D6ECB666A230969B0F220FFBBCDA4156A3B78A1BB896' 
getAccessTokenHeader = {"Content-
Type":"application/json","refresh_token":refreshtoken} 
getAccessTokenResponse = 
requests.post(url=getAccessTokenUrl,headers=getAccessTokenHeader) 
accessToken = json.loads(getAccessTokenResponse.content)['data']['access_token'] 
print(accessToken)
使用access_token向同花顺服务器取数
使用Windows超级命令协助获取协议
基础函数、日期序列函数、EDB函数、专题报表函数的指标或者科目过多，很难把所有内容都集
中在文档中，目前还是推荐用户使用Windows超级命令协助获取协议，后续我们会额外提供web
版的协议生成页面。
协议说明
requestMethod需要为POST
requestHeaders需要包含{"Content-
Type":"application/json","access_token":user_access_token}
各函数的formData或者requestURL见下方协议或者使用Windows超级命令生成
请求参数需要统一处理为urlencode，请求参数压缩支持：Accept-Encoding: gzip,deflate
返回内容统一为unicode编码
示例——以Python请求300033实时行情为例
# -*- coding: utf-8 -*- 
import requests 
thsUrl = 'https://ft.10jqka.com.cn/ds_service/api/v1/real_time_quotation' 
accessToken = '12fe737bc2014f39f195a2b7b03e3b11ec63b66b' 
thsHeaders = {"Content-Type":"application/json","access_token":accessToken} 
thsPara = {"codes":"300033.SZ","indicators":"open,high,low,latest"} 
thsResponse = requests.post(url=thsUrl,json=thsPara,headers=thsHeaders) 
print(thsResponse.content)
各函数URL及formData生成逻辑
基础数据
URL
https://ft.10jqka.com.cn/api/v1/basic_data_service
formData
key	key	是	否	必	须	value	示例	
codes	是	半角逗号分隔的所有代码	"codes":"300033.SZ,600030.SH"	
indipara	是	各个指标及其相关参数，indicator代	表指标英文名，indiparams代表该指	标的用户层的参数，otherparams代表	用户无需知晓但传输给服务端所需的	其他参数。otherparams中sys用来标	记服务端所需的name中为True的参	数。推荐使用Windows超级命令生	成。	见下方代码块	
示例
para = 
{ 
 "codes":"300033.SZ;600030.SH",  
 "indipara":[ 
 { 
 "indicator":"ths_sq_net_asset_yield_roe_index", 
 "indiparams":["0","101"], 
 "_otherparams":{ 
 "id":"680799", 
 "name":["THSCODE","F0","F1","FDIR"] 
 } 
 }, 
 { 
 "indicator":"ths_roe_avg_index", 
 "indiparams":[], 
 "_otherparams":{ 
 "id":"653072", 
 "name":["THSCODE","FDREPORT","FDIR"], 
 } 
 } 
 ] 
}
日期序列
URL
https://ft.10jqka.com.cn/api/v1/data_sequence
formData
key	key	是	否	必	须	value	示例	
codes	是	半角逗号分隔的所有代码	"codes":"300033.SZ,600030.SH"	
functionpara	否	key-value格式。所有key均取默	认时，functionpara省略。	见下方说明	
startdate	是	开始日期，支持”YYYYMMDD"”	YYYY-MM-DD"”YYYY/MM/DD"三	种日期格式	"startdate":"2018-01-01"	
enddate	是	结束日期，支持”YYYYMMDD"”	YYYY-MM-DD"”YYYY/MM/DD"三	种日期格式	"enddate":"2018-01-01"	
indipara	是	各个指标及其相关参数，	indicator代表指标英文名，	indiparams代表该指标的用户层	的参数，otherparams代表用户无	需知晓但传输给服务端所需的其	他参数。otherparams中sys用来	标记服务端所需的name中为True	的参数。推荐使用Windows超级	命令生成。	见下方代码块	
functionpara说明
名称	keys	value说明	省略时逻辑	
时间周期	Interval	D-日 W-周 M-月 Q-季 S-半年 Y-年	D-日	
日期类型	Days	Tradedays-交易日 Alldays-日历日	Tradedays-交易日	
非交易间隔处理	Fill	Previous-沿用之前数据 Blank-空值	Previous-沿用之前数据	
示例
para = 
{ 
 "codes":"300033.SZ;600030.SH",  
 "startdate":"2018-01-01", 
 "enddate":"2018-01-01", 
 "functionpara":{ 
 "Days":"tradedays", 
 "Fill":"Previous", 
 "Interval":"D" 
 } 
 "indipara":[ 
 { 
 "indicator":"ths_sq_net_asset_yield_roe_index", 
 "indiparams":["0","101"], 
 "_otherparams":{ 
 "id":"680799", 
 "name":["THSCODE","F0","F1","FDIR"] 
 } 
 }, 
 { 
 "indicator":"ths_roe_avg_index", 
 "indiparams":[], 
 "_otherparams":{ 
 "id":"653072", 
 "name":["THSCODE","FDREPORT","FDIR"] 
 } 
 } 
 ] 
}
历史行情
URL
https://ft.10jqka.com.cn/api/v1/cmd_history_quotation
formData
key	key	是否	必须	value	示例	
codes	是	半角逗号分隔的所有代码	"codes":"300033.SZ,600030.SH"	
indicators	是	半角逗号分隔的所有指标	"indicators":"preClose,open"	
functionpara	否	key-value格式。所有key均取默	认时，functionpara省略。	见下方说明	
startdate	是	开始日期，支	持"YYYYMMDD""YYYY-MM-	DD""YYYY/MM/DD"三种日期格	式	"startdate":"2018-01-01"	
enddate	是	结束日期，支	持"YYYYMMDD""YYYY-MM-	DD""YYYY/MM/DD"三种日期格	式	"enddate":"2018-01-01"	
indicators参数说明
指标名	指标说明	指标备注	
preClose	前收盘价		
open	开盘价		
high	最高价		
low	最低价		
close	收盘价		
avgPrice	均价		
change	涨跌		
changeRatio	涨跌幅		
volume	成交量		
amount	成交额		
turnoverRatio	换手率		
transactionAmount	成交笔数		
totalShares	总股本		
totalCapital	总市值		
floatSharesOfAShares	A股流通股本		
floatSharesOfBShares	B股流通股本		
floatCapitalOfAShares	A股流通市值		
floatCapitalOfBShares	B股流通市值		
pe_ttm	市盈率（TTM）		
pe	PE市盈率		
pb	PB市净率		
ps	PS市销率		
pcf	PCF市现率		
ths_trading_status_stock	交易状态		
ths_up_and_down_status_stock	涨跌停状态		
指标名	指标说明	指标备注	
ths_af_stock	复权因子		
ths_vol_after_trading_stock	盘后成交量		
ths_trans_num_after_trading_stock	盘后成交笔数		
ths_amt_after_trading_stock	盘后成交额		
ths_vaild_turnover_stock	有效换手率		
netAssetValue	单位净值	基金专用	
adjustedNAV	复权单位净值	基金专用	
accumulatedNAV	累计单位净值	基金专用	
premium	贴水	基金专用	
premiumRatio	贴水率	基金专用	
estimatedPosition	估算仓位	基金专用	
floatCapital	流通市值	指数专用	
pe_ttm_index	PE(TTM)	指数专用	
pb_mrq	PB(MRQ)	指数专用	
pe_indexPublisher	PE(指数发布方）	指数专用	
yieldMaturity	到期收益率	债券专用	
remainingTerm	剩余期限	债券专用	
maxwellDuration	麦氏久期	债券专用	
modifiedDuration	修正久期	债券专用	
convexity	凸性	债券专用	
close_2330	收盘价（23：30）	外汇交易中心专用	
openInterest	持仓量	期权专用	
positionChange	持仓变动	期权专用	
preSettlement	前结算价	期货专用	
settlement	结算价	期货专用	
指标名	指标说明	指标备注	
change_settlement	涨跌（结算价）	期货专用	
chg_settlement	涨跌幅（结算价）	期货专用	
openInterest	持仓量	期货专用	
positionChange	持仓变动	期货专用	
amplitude	振幅	期货专用	
functionpara参数说明
名	称	keys	value说明	省略时逻	辑	
时	间	周	期	Interval	D-日 W-周 M-月 Q-季 S-半年 Y-年 同抽样周期二选	一，返回周期汇总统计值	D-日	
抽	样	周	期	SampleInterval	D-日 W-周 M-月 Q-季 S-半年 Y-年 同时间周期二选	一，返回周期最后一个交易日日频数据	D-日	
复	权	方	式	CPS	1-不复权 2-前复权（分红再投） 3-后复权（分红再	投） 4-全流通前复权（分红再投） 5-全流通后复权	（分红再投） 6-前复权（现金分红） 7-后复权（现	金分红）	1-不复权	
报	价	类	型	PriceType	1-全价 2-净价 仅债券生效	1-全价	
非	交	易	间	隔	处	理	Fill	Previous-沿用之前数据 Blank-空值 具体数值-自定义	数值 Omit-缺省值	Previous-	沿用之前	数据	
设	定	复	权	基	点	BaseDate	复权基点日期，"YYYY-MM-DD"	后复权按	上市日，	前复权按	最新日	
货	币	Currency	MHB-美元 GHB-港元 RMB-人民币 YSHB-原始货币	YSHB-原	始货币	
示例
para = 
{ 
 "codes":"300033.SZ,600030.SH",  
 "indicators":"preClose,open", 
 "startdate":"2018-01-01", 
 "enddate":"2018-01-01"， 
 "functionpara": { 
 "Fill": "Previous", 
 "Currency": "YSHB", 
 "PriceType": "0", 
 "Interval": "D", 
 "CPS": "6" 
 } 
}
高频序列
URL
https://ft.10jqka.com.cn/api/v1/high_frequency
formData
key	key	是	否	必	须	value	示例	
codes	是	半角逗号分隔的所有代码	"codes":"300033.SZ,600030.SH"	
indicators	是	半角逗号分隔所有指标	"indicators":"open,high"	
functionpara	否	key-value格式。所有key均取默	认时，functionpara省略。技术	指标额外在calculate生成，生成	规则见下文。	见下方代码块	
starttime	是	开始日期，支持”YYYYMMDD 	HH:mm:ss"”YYYY-MM-DD 	HH:mm:ss"”YYYY/MM/DD 	HH:mm:ss"三种时间格式	"starttime":"2018-01-01	09:15:00"	
endtime	是	结束日期，支持”YYYYMMDD 	HH:mm:ss"”YYYY-MM-DD 	HH:mm:ss"”YYYY/MM/DD 	HH:mm:ss"三种日期格式	"endtime":"2018-01-01	15:15:00"	
indicators参数说明
指标名	指标说明	指标备注	
open	开盘价	通用	
high	最高价	通用	
low	最低价	通用	
close	收盘价	通用	
avgPrice	均价	通用	
volume	成交量	通用	
amount	成交额	通用	
change	涨跌	通用	
changeRatio	涨跌幅	通用	
turnoverRatio	换手率	通用	
sellVolume	内盘	通用	
buyVolume	外盘	通用	
changeRatio_accumulated	涨跌幅(累计)	股票	
BBI	BBI多空指数	股票	
DDI	DDI方向标准离差指数	股票	
DMA	DMA平均线差	股票	
MA	MA简单移动平均	股票	
EXPMA	EXPMA指数平均数	股票	
MACD	MACD指数平滑异同平均	股票	
MTM	MTM动力指标	股票	
PRICEOSC	PRICEOSC价格振荡指标	股票	
TRIX	TRIX三重指数平滑平均	股票	
BIAS	BIAS乖离率	股票	
CCI	CCI顺势指标	股票	
DBCD	DBCD异同离差乖离率	股票	
指标名	指标说明	指标备注	
DPO	DPO区间震荡线	股票	
KDJ	KDJ随机指标	股票	
LWR	LWR威廉指标	股票	
ROC	ROC变动速率	股票	
RSI	RSI相对强弱指标	股票	
SI	SI摆动指标	股票	
SRDM	SRDM动向速度比率	股票	
VROC	VROC量变动速率	股票	
VRSI	VRSI量相对强弱	股票	
WR	WR威廉指标	股票	
ARBR	ARBR人气意愿指标	股票	
CR	CR能量指标	股票	
PSY	PSY心理指标	股票	
VR	VR成交量比率	股票	
WAD	WAD威廉聚散指标	股票	
MFI	MFI资金流向指标	股票	
OBV	OBV能量潮	股票	
PVT	PVT量价趋势指标	股票	
WVAD	WVAD威廉变异离散量	股票	
BBIBOLL	BBIBOLL多空布林线	股票	
BOLL	BOLL布林线	股票	
CDP	CDP逆势操作	股票	
ENV	ENV指标	股票	
MIKE	MIKE麦克指标	股票	
LB	量比	股票	
指标名	指标说明	指标备注	
VMA	VMA量简单移动平均	股票	
VMACD	VMACD量指数平滑异同平均	股票	
VOSC	VOSC成交量震荡	股票	
TAPI	TAPI加权指数成交值	股票	
VSTD	VSTD成交量标准差	股票	
ADTM	ADTM动态买卖气指标	股票	
MI	MI动量指标	股票	
MICD	MICD异同离差动力指数	股票	
RC	RC变化率指数	股票	
RCCD	RCCD异同离差变化率指数	股票	
SRMI	SRMI(MI修正指标)	股票	
DPTB	DPTB大盘同步指标	股票	
JDQS	JDQS阶段强势指标	股票	
JDRS	JDRS阶段弱势指标	股票	
ZDZB	ZDZB筑底指标	股票	
ATR	ATR真实波幅	股票	
MASS	MASS梅丝线	股票	
STD	STD标准差	股票	
VHF	VHF纵横指标	股票	
CVLT	CVLT佳庆离散指标	股票	
large_amt_timeline	主力净流入金额(分时)	股票	
active_buy_large_volume	主动买入特大单量	股票,同花顺指数	
active_sell_large_volume	主动卖出特大单量	股票,同花顺指数	
active_buy_main_volume	主动买入大单量	股票,同花顺指数	
active_sell_main_volume	主动卖出大单量	股票,同花顺指数	
指标名	指标说明	指标备注	
active_buy_middle_volume	主动买入中单量	股票,同花顺指数	
active_sell_middle_volume	主动卖出中单量	股票,同花顺指数	
possitive_buy_large_volume	被动买入特大单量	股票,同花顺指数	
possitive_sell_large_volume	被动卖出特大单量	股票,同花顺指数	
possitive_buy_main_volume	被动买入大单量	股票,同花顺指数	
possitive_sell_main_volume	被动卖出大单量	股票,同花顺指数	
possitive_buy_middle_volume	被动买入中单量	股票,同花顺指数	
possitive_sell_middle_volume	被动卖出中单量	股票,同花顺指数	
active_buy_large_amount	主动买入特大单金额	股票,同花顺指数	
active_sell_large_amount	主动卖出特大单金额	股票,同花顺指数	
active_buy_main_amount	主动买入大单金额	股票,同花顺指数	
active_sell_main_amount	主动卖出大单金额	股票,同花顺指数	
possitive_buy_large_amount	被动买入特大单金额	股票,同花顺指数	
possitive_sell_large_amount	被动卖出特大单金额	股票,同花顺指数	
possitive_buy_main_amount	被动买入大单金额	股票,同花顺指数	
possitive_sell_main_amount	被动卖出大单金额	股票,同花顺指数	
active_buy_middle_amount	主动买入中单金额	股票,同花顺指数	
active_sell_middle_amount	主动卖出中单金额	股票,同花顺指数	
possitive_buy_middle_amount	被动买入中单金额	股票,同花顺指数	
possitive_sell_middle_amount	被动卖出中单金额	股票,同花顺指数	
openInterest	持仓量	期权，期货	
changeRatio_periodical	涨跌幅(阶段)	期权专用	
技术指标规则说明
选择技术指标时，需同时在functionpara的calculate字段以indicators为key，以半角逗号拼接各个
参数字符串为value。为下列特殊的参数额外使用下列英文名，其他的沿用下拉框英文值。
indicators参数说明
指标名	指标说明	指标备注	
BBI	BBI多空指数	{周期1},{周期2},{周期3},{周期4}	
DDI	DDI方向标准离差指数	{周期1},{周期2},{平滑因子},{周期3},{DDI or ADDI	or AD}	
DMA	DMA平均线差	{短周期},{长周期},{周期},{DDD or AMA}	
MA	MA简单移动平均	{周期}	
EXPMA	EXPMA指数平均数	{周期}	
MACD	MACD指数平滑异同平均	{短周期},{长周期},{周期},{DIFF or DEA or MACD}	
MTM	MTM动力指标	{间隔周期},{周期},{MTM or MTMMA}	
PRICEOSC	PRICEOSC价格振荡指标	{短周期},{长周期}	
TRIX	TRIX三重指数平滑平均	{周期1},{周期2},{TRIX or TRMA}	
BIAS	BIAS乖离率	{周期}	
CCI	CCI顺势指标	{周期}	
DBCD	DBCD异同离差乖离率	{周期1},{周期2},{周期3},{DBCD or MM}	
DPO	DPO区间震荡线	{周期1},{周期2},{DPO or MADPO}	
KDJ	KDJ随机指标	{周期},{周期1},{周期2},{K or D or J}	
LWR	LWR威廉指标	{周期},{周期1},{周期2},{LWR1 or LWR2}	
ROC	ROC变动速率	{间隔周期},{周期},{ROC or ROCMA}	
RSI	RSI相对强弱指标	{周期}	
SI	SI摆动指标		
SRDM	SRDM动向速度比率	{周期},{SRDM or ASRDM}	
VROC	VROC量变动速率	{周期}	
VRSI	VRSI量相对强弱	{周期}	
WR	WR威廉指标	{周期}	
ARBR	ARBR人气意愿指标	{周期},{AR or BR}	
CR	CR能量指标	{周期}	
指标名	指标说明	指标备注	
PSY	PSY心理指标	{周期1},{周期2},{PSY or MAPSY}	
VR	VR成交量比率	{周期}	
WAD	WAD威廉聚散指标	{周期},{WAD or MAWAD}	
MFI	MFI资金流向指标	{周期}	
OBV	OBV能量潮	{OBV or OBV_XZ}	
PVT	PVT量价趋势指标		
WVAD	WVAD威廉变异离散量	{周期1},{周期2},{WVAD or MAWVAD}	
BBIBOLL	BBIBOLL多空布林线	{周期},{宽带},{BBIBOLL or UPR or DWN}	
BOLL	BOLL布林线	{周期},{宽带},{MID or UPPER or LOWER}	
CDP	CDP逆势操作	{CDP or AH or AL or NH or NL}	
ENV	ENV指标	{周期},{UPPER or LOWER}	
MIKE	MIKE麦克指标	{周期},{WR or MR or SR or WS or MS or SS}	
LB	量比	{周期}	
VMA	VMA量简单移动平均	{周期}	
VMACD	VMACD量指数平滑异同	平均	{短周期},{长周期},{周期},{DIFF or DEA or MACD}	
VOSC	VOSC成交量震荡	{短周期},{长周期}	
TAPI	TAPI加权指数成交值	{周期},{TAPI or MATAPI}	
VSTD	VSTD成交量标准差	{周期}	
ADTM	ADTM动态买卖气指标	{周期},{周期1},{ADTM or MAADTM}	
MI	MI动量指标	{周期},{A or MI}	
MICD	MICD异同离差动力指数	{周期},{周期1},{周期2},{DIF or MICD}	
RC	RC变化率指数	{周期}	
RCCD	RCCD异同离差变化率指	数	{周期},{周期1},{周期2},{DIF or RCCD}	
指标名	指标说明	指标备注	
SRMI	SRMI(MI修正指标)	{周期}	
DPTB	DPTB大盘同步指标	{周期},{000001 or 000010 or 399001 or 000300}	
JDQS	JDQS阶段强势指标	{周期},{000001 or 000010 or 399001 or 000300}	
JDRS	JDRS阶段弱势指标	{周期},{000001 or 000010 or 399001 or 000300}	
ZDZB	ZDZB筑底指标	{周期},{周期1},{周期2},{B or D}	
ATR	ATR真实波幅	{周期},{TR or ATR}	
MASS	MASS梅丝线	{周期1},{周期2}	
STD	STD标准差	{周期}	
VHF	VHF纵横指标	{周期}	
CVLT	CVLT佳庆离散指标	{周期}	
functionpara控件说明
名	称	keys	value说明	省略时逻辑	
设	置	时	间	区	间-	开	始	时	间	Limitstart	限定每个交易日数据的开始时间		
设	置	时	间	区	间-	结	束	时	间	Limitend	限定每个交易日数据的截止时间		
时	间	周	期	Interval	1-1 分钟 3-3 分钟 5-5 分钟 10-10分钟 15-15 分钟 30-	30 分钟 60-60分钟	1-1 分钟	
非	交	易	间	隔	处	理	Fill	Previous-沿用之前数据 Blank-空值 具体数值-自定义	数值 Original-不处理	Original-不	处理	
名	称	keys	value说明	省略时逻辑	
分	红	再	投	复	权	方	式	CPS	后复权（分红方案计算）-backward1 前复权（交易所	价格计算）-forward3 后复权（交易所价格计算） -	backward3 全流通前复权（分红方案计算）-forward2	全流通后复权（分红方案计算）-backward2 全流通前	复权（交易所价格计算）-forward4 全流通后复权	（交易所价格计算）-backward4 不复权-no	no-不复权	
时	间	戳	格	式	Timeformat	BeiJingTime-北京时间 LocalTime-当地时间	BeiJingTime-	北京时间	
设	定	复	权	基	点	BaseDate	复权基点日期，"YYYY-MM-DD"	后复权按上	市日，前复	权按最新日	
示例
para = 
{ 
 "codes": "300033.SZ,600030.SH", 
 "indicators": "open,high,SI,MACD,DPTB,OBV,KDJ", 
 "starttime": "2018-01-01 09:15:00", 
 "endtime": "2018-01-01 09:50:00", 
 "functionpara": { 
 "Interval": "1", 
 "Fill": "Original", 
 "calculate": { 
 "SI":"" 
 "MACD": "12,26,9,MACD", 
 "DPTB": "7,000001", 
 "OBV": "OBV_XZ", 
 "KDJ": "9,3,3,K", 
 } 
 } 
 }
实时行情
URL
https://ft.10jqka.com.cn/api/v1/real_time_quotation
formData
key	key	是否	必须	value	示例	
codes	是	半角逗号分隔的所有代码	"codes":"300033.SZ,600030.SH"	
indicators	是	半角逗号分隔的所有指标	"indicators":"open,high"	
functionpara	否	key-value格式。仅包含债券报价	方式（pricetype）控件失效时，	不生成，否则生成。	见下方代码块	
indicators参数说明
指标名	指标说明	指标备注	
tradeDate	交易日期	通用	
tradeTime	交易时间	通用	
preClose	前收盘价	通用	
open	开盘价	通用	
high	最高价	通用	
low	最低价	通用	
latest	最新价	通用	
latestAmount	现额	通用	
latestVolume	现量	通用	
avgPrice	均价	通用	
change	涨跌	通用	
changeRatio	涨跌幅	通用	
upperLimit	涨停价	通用	
downLimit	跌停价	通用	
amount	成交额	通用	
volume	成交量	通用	
turnoverRatio	换手率	通用	
sellVolume	内盘	通用	
buyVolume	外盘	通用	
totalBidVol	委买十档总量	股票	
totalAskVol	委卖十档总量	股票	
totalShares	总股本	股票	
totalCapital	总市值	股票	
pb	市净率	股票	
riseDayCount	连涨天数	股票	
指标名	指标说明	指标备注	
suspensionFlag	停牌标志	股票	
tradeStatus	交易状态	股票	
chg_1min	1分钟涨跌幅	股票	
chg_3min	3分钟涨跌幅	股票	
chg_5min	5分钟涨跌幅	股票	
chg_5d	5日涨跌幅	股票	
chg_10d	10日涨跌幅	股票	
chg_20d	20日涨跌幅	股票	
chg_60d	60日涨跌幅	股票	
chg_120d	120日涨跌幅	股票	
chg_250d	250日涨跌幅	股票	
chg_year	年初至今涨跌幅	股票	
mv	流通市值	股票	
vol_ratio	量比	股票	
committee	委比	股票	
commission_diff	委差	股票	
pe_ttm	市盈率TTM	股票	
pbr_lf	市净率LF	股票	
swing	振幅	股票	
lastest_price	最新成交价	股票	
af_backward	后复权因子(分红方案计算)	股票	
bid10	买10价	股票	
bid9	买9价	股票	
bid8	买8价	股票	
bid7	买7价	股票	
指标名	指标说明	指标备注	
bid6	买6价	股票	
bid5	买5价	股票	
bid4	买4价	股票	
bid3	买3价	股票	
bid2	买2价	股票	
bid1	买1价	股票	
ask1	卖1价	股票	
ask2	卖2价	股票	
ask3	卖3价	股票	
ask4	卖4价	股票	
ask5	卖5价	股票	
ask6	卖6价	股票	
ask7	卖7价	股票	
ask8	卖8价	股票	
ask9	卖9价	股票	
ask10	卖10价	股票	
bidSize10	买10量	股票	
bidSize9	买9量	股票	
bidSize8	买8量	股票	
bidSize7	买7量	股票	
bidSize6	买6量	股票	
bidSize5	买5量	股票	
bidSize4	买4量	股票	
bidSize3	买3量	股票	
bidSize2	买2量	股票	
指标名	指标说明	指标备注	
bidSize1	买1量	股票	
askSize1	卖1量	股票	
askSize2	卖2量	股票	
askSize3	卖3量	股票	
askSize4	卖4量	股票	
askSize5	卖5量	股票	
askSize6	卖6量	股票	
askSize7	卖7量	股票	
askSize8	卖8量	股票	
askSize9	卖9量	股票	
askSize10	卖10量	股票	
avgBuyPrice	均买价	股票	
avgSellPrice	均卖价	股票	
totalBuyVolume	总买量	股票	
totalSellVolume	总卖量	股票	
transClassification	成交分类	股票	
transTimes	成交次数	股票	
mainInflow	主力流入金额	股票	
mainOutflow	主力流出金额	股票	
mainNetInflow	主力净流入金额	股票	
retailInflow	散户流入金额	股票	
retailOutflow	散户流出金额	股票	
retailNetInflow	散户净流入金额	股票	
largeInflow	超大单流入金额	股票	
largeOutflow	超大单流出金额	股票	
指标名	指标说明	指标备注	
largeNetInflow	超大单净流入金额	股票	
bigInflow	大单流入金额	股票	
bigOutflow	大单流出金额	股票	
bigNetInflow	大单净流入金额	股票	
middleInflow	中单流入金额	股票	
middleOutflow	中单流出金额	股票	
middleNetInflow	中单净流入金额	股票	
smallInflow	小单流入金额	股票	
smallOutflow	小单流出金额	股票	
smallNetInflow	小单净流入金额	股票	
activeBuyLargeAmt	主动买入特大单金额	股票	
activeSellLargeAmt	主动卖出特大单金额	股票	
activeBuyMainAmt	主动买入大单金额	股票	
activeSellMainAmt	主动卖出大单金额	股票	
activeBuyMiddleAmt	主动买入中单金额	股票	
activeSellMiddleAmt	主动卖出中单金额	股票	
activeBuySmallAmt	主动买入小单金额	股票	
activeSellSmallAmt	主动卖出小单金额	股票	
possitiveBuyLargeAmt	被动买入特大单金额	股票	
possitiveSellLargeAmt	被动卖出特大单金额	股票	
possitiveBuyMainAmt	被动买入大单金额	股票	
possitiveSellMainAmt	被动卖出大单金额	股票	
possitiveBuyMiddleAmt	被动买入中单金额	股票	
possitiveSellMiddleAmt	被动卖出中单金额	股票	
possitiveBuySmallAmt	被动买入小单金额	股票	
指标名	指标说明	指标备注	
possitiveSellSmallAmt	被动卖出小单金额	股票	
activeBuyLargeVol	主动买入特大单量	股票	
activeSellLargeVol	主动卖出特大单量	股票	
activeBuyMainVol	主动买入大单量	股票	
activeSellMainVol	主动卖出大单量	股票	
activeBuyMiddleVol	主动买入中单量	股票	
activeSellMiddleVol	主动卖出中单量	股票	
activeBuySmallVol	主动买入小单量	股票	
activeSellSmallVol	主动卖出小单量	股票	
possitiveBuyLargeVol	被动买入特大单量	股票	
possitiveSellLargeVol	被动卖出特大单量	股票	
possitiveBuyMainVol	被动买入大单量	股票	
possitiveSellMainVol	被动卖出大单量	股票	
possitiveBuyMiddleVol	被动买入中单量	股票	
possitiveSellMiddleVol	被动卖出中单量	股票	
possitiveBuySmallVol	被动买入小单量	股票	
possitiveSellSmallVol	被动卖出小单量	股票	
activebuy_volume	主买总量	股票	
activesell_volume	主卖总量	股票	
activebuy_amt	主买总额	股票	
activesell_amt	主卖总额	股票	
post_lastest	盘后最新成交价	股票	
post_latestVolume	盘后现量	股票	
post_volume	盘后成交量	股票	
post_amt	盘后成交额	股票	
指标名	指标说明	指标备注	
post_dealnum	盘后成交笔数	股票	
priceDiff	买卖价差	港股专用	
sharesPerHand	每手股数	港股专用	
expiryDate	到期日	港股专用	
tradeStatus	交易状态	港股专用	
iopv	IOPV（净值估值）	基金专用	
premium	折价	基金专用	
riseCount	上涨家数	指数专用	
fallCount	下跌家数	指数专用	
upLimitCount	涨停家数	指数专用	
downLimitCount	跌停家数	指数专用	
suspensionCount	停牌家数	指数专用	
pure_bond_value_cb	纯债价值	指数专用	
surplus_term	剩余期限(天)	指数专用	
dealDirection	成交方向	期货期权专用	
dealtype	成交性质	期货期权专用	
impliedVolatility	隐含波动率	期权专用	
historyVolatility	历史波动率	期权专用	
delta	Delta	期权专用	
gamma	Gamma	期权专用	
vega	Vega	期权专用	
theta	Theta	期权专用	
rho	Rho	期权专用	
pre_open_interest	前持仓量	期权专用	
pre_implied_volatility	前隐含波动率	期权专用	
指标名	指标说明	指标备注	
volume_pcr_total	成交量pcr（品种）	期权专用	
volume_pcr_month	成交量pcr（同月）	期权专用	
示例
para = 
{ 
 "codes": "300033.SZ,600000.SH", 
 "indicators": "open,high", 
 }
日内快照
URL
https://ft.10jqka.com.cn/api/v1/snap_shot
formData
key	key	是	否	必	须	value	示例	
codes	是	半角逗号分隔的所有代码	"codes":"300033.SZ,600030.SH"	
indicators	是	半角逗号分隔的所有指标	"indicators":"open,high"	
starttime	是	开始日期，支持”YYYYMMDD 	HH:mm:ss"”YYYY-MM-DD 	HH:mm:ss"”YYYY/MM/DD 	HH:mm:ss"三种时间格式	"starttime":"2018-01-01	09:15:00"	
endtime	是	结束日期，支持”YYYYMMDD 	HH:mm:ss"”YYYY-MM-DD 	HH:mm:ss"”YYYY/MM/DD 	HH:mm:ss"三种日期格式	"endtime":"2018-01-01	15:15:00"	
indicators参数说明
指标名	指标说明	指标备注	
tradeDate	交易日期	股票	
tradeTime	交易时间	股票	
preClose	前收盘价	股票	
open	开盘价	股票	
high	最高价	股票	
low	最低价	股票	
latest	现价	股票	
amt	成交额	股票	
vol	成交量	股票	
amount	累计成交额	股票	
volume	累计成交量	股票	
tradeNum	成交次数	股票	
bid10	买10价	股票	
bid9	买9价	股票	
bid8	买8价	股票	
bid7	买7价	股票	
bid6	买6价	股票	
bid5	买5价	股票	
bid4	买4价	股票	
bid3	买3价	股票	
bid2	买2价	股票	
bid1	买1价	股票	
ask1	卖1价	股票	
ask2	卖2价	股票	
ask3	卖3价	股票	
指标名	指标说明	指标备注	
ask4	卖4价	股票	
ask5	卖5价	股票	
ask6	卖6价	股票	
ask7	卖7价	股票	
ask8	卖8价	股票	
ask9	卖9价	股票	
ask10	卖10价	股票	
bidSize10	买10量	股票	
bidSize9	买9量	股票	
bidSize8	买8量	股票	
bidSize7	买7量	股票	
bidSize6	买6量	股票	
bidSize5	买5量	股票	
bidSize4	买4量	股票	
bidSize3	买3量	股票	
bidSize2	买2量	股票	
bidSize1	买1量	股票	
askSize1	卖1量	股票	
askSize2	卖2量	股票	
askSize3	卖3量	股票	
askSize4	卖4量	股票	
askSize5	卖5量	股票	
askSize6	卖6量	股票	
askSize7	卖7量	股票	
askSize8	卖8量	股票	
指标名	指标说明	指标备注	
askSize9	卖9量	股票	
askSize10	卖10量	股票	
avgBuyPrice	均买价	股票	
avgSellPrice	均卖价	股票	
totalBuyVolume	总买量	股票	
totalSellVolume	总卖量	股票	
dealDirection	成交方向(仅当日有效)	股票、期货、期权	
dealtype	成交性质(仅当日有效)	期货、期权	
示例
para = 
{ 
 "codes":"300033.SZ,600030.SH",  
 "indicators":"open,high", 
 "starttime":"2018-01-01 09:15:00", 
 "endtime":"2018-01-01 15:15:00" 
}
经济数据库
URL
https://ft.10jqka.com.cn/api/v1/edb_service
formData
key	key	是	否	必	须	value	示例	
indicators	是	半角逗号分隔的所有指	标，宏观指标过多，推	荐使用Windows超级命	令生成。	"indicators":"M001620326,M002822183"	
functionpara	否	key-value格式,省略时不	进行更新时间筛选。两	个时间控件更新起始时	间（startrtime）和更新	结束时间	（endrtime），不勾选	时省略	见下方代码块	
startdate	是	开始日期，支持”	YYYYMMDD"”YYYY-	MM-DD"”	YYYY/MM/DD"三种时间	格式	"startdate":"2018-01-01"	
enddate	是	结束日期，支持”	YYYYMMDD"”YYYY-	MM-DD"”	YYYY/MM/DD"三种日期	格式	"enddate":"2018-01-01"	
示例
para = 
{ 
 "indicators": "M001620326,M002822183", 
 "startdate": "2018-01-01", 
 "enddate": "2018-01-01", 
 "functionpara": { 
 "startrtime": "2018-01-01 09:15:00", 
 "endrtime": "2018-01-01 10:15:00", 
 } 
 },
专题报表函数
URL
https://ft.10jqka.com.cn/api/v1/data_pool
formData
过多，推荐使用Windows超级命令生成。
key	key	是	否	必	须	value	示例	
reportname	是		"reportname":"p03341"	
functionpara	是	key-	value	的参	数，	key按	照过去	的指标	名称	见下方代码块	
outputpara	是	半角逗	号分隔	的Y/N	来控制	是否显	示该字	段	"outputpara":"date:Y,thscode:Y,security_name:Y,weight:Y"	
示例
para = 
{ 
 "reportname":"p03341",  
 "functionpara": { 
 "sdate": "20210421", 
 "edate":"20211119", 
 "xmzt": "全部", 
 "jcsslx":"全部", 
 "jys":"全部" 
 },                             
 "outputpara":"p03341_f001:Y,p03341_f002:Y" 
}
组合管理
组合新建
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必	须	value	示例	
功能名称	func	是		"func":	"newportf"	
组合名称	name	是		"name":"股债策	略组合"	
所属分组	group	是		"group": 11580	
业绩基准，基准代码	和名称	performbm	否，默	认填充	沪深300	键值对	"performbm": { 	"code": 	"000300.SH", 	"name": "沪深	300"	
}					
跌价基准，基准代	码、基准名称、基准	类型	supbm	否，省	略时为	空	键值对	"supbm": {	
"code":"000001.SH", 	"name": "上证指数",					
"benchmarkType":	"1"}					
交易日	tday	否，默	认国内	交易所	枚举值国内交易	所、港股、美股、	国内银行间	"tday": "国内交	易所"	
基准货币	currency	否，默	认人民	币	枚举值CND、	HKD、USD	"currency":	"CNY"	
融资利率%	finacrate	否，默	认为空		"finacrate": "7.5"	
融券利率%	secrate	否，默	认为空		"secrate": "5.5"	
名称	key	是否必	须	value	示例	
组合说明	info	否，默	认为空		"info": "股票与债	券结合的策略组	合"	
示例
para = { 
"func": "newportf", 
"name": "股债联动", 
"group": 11580, 
"performbm": { 
"code": "000300.SH", 
"name": "沪深300" 
}, 
"supbm": { 
"code": "", 
"name": "", 
}, 
"tday": "国内交易所", 
"currency": "CNY", 
"finacrate": "", 
"secrate": "", 
"info": "股票与债券结合的策略组合" 
}
组合导入
1. 模板导入
通过读取组合文件的内容，进行上传完成组合导入。
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必须	value	示例	
功能名称	func	是	importf	"func": "importf"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
组合内容	content	是	二维表		
示例
para ={ 
"func": "importf", 
"name": "股债策略组合", 
"portfid": 161390, 
"content": [ 
[ 
"交易日期", 
"证券代码", 
"业务类型", 
"数量", 
"价格", 
"成交金额", 
"费用", 
"证券类型" 
], 
[ 
"2020-03-30", 
"CNY", 
"现金存入", 
"", 
"", 
10000000, 
"", 
"" 
], 
[ 
"2020-04-01", 
"600000.SH", 
"买入", 
100, 
10.09, 
1009, 
5.225, 
"A股" 
], 
] 
}
2.文件导入
通过文件对象的形式提交，来实现组合导入。
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必须	value	示例	
功能名称	func	是	fileimport	"func": "fileimport"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
组合文件	file	是	文件对象	file:{本地文件}	
示例（python）
para = { 
"func":"fileimport", 
"name":"股债策略组合"， 
"portid":161930, 
"file":"股债策略组合内容.xlsx" 
} 
# file_object为待导入组合的文件对象 
files = {"file":("股债策略组合内容.xlsx", open("C:\demo\股债策略组合内容.xlsx", 'rb))}
3.状态查询
适用于大文件导入、导入历史持仓计算量较大的组合导入时，查询导入状态。
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必须	value	示例	
功能名称	func	是	fileimport	"func": "query_commit"	
组合ID	portfid	是		"portfid": 161390	
组合文件	jobid	是	文件导入后返回	"jobid":21	
示例（python）
para = { 
"func":"query_commit", 
"portid":161930, 
"jobid":21 
}
现金存取
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必须	value	示例	
功能名称	func	是	cashacs	"func": "cashacs"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
功能参数	functionpara	是		"functionpara": {	
"acesscls": "101",					
"amount": "10000"					
}					
functionpara说明
名称	key	value	省略时	
存取类型	acesscls	存入-不计入收益：101； 	取出-不计入收益：102	不能省略	
现金数额	amount		不能省略	
示例
para = { 
"func": "cashacs", 
"name": "bldptf5", 
"portfid": 161390, 
"functionpara": { 
"acesscls": "101", 
"amount": "10000" 
} 
}
普通交易
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必须	value	示例	
功能名称	func	是	deal	"func": "deal"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
功能参数	functionpara	是			
functionpara说明
名称	key	value	省略时	
行情代码	thscode		不能省略	
交易方向	direct	买入：buy；卖出：sell	不能省略	
标的名称	codeName		不能省略	
交易市场	marketCode		不能省略	
标的类型	securityType		不能省略	
成交价格	price		不能省略	
成交数量	volume		不能省略	
结算货币	currency		不能省略	
费用	fee		不能省略	
费率	feep		不能省略	
汇率	rate		不能省略	
分红方式	bonus	适用基金，现金分红：1；红利再投资：2		
示例
para = { 
"func": "deal", 
"name": "股债策略组合", 
"portfid": 161390, 
"functionpara": { 
"thscode": "300033", 
"direct": "buy", 
"codeName": "同花顺", 
"marketCode": "212100", 
"securityType": "001001", 
"price": 78.7, 
"volume": 100, 
"currency": "CNY", 
"fee": "0", 
"feep": 0, 
"rate": "1.00", 
"bonus": "" 
} 
}
交易流水
目前支持最大时间区间为7天
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是	否	必	须	value	示例	
功能名称	func	是	query_exchange_records	"func":	"query_exchange_records"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
指标	indicators	是		"indicators":	"date,code,name,dealPrice“	
开始时间	startdate	是		"startdate": "2022-10-18"	
结束时间	enddate	是		"enddate": "2022-10-20"	
功能参数	functionpara	否		"functionpara": {	
"keyword":	""}					
indicators说明
指标名称	英文名称	备注	
交易日期	date		
证券代码	code		
证券简称	name		
成交价格	dealPrice		
成交数量	dealNumber		
发生金额	realPrice		
业务名称	businessName		
手续费	serviceCharge		
证券类型	type		
币种	currency		
汇率	exchangeRate		
市场	marketName		
备注信息	importType		
functionpara说明
名称	key	value	省略时	
关键字	keyword		默认为空	
示例
para = { 
"func": "query_exchange_records", 
"name": "股债策略组合", 
"portfid": 161390, 
"indicators": 
"date,code,name,dealPrice,dealNumber,realPrice,businessName,serviceCharge,type,currenc 
y,exchangeRate,marketName,importType", 
"startdate": "2022-10-18", 
"enddate": "2022-10-20", 
"functionpara": { 
"keyword": "" 
} 
}
组合监控
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必须	value	示例	
功能名称	func	是	query_overview	"func": "query_overview"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
指标	indicators	是			
indicators说明
指标名称	英文名称	备注	
资产分类	category		
证券代码	thscode		
证券简称	stockName		
最新价格	newPrice		
涨跌	increase		
涨跌幅	increseRate		
持仓数量	number		
持仓市值	marketValue		
最新权重	weight		
当日盈亏	todayProfit		
浮动盈亏	floatProfit		
浮动盈亏率	floatProfitRate		
累计盈亏	totalProfit		
累计盈亏率	totalProfitRate		
分红派息	interestIncome		
已实现盈利	realizedProfit		
成本价格	positionPrice		
持仓成本	positionCost		
保本价格	breakevenPrice		
手续费	serviceCharge		
币种	moneyType		
汇率	currentPrice		
更新时间	updateTime		
示例
para = { 
"func": "query_overview", 
"name": "股债策略组合", 
"portfid": 161390, 
"indicators": 
"category,thscode,stockName,newPrice,increase,increaseRate,number,marketValue,weight,t 
odayProfit,floatProfit,floatProfitRate,totalProfit,totalProfitRate,interestIncome,real 
izedProfit,positionPrice,positionCost,breakevenPrice,serviceCharge,moneyType,currentPr 
ices,updateTime" 
}
持仓分析
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否必	须	value	示例	
功能名称	func	是	query_positions	"func":	"query_positions"	
组合名称	name	否		"name": "股债策略组	合"	
组合ID	portfid	是		"portfid": 161390	
指标	indicators	是			
功能参数	functionpara	是		"functionpara": {	
"penetrate":	"false"}					
indicators说明
指标名称	英文名称	备注	
证券类型	categoryName		
证券名称	securityName		
证券代码	thsCode		
权重	weight		
持仓市值	marketPrice		
持仓成本	cost		
浮动盈亏	wavepl		
累计收益	cumpl		
收盘价	price		
涨跌幅	increaseRate		
持仓数量	amount		
持仓成本价	costPrice		
functionpara说明
名称	key	value	省略时	
是否穿透	penetrate	不穿透：false； 	穿透：true	不能省略	
示例
para = { 
"func": "query_positions", 
"name": "股债策略组合", 
"portfid": 161390, 
"indicators": 
"categoryName,securityName,thsCode,weight,marketPrice,cost,wavepl,cumpl,price,increase 
Rate,amount,costPrice", 
"date": "2022-10-19", 
"functionpara": { 
"penetrate": "false" 
} 
}
绩效指标
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是否	必须	value	示例	
功能名称	func	是	query_perform	"func": "cashacs"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
日期	date	是		适用于当日实时，"date":	"2020-06-02"；	
开始日期	startdate	是		开始日期适用于区	间"startdate": "2020-06-02"	
结束日期	enddate	是		开始日期适用于区	间"enddate": "2020-06-02"	
业绩基准	performbm	是		"performbm": "000300"	
功能参数	functionpara	是		"functionpara": {	
"pfclass":	"utnv",					
"cycle":	"day"					
}					
functionpara说明
名称	key	value	省略时	
业绩类型	pfclass	业绩表现：perform 	净资产：nasset 	组合净值：utnv	不能省略	
周期	cycle	当日实时:rquota 	日:day 	周:week 	月:month 	半年:halfYear 	年:year	不能省略	
示例
para = { 
"func": "query_perform", 
"name": "股债策略组合", 
"portfid": 161390, 
"performbm": "000300", 
"startdate": "2020-06-02", 
"enddate": "2022-10-20", 
"functionpara": { 
"pfclass": "utnv", 
"cycle": "day" 
} 
}
风险指标
URL
https://quantapi.51ifind.com/api/v1/portfolio_manage
formData
名称	key	是	否	必	须	value	示例	
功能名称	func	是	query_risk_profits	"func": "query_risk_profits"	
组合名称	name	否		"name": "股债策略组合"	
组合ID	portfid	是		"portfid": 161390	
指标	indicators	是		"indicators": [	"alpha,yield,annual_yield,sharpe_r	
开始日期	startdate	是		"startdate": "2021-10-19"	
结束日期	enddate	是		"enddate": "2022-10-19"	
功能参数	functionpara	是		"functionpara": {	
"cycle":	"day",					
"benchmark":	"000300"}					
indicators说明
指标名称	英文名称	备注	
ALPHA	ALPHA		
累计收益	yield		
年化收益	annual_yield		
夏普比率	sharpe_ratio		
信息比率	information_ratio		
索提诺比率	sortino_ratio		
詹森阿尔法	jensen_alpha		
特雷诺比率	treynor_ratio		
胜率	win_ratio		
正收益期数	positiveMonth		
BETA	beta		
年化波动率	annual_volatility		
跟踪误差	tracking_error		
下行风险	downside_risk		
在险价值	value_at_risk		
最大回撤	max_drawdown		
最大回撤形成期	maxdrawdownRepairNum		
最大回撤修复期	maxdownNum		
连续下跌最大幅度	max_cont_decline		
R-square	rSquare		
functionpara说明
名称	key	value	省略时	
数据频率	cycle	日:day;周:week;月:month;季:season;年:year	不能省略	
计算基准	benchmark		不能省略	
示例
para = { 
"func": "query_risk_profits", 
"name": "股债策略组合", 
"portfid": 161390, 
"indicators": [ "alpha,yield,annual_yield,sharpe_ratio"], 
"startdate": "2021-10-19", 
"enddate": "2022-10-19", 
"functionpara": { 
"cycle": "day", 
"benchmark": "000300" 
} 
}
问财函数
URL
https://ft.10jqka.com.cn/api/v1/smart_stock_picking
formData
key	key是否必须	value	示例	
searchstring	是	搜索关键词	"searchstring":"个股热度"	
searchtype	是	搜索类别	"searchtype":"stock"	
示例
para = 
{ 
 "searchstring":"个股热度",  
 "searchtype":"stock" 
}
基金实时估值
URL
https://ft.10jqka.com.cn/api/v1/fund_valuation
formData
key	key	是	否	必	须	value	示例	
codes	是	半角	逗号	分隔	的所	有代	码	"codes":"000001.OF,000003.OF"	
functionpara	是	key-	value	的参	数	见下方表格	
outputpara	是	半角	逗号	分隔	的	Y/N	来控	制是	否显	示该	字段	"changeRatioValuation:Y,realTimeValuation:Y,Deviation30TDay	
functionpara参数说明
名称	keys	value说明	省略时逻辑	
仅返回最新估	值	onlyLastest	1-仅返回最新估值 0-返回时间区间	估值	不能省略	
开始时间	beginTime		仅返回最新估值可省	略	
结束时间	endTime		仅返回最新估值可省	略	
outputpara说明
字段名称	字段中文	
changeRatioValuation	估值涨跌幅	
realTimeValuation	基金实时估值	
Deviation30TDays	30交易日估算平均偏差（%）	
rank	请求基金最新估值涨跌幅排名	
示例
para = 
{ 
 "codes":"000001.OF,000003.OF",  
 "functionpara":{ 
 "onlyLastest":"0", 
 "beginTime":"2021-08-24 09:15:00", 
 "endTime":"2021-08-24 15:15:00" 
 }, 
 "outputpara":"date:Y,thscode:Y,security_name:Y,weight:Y" 
}
基金实时估值日函数
URL
https://ft.10jqka.com.cn/api/v1/final_fund_valuation
formData
key	key	是	否	必	须	value	示例	
codes	是	半角逗号分隔的所	有代码	"codes":"000001.OF,000003.OF"	
functionpara	是	key-value的参数，	包括开始日期	beginDate，截止	日期endDate	见下方示例	
outputpara	是	半角逗号分隔的	Y/N来控制是否显	示该字段	"finalValuation:Y,netAssetValue:Y,deviation:Y"	
outputpara说明
字段名称	字段中文	
finalValuation	日最终估值	
netAssetValue	日实际净值	
deviation	3估值相对净值偏差率（%）	
示例
para = 
{ 
 "codes":"000001.OF;000003.OF",  
 "functionpara":{ 
 "beginDate":"2021-06-01", 
 "endDate":"2021-09-02" 
 }, 
 "finalValuation:Y,netAssetValue:Y,deviation:Y" 
}
日期查询函数
URL
https://ft.10jqka.com.cn/api/v1/get_trade_dates
formData
key	key是	否必	须	value	示例	
marketcode	是	见下方说明	"marketcode":"212001"	
functionpara	是	key-value的参数	见下方代码块	
startdate	是	开始日期，支持”YYYYMMDD"”YYYY-	MM-DD"”YYYY/MM/DD"三种时间格式	"startdate":"2018-01-	01"	
enddate	是	结束日期，支持”YYYYMMDD"”YYYY-	MM-DD"”YYYY/MM/DD"三种日期格式	"enddate":"2018-01-	01"	
marketcode说明
交易所代码	交易所名称	
212001	上交所	
212100	深交所	
212200	港交所	
212020001	中国金融期货交易所	
212020002	上海黄金交易所	
212020003	郑州商品交易所	
212020004	大连商品交易所	
212004	银行间债券市场	
212005	代办转让市场	
212020006	伦敦金属交易所(LME)	
212020007	纽约商业期货交易所(NYMEX)	
212020008	上海期货交易所	
212020010	纽约商品交易所(COMEX)	
212020011	纽约期货交易所(NYBOT)	
212020012	芝加哥商品交易所(CBOT)	
212020013	洲际交易所(ICE)	
212020014	马来西亚衍生品交易所	
212020015	芝加哥商业交易所(CME)	
212010	美国纽约证券交易所	
212011	美国NASDAQ证券交易所	
212049	美国证券交易所	
212050	NYSE Arca	
212012	英国伦敦证券交易所	
212013	新加坡证券交易所	
212014	荷兰阿姆斯特丹证券交易所	
交易所代码	交易所名称	
212015	挪威奥斯陆证券交易所	
212016	澳大利亚证券交易所	
212017	法国巴黎证券交易所	
212018	比利时布鲁塞尔证券交易所	
212020016	天津贵金属交易所	
212024	德国法兰克福证券交易所	
212025	日本东京证券交易所	
212026	加拿大多伦多证券交易所	
212027	韩国证券交易所	
212029	马来西亚吉隆坡证券交易所	
212031	马德里证券交易所	
212033	墨西哥证券交易所	
212035	瑞士证券交易所	
212036	巴西圣保罗证券期货交易所	
212037	瑞典斯德哥尔摩证券交易所	
212039	台湾证券交易所	
212040	泰国证券交易所	
212041	奥地利维也纳证券交易所	
212045	意大利米兰证券交易所	
212047	印度尼西亚证券交易所	
212051	美国IEX证券交易所	
212053	新西兰证券交易所	
212055	美国OTC市场	
212061	菲律宾证券交易所	
212062	孟买证券交易所	
交易所代码	交易所名称	
212063	布宜诺斯艾利斯证券交易所	
212203	特拉维夫证券交易所	
212205	莫斯科证券交易所	
212210	BATS交易所	
functionpara说明
	对应字段	字段类型	是否可省略	命令生成示例说明	
函数模式	inputpara	字符串	不可	查询区间日期 "mode":"1" 	查询区间日期数目 "mode":"2"	
日期类型	inputpara	字符串	不可	交易日 "dateType":"0" 	日历日 "dateType":"1"	
日期格式	inputpara	字符串	不可	YYYY-MM-DD "dateFormat":"0" 	YYYY/MM/DD "dateFormat":"1" 	YYYYMMDD "dateFormat":"2"	
时间周期	inputpara	字符串	不可	日 "period":"D" 	周 "period":"W" 	月 "period":"M" 	季 "period":"Q" 	半年 "period":"S" 	年 "period":"Y"	
时间周期偏移	inputpara	字符串	不可	时间周期正数第1日 	"periodnum":"1" 	时间周期倒数第1日 	"periodnum":"-1"	
示例
para = 
{ 
 "marketcode":"212001", 
 "functionpara":{ 
 "mode":"1", 
 "dateType":"0", 
 "period":"D", 
 "dateFormat":"0" 
 }, 
 "startdate":"2018-01-01", 
 "enddate":"2018-01-01" 
}
日期偏移函数
URL
https://ft.10jqka.com.cn/api/v1/get_trade_dates
formData
key	key是	否必	须	value	示例	
marketcode	是	见日期查询函数说明	"marketcode":"212001"	
functionpara	是	key-value的参数	见下方代码块	
basedate	是	基准日期，支持”YYYYMMDD"”YYYY-	MM-DD"”YYYY/MM/DD"三种时间格式	"basedate":"2018-01-	01"	
functionpara说明
	对应字段	字段	类型	是否可	省略	省略时	逻辑	命令生成示例说明	
日期类型	inputpara	字符	串	不可		交易日 "dateType":"0" 	日历日 "dateType":"1"	
日期格式	inputpara	字符	串	不可		YYYY-MM-DD	"dateFormat":"0" 	YYYY/MM/DD	"dateFormat":"1" 	YYYYMMDD	"dateFormat":"2"	
前推后退	inputpara	字符	串	不可		前推 "offset":"-5" 	后推 "offset":"5"	
时间周期	inputpara	字符	串	不可		日 "period":"D" 	周 "period":"W" 	月 "period":"M" 	季 "period":"Q" 	半年 "period":"S" 	年 "period":"Y"	
时间周期内	偏移	inputpara	字符	串	可	默认	默认 省略 	时间周期正数第1日	"periodnum":"1" 	时间周期倒数第1日	"periodnum":"-1"	
输出选项	inputpara	字符	串	不可		所有日期	"output":"sequencedate" 	单个日期	"output":"singledate"	
示例
para = 
{ 
 "marketcode":"212001", 
 "functionpara":{ 
 "mode":"1", 
 "dateType":"0", 
 "period":"D", 
 "dateFormat":"0“ 
 }, 
 "startdate":"2018-01-01", 
 "enddate":"2018-01-01" 
}
数据量查询
无需参数，仅需要传入token访问url即可
URL
https://quantapi.51ifind.com/api/v1/get_data_volume
示例
Headers = 
{"Content-Type":"application/json", 
"access_token":"xxxxxxxxxx" 
}
错误信息查询
URL
https://quantapi.51ifind.com/api/v1/get_error_message
示例
para = { 
 "errorcode":0 
}
转同花顺代码
URL
https://quantapi.51ifind.com/api/v1/get_thscode
formData
key	key是否必须	value	示例	
seccode/secname	是	行情代码/简称	"seccode":"000001"	
mode	是	seccode/secname	"mode":"seccode"	
sectype	是	证券类型	"sectype":"001"	
market	是	市场	"market":"212001"	
tradestatus	是	0，1，2	"tradestatus":"0"	
isexact	是	0，1	"isexact":" 1"	
示例
para = { 
"mode":"secname", 
"sectype":"001", 
"market":"212001", 
"tradestatus":"1", 
"isexact":"1" 
}
错误说明
错误	码	错误信息	错误提示	
-1010	your account has been loggout out.	token已失效	
-1000	datasvr error!		
-1001	gwsvr error!		
-1002	timeout!	超时	
-1003	access-token can not be empty!		
-1004	datasvrhq error!		
-1005	auth user error!		
-1201	failed,please change your input condition.		
-1202	there are errors in your parameters,please have a check.		
-1203	parsing failed.		
-4001	no data.	数据为空	
-4100	please log in first!	请先登录iFind	
-4101	database execution error	数据库执行错误	
-4102	server internal error.		
-4201	the data server is incorrect	数据服务器取值错误	
-4203	request format is wrong	请求格式错误	
-4204	wrong time format	错误的时间格式	
-4205	the start time can not be greater than the end time	开始时间不能大于结	束时间	
-4206	include the wrong thscode	含有错误的同花顺代	码	
-4207	sorry,currently we do not support bonds of this market.		
-4208	sorry, currently we just support kinds of securities of SSE,	SZSE and CFFEX.		
-4209	sorry, the startDate and endDate of Shopshot command	should be the same, please have a check.		
错误	码	错误信息	错误提示	
-4210	error happen with input parameters, please have a check.		
-4211	sorry, there is no trading date in the date range, please	have a check	时间区间内无交易日	
-4212	sorry, the input endDate is earlier than the listDates of the	input security codes	时间区间内股票未上	市	
-4213	sorry, startDate can't later than endDate in the command,	please have a check	开始日期大于截止日	期	
-4301	sorry, your usage of basic data has exceeded 5 million this	week.		
-4302	sorry, your usage of quote data has exceeded 150 million	this week.		
-4303	sorry, your usage of EDB data has exceeded 5 million this	week.		
-4317	sorry, your usage of data has exceeded 1w this week.		
-4318	sorry, your usage of data has exceeded this month.		
-4320	sorry, your account must use the corresponding.	抱歉，您的账户必须	使用对应客户端	
-4321	sorry, your usage of data has exceeded 100 this month.		
-4304	sorry, the HighFrequeceSequence command can support 	requiring 200W data at most, please modify your input 	params	单条命令请求数据量	过大	
-4305	sorry, the BasicData command can support requiring 20W	data at most, please modify your input params	单条命令请求数据量	过大	
-4306	sorry, the Snapshot command can support requiring	200W data at most, please modify your input params	单条命令请求数据量	过大	
-4319	sorry, the free Acount can support requiring 5W data at	most, please modify your input params	免费用户单条命令请	求数据量过大	
-4321	sorry, the free Acount can support requiring 10W data at	most, please modify your input params	免费用户单条命令请	求数据量过大	
错误	码	错误信息	错误提示	
-4322	sorry, the free Acount can support requiring 1W data at	most, please modify your input params	免费用户单条命令请	求数据量过大	
-4307	data extraction is overrun.		
-4308	the range between startDate and endDate must be 	smaller than 1 month. Please check your input 	parameters.	请求区间不能超过一	个月	
-4309	sorry, trial account can get 1 year data for authority 	limited, so as to acquire more data, please transfer it to 	formal account	超出时间限制	
-4310	sorry, trial account can get 1 month data for authority 	limited, so as to acquire more data, please transfer it to 	formal account	超出时间限制	
-4311	sorry, trial account can get 5 year data for authority 	limited, so as to acquire more data, please transfer it to 	formal account	超出时间限制	
-4312	sorry, the HistoryQuotes command can support requiring	200W data at most, please modify your input params	超出200W限制	
-4313	sorry,the interval should be smaller than 3 years,please	change your startDate or endDate.		
-4314	sorry,the interval should be smaller than 6 months,please	change your startDate or endDate.		
-4315	sorry,the interval should be smaller than 3 months,please	change your startDate or endDate.		
-4316	sorry,the interval should be smaller than 1 year,please	change your startDate or endDate.		
-4400	sorry, we just support 600 requests per minute.		
-5001	sorry,data server parameter error.	请求远程服务器参数	错误	
-5002	sorry,data server is busy now.	查询失败	
错误	码	错误信息	错误提示	
-5003	sorry,does not support the stock box selection calculation.	不支持该股权查询	
-5004	sorry,data process waiting timeout.	等待超时	
-5005	sorry, data calculation error.	计算错误	
-5006	sorry,data process query failed.	查询失败	
-5007	sorry,data process Waiting for calculation.	等待计算	
-5008	sorry,data process calculating.	正在计算	
-5009	sorry,must complete the last instruction request.	必须完成上一次计算	请求	
-5010	sorry,only supports single code incoming.	仅支持单代码传入	
-5100	Sorry,account type is not supported.	抱歉，您的账户类型	不支持	
-5101	Please confirm,you have not used the amount of date for	the month.	请确认，您尚未使用	本月的数据量	
-5102	Sorry,you have exceeded the maximum number of cleaes.	抱歉，您已超过最大	清零次数	
-5103	Sorry,Do not allow accounts to operate in unbound mac	code environments.	抱歉，不允许账户在	非绑定mac代码环境	中运行	
-5104	Sorry,this mac code has been bound .	抱歉，该机器的mac	已被绑定	
-5000	please enter a reasonable expected dividend growth rate	请输入合理的预期红	利增长率数值	
适用范围
本接口规范适用于同花顺数据接口与服务商端接口 同花顺公司保留本接口最终解释权利
版本管理
版本信息体现在各函数的url中，新版本版本号逐渐向上累加，旧版本在有用户使用情况下保持不
变
