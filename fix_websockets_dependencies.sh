#!/bin/bash

echo "🔧 修复websockets依赖问题..."

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate financial-mcp

# 检查当前websockets版本
echo "📦 当前websockets版本:"
pip show websockets | grep Version

# 方案1: 降级websockets到稳定版本
echo "⬇️ 降级websockets到稳定版本..."
pip install "websockets>=11.0,<12.0"

# 方案2: 升级uvicorn到最新版本
echo "⬆️ 升级uvicorn到最新版本..."
pip install --upgrade "uvicorn[standard]>=0.24.0"

# 方案3: 安装兼容的fastmcp版本
echo "🔄 确保fastmcp版本兼容..."
pip install --upgrade fastmcp

echo "✅ 依赖修复完成！"

# 验证修复
echo "🧪 验证修复结果..."
python -c "
import warnings
warnings.filterwarnings('ignore', category=DeprecationWarning)

try:
    import websockets
    import uvicorn
    import fastmcp
    print('✅ 所有依赖导入成功')
    print(f'websockets版本: {websockets.__version__}')
    print(f'uvicorn版本: {uvicorn.__version__}')
    print(f'fastmcp版本: {fastmcp.__version__}')
except Exception as e:
    print(f'❌ 导入失败: {e}')
"

echo "🎯 修复完成！现在可以启动服务器了。"
