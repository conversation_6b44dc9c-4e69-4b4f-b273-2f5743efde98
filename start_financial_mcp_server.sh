#!/bin/bash

# 金融分析MCP服务器启动脚本
# 支持SSE方式运行，提供更好的性能和并发支持

echo "🚀 启动金融分析MCP服务器..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3"
    exit 1
fi

# 检查虚拟环境（如果使用conda）
if [[ "$CONDA_DEFAULT_ENV" == "AZ091" ]]; then
    echo "✅ 使用conda环境: $CONDA_DEFAULT_ENV"
else
    echo "⚠️  警告: 未检测到AZ091环境，请确保已激活正确的conda环境"
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 检查必要的环境变量
if [[ -z "$IFIND_REFRESH_TOKEN" ]]; then
    echo "⚠️  警告: 未设置IFIND_REFRESH_TOKEN环境变量"
    echo "请在.env文件中设置或通过环境变量设置"
fi

# 检查依赖包
echo "📦 检查依赖包..."
python3 -c "import fastapi, uvicorn, fastmcp" 2>/dev/null
if [[ $? -ne 0 ]]; then
    echo "❌ 缺少必要依赖包，正在安装..."
    pip install -r financial_mcp_requirements.txt
fi

# 创建日志目录
mkdir -p logs

# 启动服务器
echo "🌐 启动SSE服务器在端口8080..."
echo "📡 SSE端点: http://localhost:8080/mcp/sse"
echo "🏥 健康检查: http://localhost:8080/health"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================="

# 启动服务器，支持热重载（开发模式）
python3 financial_mcp_server_sse.py

# 或者使用uvicorn直接启动（生产模式）
# uvicorn financial_mcp_server_sse:app --host 0.0.0.0 --port 8080 --reload
