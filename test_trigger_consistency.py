#!/usr/bin/env python3
"""
触发策略一致性测试
验证MCP服务器的触发策略是否与原项目保持一致
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, '/mnt/e/AI/financial-mcp-server')

def test_keyword_recognition():
    """测试关键词识别功能"""
    print("🔑 测试关键词识别功能...")
    
    try:
        from server import calculate_financial_confidence
        
        # 测试用例（与原项目tool_selector.py保持一致）
        test_cases = [
            # 高置信度查询（应该推荐）
            ("查询贵州茅台的股票行情", True, 0.8),
            ("600519.SH的实时股价", True, 0.9),
            ("分析五粮液的MACD技术指标", True, 0.8),
            ("获取比亚迪的财务报表数据", True, 0.7),
            ("查看招商银行的市盈率和市净率", True, 0.7),
            
            # 中等置信度查询（边界情况）
            ("股票市场怎么样", True, 0.6),
            ("今天行情如何", True, 0.6),
            
            # 低置信度查询（不应推荐）
            ("今天天气怎么样", False, 0.0),
            ("帮我写一段代码", False, 0.0),
            ("什么是人工智能", False, 0.0),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for query, should_recommend, min_confidence in test_cases:
            result = calculate_financial_confidence(query)
            actual_recommend = result["recommended"]
            actual_confidence = result["confidence"]
            
            # 检查推荐结果
            recommend_correct = actual_recommend == should_recommend
            # 检查置信度（允许一定误差）
            confidence_correct = actual_confidence >= min_confidence - 0.1
            
            if recommend_correct and confidence_correct:
                print(f"✅ '{query}' -> 推荐:{actual_recommend}, 置信度:{actual_confidence:.2f}")
                passed += 1
            else:
                print(f"❌ '{query}' -> 期望推荐:{should_recommend}, 实际:{actual_recommend}, 置信度:{actual_confidence:.2f}")
        
        print(f"\n📊 关键词识别测试: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"❌ 关键词识别测试失败: {e}")
        return False

def test_stock_code_extraction():
    """测试股票代码提取功能"""
    print("\n🏷️ 测试股票代码提取功能...")
    
    try:
        from tools.base_tool import FinancialBaseTool
        tool = FinancialBaseTool("test")
        
        # 测试用例
        test_cases = [
            ("查询600519.SH的行情", "600519.SH"),
            ("分析000858.SZ和002594.SZ", "000858.SZ"),  # 取第一个
            ("贵州茅台的股价", "600519.SH"),
            ("五粮液和比亚迪的对比", "000858.SZ"),  # 取第一个映射
            ("平安银行怎么样", "000001.SZ"),
            ("招行的技术指标", "600036.SH"),
            ("腾讯控股的财报", "00700.HK"),
            ("没有股票信息的查询", ""),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for query, expected in test_cases:
            result = tool.extract_stock_codes(query)
            if result == expected:
                print(f"✅ '{query}' -> {result}")
                passed += 1
            else:
                print(f"❌ '{query}' -> 期望:{expected}, 实际:{result}")
        
        print(f"\n📊 股票代码提取测试: {passed}/{total} 通过")
        return passed >= total * 0.8  # 允许20%的误差
        
    except Exception as e:
        print(f"❌ 股票代码提取测试失败: {e}")
        return False

def test_query_type_detection():
    """测试查询类型检测功能"""
    print("\n🔍 测试查询类型检测功能...")
    
    try:
        from tools.base_tool import FinancialBaseTool
        tool = FinancialBaseTool("test")
        
        # 测试用例
        test_cases = [
            ("查询贵州茅台的实时行情", "real_time"),
            ("获取五粮液的最新股价", "real_time"),
            ("分析比亚迪的MACD指标", "technical_indicators"),
            ("查看招商银行的RSI和KDJ", "technical_indicators"),
            ("获取平安银行的历史数据", "history"),
            ("查看宁德时代过去一个月的走势", "history"),
            ("分析腾讯的财务报表", "financial_report"),
            ("获取阿里巴巴的财务数据", "financial_report"),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for query, expected in test_cases:
            result = tool.detect_query_type(query)
            if result == expected:
                print(f"✅ '{query}' -> {result}")
                passed += 1
            else:
                print(f"❌ '{query}' -> 期望:{expected}, 实际:{result}")
        
        print(f"\n📊 查询类型检测测试: {passed}/{total} 通过")
        return passed >= total * 0.8  # 允许20%的误差
        
    except Exception as e:
        print(f"❌ 查询类型检测测试失败: {e}")
        return False

async def test_smart_recommendation():
    """测试智能推荐功能"""
    print("\n🤖 测试智能推荐功能...")
    
    try:
        from server import analyze_query_intent
        
        # 测试用例
        test_cases = [
            ("查询贵州茅台的股票行情", True),  # 应该推荐
            ("分析五粮液的技术指标", True),   # 应该推荐
            ("今天天气怎么样", False),        # 不应推荐
            ("帮我写代码", False),            # 不应推荐
        ]
        
        passed = 0
        total = len(test_cases)
        
        for query, should_recommend in test_cases:
            result = await analyze_query_intent(query)
            
            # 检查是否包含推荐信息
            has_recommendation = "💡 检测到您可能需要专业金融数据" in result
            
            if has_recommendation == should_recommend:
                print(f"✅ '{query}' -> 推荐正确")
                passed += 1
            else:
                print(f"❌ '{query}' -> 期望推荐:{should_recommend}, 实际:{has_recommendation}")
        
        print(f"\n📊 智能推荐测试: {passed}/{total} 通过")
        return passed >= total * 0.8
        
    except Exception as e:
        print(f"❌ 智能推荐测试失败: {e}")
        return False

def test_mcp_tools_availability():
    """测试MCP工具可用性"""
    print("\n🛠️ 测试MCP工具可用性...")
    
    try:
        import server
        
        # 检查所有必需的工具
        required_tools = [
            "get_stock_real_time",
            "get_stock_history", 
            "get_stock_basic_data",
            "get_financial_report",
            "analyze_technical_indicators",
            "financial_smart_query",
            "analyze_query_intent"  # 新增的推荐工具
        ]
        
        available_tools = 0
        total_tools = len(required_tools)
        
        for tool_name in required_tools:
            if hasattr(server, tool_name) or hasattr(server.mcp_server, tool_name):
                print(f"✅ {tool_name} 工具可用")
                available_tools += 1
            else:
                print(f"❌ {tool_name} 工具不可用")
        
        print(f"\n📊 MCP工具可用性: {available_tools}/{total_tools}")
        return available_tools == total_tools
        
    except Exception as e:
        print(f"❌ MCP工具可用性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 触发策略一致性测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("关键词识别测试", test_keyword_recognition),
        ("股票代码提取测试", test_stock_code_extraction),
        ("查询类型检测测试", test_query_type_detection),
        ("MCP工具可用性测试", test_mcp_tools_availability),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 异步测试
    print(f"\n🔬 运行 智能推荐测试...")
    try:
        if asyncio.run(test_smart_recommendation()):
            passed += 1
            total += 1
            print(f"✅ 智能推荐测试 通过")
        else:
            total += 1
            print(f"❌ 智能推荐测试 失败")
    except Exception as e:
        total += 1
        print(f"❌ 智能推荐测试 异常: {e}")
    
    # 最终结果
    print("\n" + "=" * 60)
    print(f"📊 触发策略一致性测试结果: {passed}/{total} 通过")
    
    if passed >= total * 0.8:  # 80%通过率
        print("\n🎉 **触发策略一致性验证成功！**")
        print("\n✨ **一致性特点**:")
        print("  🔑 关键词识别逻辑与原项目完全一致")
        print("  🏷️ 股票代码提取功能保持一致")
        print("  🔍 查询类型检测算法一致")
        print("  🤖 智能推荐机制完整移植")
        print("  🛠️ 所有MCP工具正常可用")
        
        print("\n📋 **使用方式**:")
        print("  1. 配置Agent-Zero MCP客户端关键词")
        print("  2. 启动MCP服务器: ./start_server.sh")
        print("  3. 使用与原项目相同的查询语句")
        
        return True
    else:
        print("⚠️  触发策略存在不一致，需要进一步调整")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
