# Enhanced Search Engine MCP移植规划文档

## 📋 **项目概述**

将Agent-Zero项目中的Enhanced Search Engine工具移植到SearXNG项目中，实现独立的MCP服务，提供高性能的增强搜索功能。

### **移植目标**
- 将Enhanced Search Engine从Agent-Zero工具转换为独立MCP服务
- 集成到现有SearXNG项目中，复用其搜索基础设施
- 提供标准MCP协议接口，支持多客户端访问
- 保持原有功能的完整性并增强性能

## 🎯 **可行性分析**

### **✅ 移植优势**

1. **技术兼容性**
   - Enhanced Search Engine已经使用SearXNG作为搜索后端
   - 现有代码结构清晰，依赖关系明确
   - 异步架构与MCP协议天然匹配

2. **功能独立性**
   - 搜索逻辑完全独立，无Agent-Zero强依赖
   - 可独立部署和扩展
   - 适合作为微服务运行

3. **性能提升潜力**
   - 独立进程避免Agent主进程阻塞
   - 可实现搜索结果缓存
   - 支持并发多查询处理

### **⚠️ 技术挑战**

1. **依赖解耦**
   - 需要移除Agent-Zero特定依赖
   - 重构工具基类继承关系
   - 适配MCP协议接口

2. **配置管理**
   - SearXNG配置集成
   - 环境变量和设置管理
   - 日志和监控集成

## 🏗️ **架构设计**

### **整体架构**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │───▶│  Enhanced Search │───▶│    SearXNG      │
│  (Agent-Zero)   │    │   MCP Server     │    │   (localhost:   │
│                 │    │  (Port: 8881)    │    │    8888)        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  Search Strategy │
                       │   Engine         │
                       │ • Basic Search   │
                       │ • Expanded Search│
                       │ • Related Search │
                       │ • Quality Score  │
                       │ • Smart Summary  │
                       └──────────────────┘
```

### **核心组件**

1. **MCP服务器** (`enhanced_search_mcp_server.py`)
   - FastMCP框架实现
   - HTTP/SSE双协议支持
   - 工具注册和管理

2. **搜索引擎** (`enhanced_search_engine.py`)
   - 多轮搜索策略
   - 结果质量评估
   - 智能摘要生成

3. **SearXNG集成** (`searxng_adapter.py`)
   - SearXNG API封装
   - 连接池管理
   - 错误处理和重试

4. **配置管理** (`config.py`)
   - 环境变量管理
   - SearXNG配置集成
   - 日志配置

## 📁 **项目结构设计**

```
E:\AI\searxng\
├── enhanced_search_mcp/          # 新增MCP服务目录
│   ├── __init__.py
│   ├── server.py                 # MCP服务器主入口
│   ├── enhanced_search_engine.py # 增强搜索引擎
│   ├── searxng_adapter.py        # SearXNG适配器
│   ├── config.py                 # 配置管理
│   ├── utils.py                  # 工具函数
│   └── requirements.txt          # MCP服务依赖
├── scripts/
│   ├── start_enhanced_mcp.sh     # MCP服务启动脚本
│   └── setup_mcp_env.sh          # 环境设置脚本
├── config/
│   └── enhanced_mcp_config.yml   # MCP服务配置
└── docs/
    └── enhanced_mcp_setup.md     # 部署文档
```

## 🔧 **技术实现方案**

### **第一阶段：基础移植 (2-3天)**

#### **1.1 创建MCP服务器框架**
```python
# enhanced_search_mcp/server.py
from fastmcp import FastMCP
from fastapi import FastAPI
import uvicorn

mcp_server = FastMCP(
    name="Enhanced Search Engine",
    instructions="专业增强搜索引擎MCP服务器"
)

@mcp_server.tool()
async def enhanced_search(
    query: str,
    search_depth: str = "deep",
    max_results: int = 20
) -> str:
    """执行增强搜索"""
    engine = EnhancedSearchEngine()
    return await engine.execute(query, search_depth, max_results)
```

#### **1.2 移植搜索引擎核心逻辑**
```python
# enhanced_search_mcp/enhanced_search_engine.py
class EnhancedSearchEngine:
    def __init__(self, searxng_adapter):
        self.searxng = searxng_adapter
        
    async def execute(self, query: str, depth: str, max_results: int):
        # 移植原有的三轮搜索逻辑
        basic_results = await self._basic_search(query)
        expanded_results = await self._expanded_search(query)
        related_results = await self._related_search(query)
        
        # 结果整合和质量评估
        integrated = await self._integrate_results(
            basic_results, expanded_results, related_results
        )
        
        # 生成智能摘要
        summary = await self._generate_summary(query, integrated)
        
        return self._format_result(query, summary, integrated[:max_results])
```

#### **1.3 创建SearXNG适配器**
```python
# enhanced_search_mcp/searxng_adapter.py
class SearXNGAdapter:
    def __init__(self, base_url="http://localhost:8888"):
        self.base_url = base_url
        self.session = None
        
    async def search(self, query: str, engines: str = "google,bing,brave"):
        """执行SearXNG搜索"""
        params = {
            'q': query,
            'format': 'json',
            'categories': 'general',
            'engines': engines
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/search", 
                params=params, 
                timeout=30
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"SearXNG search failed: {response.status}")
```

### **第二阶段：功能增强 (2-3天)**

#### **2.1 添加缓存机制**
```python
# enhanced_search_mcp/cache.py
import redis
import json
from datetime import timedelta

class SearchCache:
    def __init__(self, redis_url="redis://localhost:6379"):
        self.redis = redis.from_url(redis_url)
        
    async def get_cached_result(self, query: str):
        """获取缓存的搜索结果"""
        key = f"enhanced_search:{hash(query)}"
        cached = self.redis.get(key)
        if cached:
            return json.loads(cached)
        return None
        
    async def cache_result(self, query: str, result: dict, ttl: int = 3600):
        """缓存搜索结果"""
        key = f"enhanced_search:{hash(query)}"
        self.redis.setex(key, ttl, json.dumps(result))
```

#### **2.2 并发搜索优化**
```python
# enhanced_search_mcp/concurrent_search.py
import asyncio
from typing import List, Dict

class ConcurrentSearchManager:
    def __init__(self, searxng_adapter, max_concurrent=5):
        self.searxng = searxng_adapter
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
    async def parallel_search(self, queries: List[str]) -> List[Dict]:
        """并发执行多个搜索查询"""
        tasks = [self._search_with_semaphore(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = [r for r in results if not isinstance(r, Exception)]
        return valid_results
        
    async def _search_with_semaphore(self, query: str):
        async with self.semaphore:
            return await self.searxng.search(query)
```

#### **2.3 智能结果排序**
```python
# enhanced_search_mcp/ranking.py
class IntelligentRanking:
    def __init__(self):
        self.quality_weights = {
            'title_relevance': 0.3,
            'content_quality': 0.25,
            'source_authority': 0.2,
            'freshness': 0.15,
            'user_engagement': 0.1
        }
        
    def calculate_comprehensive_score(self, result: dict, query: str) -> float:
        """计算综合质量评分"""
        scores = {}
        
        # 标题相关性评分
        scores['title_relevance'] = self._calculate_title_relevance(
            result.get('title', ''), query
        )
        
        # 内容质量评分
        scores['content_quality'] = self._calculate_content_quality(
            result.get('content', '')
        )
        
        # 来源权威性评分
        scores['source_authority'] = self._calculate_source_authority(
            result.get('url', '')
        )
        
        # 计算加权总分
        total_score = sum(
            scores[key] * self.quality_weights[key] 
            for key in scores
        )
        
        return min(total_score, 10.0)  # 限制最高分为10分
```

### **第三阶段：集成部署 (1-2天)**

#### **3.1 Docker集成**
```dockerfile
# enhanced_search_mcp/Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8881

CMD ["python", "server.py"]
```

#### **3.2 Docker Compose配置**
```yaml
# docker-compose.enhanced-mcp.yml
version: '3.8'

services:
  enhanced-search-mcp:
    build: ./enhanced_search_mcp
    ports:
      - "8881:8881"
    environment:
      - SEARXNG_URL=http://searxng:8080
      - REDIS_URL=redis://redis:6379
    depends_on:
      - searxng
      - redis
    networks:
      - searxng-network

  redis:
    image: redis:7-alpine
    networks:
      - searxng-network

networks:
  searxng-network:
    external: true
```

## 📊 **性能优化策略**

### **缓存策略**
- **L1缓存**: 内存缓存，存储最近查询结果 (TTL: 10分钟)
- **L2缓存**: Redis缓存，存储热门查询结果 (TTL: 1小时)
- **L3缓存**: 磁盘缓存，存储历史查询结果 (TTL: 24小时)

### **并发控制**
- 最大并发搜索数: 5个
- 单个查询超时: 30秒
- 连接池大小: 20个连接
- 请求频率限制: 每秒最多10个请求

### **资源管理**
- 内存使用限制: 512MB
- CPU使用限制: 2核心
- 磁盘缓存大小: 1GB
- 日志文件轮转: 每日轮转，保留7天

## 🚀 **部署方案**

### **方案A: 独立部署（推荐）**
```bash
# 1. 创建MCP服务目录
cd /mnt/e/AI/searxng
mkdir enhanced_search_mcp

# 2. 安装依赖
pip install fastmcp fastapi uvicorn aiohttp redis

# 3. 启动服务
python enhanced_search_mcp/server.py
```

### **方案B: Docker集成部署**
```bash
# 1. 构建镜像
docker build -t enhanced-search-mcp ./enhanced_search_mcp

# 2. 启动服务栈
docker-compose -f docker-compose.enhanced-mcp.yml up -d

# 3. 验证服务
curl http://localhost:8881/health
```

## 📋 **实施时间表**

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| **第1天** | 项目结构创建、基础框架搭建 | 1天 | 开发者 |
| **第2-3天** | 核心搜索逻辑移植、SearXNG集成 | 2天 | 开发者 |
| **第4-5天** | 缓存机制、并发优化实现 | 2天 | 开发者 |
| **第6天** | Docker集成、部署脚本编写 | 1天 | 开发者 |
| **第7天** | 测试、文档编写、优化调整 | 1天 | 开发者 |

## ✅ **验收标准**

### **功能验收**
- [ ] MCP服务器正常启动，端口8881可访问
- [ ] 支持enhanced_search工具调用
- [ ] 三轮搜索策略正常工作
- [ ] 结果质量评分和排序正确
- [ ] 智能摘要生成功能正常
- [ ] 缓存机制有效工作
- [ ] 并发搜索性能达标

### **性能验收**
- [ ] 单次搜索响应时间 < 10秒
- [ ] 并发5个查询响应时间 < 15秒
- [ ] 缓存命中率 > 30%
- [ ] 内存使用 < 512MB
- [ ] CPU使用率 < 80%

### **集成验收**
- [ ] Agent-Zero可正常调用MCP服务
- [ ] 搜索结果格式与原工具一致
- [ ] 错误处理和日志记录完善
- [ ] Docker部署成功
- [ ] 文档完整，部署简单

## 🎯 **预期收益**

### **性能提升**
- 搜索响应速度提升 50%
- 支持并发查询，吞吐量提升 300%
- 缓存机制减少重复查询 70%

### **架构优势**
- 服务解耦，独立扩展
- 标准MCP协议，通用性强
- 容器化部署，运维简单

### **功能增强**
- 更智能的结果排序
- 更全面的搜索覆盖
- 更准确的质量评估

## 📝 **总结**

Enhanced Search Engine移植到SearXNG项目作为MCP服务是**完全可行**的，具有以下优势：

1. **技术可行性高**: 现有代码结构清晰，依赖关系简单
2. **性能提升明显**: 独立进程、缓存机制、并发优化
3. **架构更合理**: 服务解耦、标准协议、容器化部署
4. **实施风险低**: 渐进式迁移，功能完整保留

**建议立即开始实施**，预计7天内可完成完整的移植和部署。

## 💻 **核心代码示例**

### **MCP服务器主入口**
```python
# enhanced_search_mcp/server.py
#!/usr/bin/env python3
import asyncio
import logging
import os
from datetime import datetime
from fastmcp import FastMCP
from fastapi import FastAPI
import uvicorn

from enhanced_search_engine import EnhancedSearchEngine
from searxng_adapter import SearXNGAdapter
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建MCP服务器
mcp_server = FastMCP(
    name="Enhanced Search Engine MCP Server",
    instructions="""
    专业增强搜索引擎MCP服务器，提供以下功能：

    🔍 核心功能：
    - 多轮搜索策略（基础+扩展+相关主题）
    - 智能结果质量评估和排序
    - 自动摘要生成
    - 搜索结果缓存优化

    🎯 适用场景：
    - 深度研究和信息收集
    - 全面的主题分析
    - 高质量信息筛选
    - 智能搜索建议
    """
)

# 初始化组件
config = Config()
searxng_adapter = SearXNGAdapter(config.SEARXNG_URL)
search_engine = EnhancedSearchEngine(searxng_adapter, config)

@mcp_server.tool()
async def enhanced_search(
    query: str,
    search_depth: str = "deep",
    max_results: int = 20,
    enable_cache: bool = True
) -> str:
    """
    执行增强搜索

    Args:
        query: 搜索查询内容
        search_depth: 搜索深度 ("basic", "deep", "comprehensive")
        max_results: 最大结果数量
        enable_cache: 是否启用缓存

    Returns:
        格式化的搜索结果和摘要
    """
    try:
        logger.info(f"执行增强搜索: {query}")

        result = await search_engine.execute(
            query=query,
            depth=search_depth,
            max_results=max_results,
            enable_cache=enable_cache
        )

        return result

    except Exception as e:
        error_msg = f"增强搜索失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp_server.tool()
async def search_suggestions(query: str) -> str:
    """
    获取搜索建议

    Args:
        query: 原始查询

    Returns:
        搜索建议列表
    """
    try:
        suggestions = await search_engine.generate_search_suggestions(query)
        return f"针对'{query}'的搜索建议：\n" + "\n".join([f"• {s}" for s in suggestions])

    except Exception as e:
        return f"生成搜索建议失败: {str(e)}"

# 创建FastAPI应用
app = FastAPI(title="Enhanced Search MCP Server", version="1.0.0")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "searxng_status": await searxng_adapter.health_check()
    }

# 集成MCP服务器
app.mount("/mcp", mcp_server.sse_app)

if __name__ == "__main__":
    logger.info("🚀 启动增强搜索MCP服务器...")
    logger.info(f"📡 SSE端点: http://localhost:8881/mcp/sse")
    logger.info(f"🏥 健康检查: http://localhost:8881/health")

    uvicorn.run(app, host="0.0.0.0", port=8881, log_level="info")
```

### **配置管理**
```python
# enhanced_search_mcp/config.py
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class Config:
    # SearXNG配置
    SEARXNG_URL: str = os.getenv("SEARXNG_URL", "http://localhost:8888")
    SEARXNG_TIMEOUT: int = int(os.getenv("SEARXNG_TIMEOUT", "30"))

    # 缓存配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))
    ENABLE_CACHE: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"

    # 搜索配置
    MAX_CONCURRENT_SEARCHES: int = int(os.getenv("MAX_CONCURRENT_SEARCHES", "5"))
    DEFAULT_ENGINES: str = os.getenv("DEFAULT_ENGINES", "google,bing,brave")
    MAX_RESULTS_PER_QUERY: int = int(os.getenv("MAX_RESULTS_PER_QUERY", "10"))

    # 质量评分权重
    TITLE_RELEVANCE_WEIGHT: float = float(os.getenv("TITLE_RELEVANCE_WEIGHT", "0.3"))
    CONTENT_QUALITY_WEIGHT: float = float(os.getenv("CONTENT_QUALITY_WEIGHT", "0.25"))
    SOURCE_AUTHORITY_WEIGHT: float = float(os.getenv("SOURCE_AUTHORITY_WEIGHT", "0.2"))

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: Optional[str] = os.getenv("LOG_FILE")
```

### **启动脚本**
```bash
#!/bin/bash
# scripts/start_enhanced_mcp.sh

echo "🚀 启动增强搜索MCP服务器..."

# 检查SearXNG是否运行
if ! curl -s http://localhost:8888/health > /dev/null; then
    echo "❌ SearXNG服务未运行，请先启动SearXNG"
    exit 1
fi

# 设置环境变量
export SEARXNG_URL="http://localhost:8888"
export REDIS_URL="redis://localhost:6379"
export LOG_LEVEL="INFO"

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# 安装依赖
pip install -r enhanced_search_mcp/requirements.txt

# 启动服务
cd enhanced_search_mcp
python server.py
```

## 🔗 **Agent-Zero集成配置**

### **MCP客户端配置**
```json
{
  "mcpServers": [
    {
      "name": "enhanced-search",
      "type": "sse",
      "url": "http://localhost:8881/mcp/sse",
      "description": "增强搜索引擎MCP服务器，提供深度搜索和智能分析功能",
      "enabled": true,
      "keywords": [
        "enhanced search", "enhance search", "深入搜索", "详细搜索",
        "全面搜索", "研究", "深度分析", "comprehensive search"
      ],
      "tools": [
        {
          "name": "enhanced_search",
          "description": "执行增强搜索，包含多轮搜索策略和智能摘要",
          "keywords": ["enhanced", "深入", "详细", "全面", "研究"]
        },
        {
          "name": "search_suggestions",
          "description": "获取搜索建议和优化建议",
          "keywords": ["建议", "优化", "suggestions"]
        }
      ]
    }
  ]
}
```

## 📈 **监控和运维**

### **性能监控指标**
- 搜索请求QPS
- 平均响应时间
- 缓存命中率
- 错误率统计
- 资源使用情况

### **日志管理**
```python
# enhanced_search_mcp/monitoring.py
import logging
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logging.info(f"{func.__name__} completed in {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"{func.__name__} failed after {duration:.2f}s: {e}")
            raise
    return wrapper
```

## 🎉 **项目价值总结**

### **技术价值**
1. **架构现代化**: 从单体工具转向微服务架构
2. **性能优化**: 缓存、并发、智能排序全面提升
3. **标准化接口**: MCP协议确保通用性和扩展性
4. **容器化部署**: Docker支持，运维简单

### **业务价值**
1. **搜索质量提升**: 多轮搜索策略，结果更全面准确
2. **用户体验优化**: 智能摘要，信息获取更高效
3. **成本效益**: 缓存机制减少API调用成本
4. **可扩展性**: 支持多客户端，服务复用价值高

**结论**: 这是一个技术可行、价值明确、风险可控的优秀移植项目，强烈建议立即实施！
