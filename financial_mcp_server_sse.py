#!/usr/bin/env python3
"""
金融分析MCP服务器 - SSE版本
提供基于Server-Sent Events的MCP服务器实现
支持实时行情、技术指标分析等金融数据查询功能
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from fastmcp import FastMCP, Context
from pydantic import BaseModel

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastMCP服务器实例
mcp_server = FastMCP(
    name="Financial Analysis MCP Server",
    instructions="""
    专业金融数据分析MCP服务器，提供以下功能：
    
    🏦 核心功能：
    - 实时股票行情查询
    - 历史数据分析
    - 技术指标计算（MACD、RSI、KDJ等50+指标）
    - 财务报表数据
    - 基础数据查询
    
    📊 支持的技术指标：
    - 趋势类：MACD、MA(任意周期)、EXPMA、DMA、TRIX
    - 震荡类：RSI、KDJ、CCI、WR、ROC
    - 成交量：OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD
    - 支撑阻力：BOLL、CDP、MIKE
    - 波动率：ATR、STD、BIAS
    
    🎯 智能识别：
    - 自动识别股票代码和名称
    - 支持自然语言查询
    - 智能查询类型检测
    
    数据源：同花顺iFinD专业金融数据
    """
)

# 金融API客户端（简化版，实际使用时需要完整实现）
class FinancialAPIClient:
    def __init__(self):
        self.base_url = "https://ft.10jqka.com.cn"
        self.access_token = os.getenv("IFIND_ACCESS_TOKEN")
        self.refresh_token = os.getenv("IFIND_REFRESH_TOKEN")
    
    async def get_real_time_quotation(self, codes: str, indicators: str = "") -> Dict[str, Any]:
        """获取实时行情数据"""
        # 这里应该实现实际的API调用逻辑
        # 为演示目的，返回模拟数据
        return {
            "errorcode": 0,
            "data": {
                "tables": [{
                    "table": [
                        {
                            "code": codes.split(",")[0],
                            "latest": "100.50",
                            "preClose": "98.20",
                            "change": "2.30",
                            "changeRatio": "2.34%",
                            "volume": "1234567",
                            "amount": "123456789"
                        }
                    ]
                }]
            }
        }
    
    async def get_technical_indicators(self, codes: str, starttime: str, 
                                     endtime: str, indicators: str) -> Dict[str, Any]:
        """获取技术指标数据"""
        return {
            "errorcode": 0,
            "data": {
                "tables": [{
                    "table": [
                        {
                            "code": codes.split(",")[0],
                            "date": datetime.now().strftime("%Y-%m-%d"),
                            "MACD": "0.15",
                            "RSI": "65.2",
                            "KDJ_K": "78.5"
                        }
                    ]
                }]
            }
        }

# 全局API客户端实例
api_client = FinancialAPIClient()

# 股票代码映射
STOCK_MAPPING = {
    '贵州茅台': '600519.SH',
    '五粮液': '000858.SZ',
    '比亚迪': '002594.SZ',
    '平安银行': '000001.SZ',
    '山西汾酒': '600809.SH',
    '招商银行': '600036.SH',
    '中国平安': '601318.SH',
    '宁德时代': '300750.SZ',
    '腾讯控股': '00700.HK',
    '阿里巴巴': '09988.HK'
}

def extract_stock_codes(text: str) -> str:
    """从文本中提取股票代码"""
    import re
    
    # 匹配标准股票代码格式
    pattern = r'\d{6}\.(SZ|SH|sz|sh)'
    matches = re.findall(pattern, text, re.IGNORECASE)
    
    if matches:
        codes = []
        for match in re.finditer(pattern, text, re.IGNORECASE):
            full_code = match.group(0).upper()
            codes.append(full_code)
        return ','.join(codes)
    
    # 尝试从股票名称映射
    for name, code in STOCK_MAPPING.items():
        if name in text:
            return code
    
    return ""

def detect_query_type(query: str) -> str:
    """检测查询类型"""
    query_lower = query.lower()
    
    # 技术指标关键词
    tech_keywords = ['macd', 'rsi', 'kdj', 'ma', '技术指标', '均线', '指标分析']
    if any(keyword in query_lower for keyword in tech_keywords):
        return "technical_indicators"
    
    # 历史数据关键词
    history_keywords = ['历史', '走势', '过去', '月', '年', '日']
    if any(keyword in query_lower for keyword in history_keywords):
        return "history"
    
    # 财务报表关键词
    report_keywords = ['财报', '报表', '财务', '利润', '资产', '现金流']
    if any(keyword in query_lower for keyword in report_keywords):
        return "financial_report"
    
    # 默认为实时查询
    return "real_time"

@mcp_server.tool()
async def get_stock_real_time(
    codes: str,
    indicators: str = "latest,preClose,change,changeRatio,volume,amount"
) -> str:
    """
    获取股票实时行情数据
    
    Args:
        codes: 股票代码，支持多个代码用逗号分隔，如 "600519.SH,000858.SZ"
        indicators: 查询指标，如 "latest,preClose,change,changeRatio,volume"
    
    Returns:
        JSON格式的实时行情数据
    """
    try:
        result = await api_client.get_real_time_quotation(codes, indicators)
        
        if result.get('errorcode') == 0:
            tables = result.get('data', {}).get('tables', [])
            if tables and tables[0].get('table'):
                data = tables[0]['table'][0]
                response = f"""
📊 **实时行情数据**

🏷️ **股票代码**: {data.get('code', codes)}
💰 **最新价**: {data.get('latest', 'N/A')}
📈 **昨收价**: {data.get('preClose', 'N/A')}
📊 **涨跌额**: {data.get('change', 'N/A')}
📈 **涨跌幅**: {data.get('changeRatio', 'N/A')}
📦 **成交量**: {data.get('volume', 'N/A')}
💵 **成交额**: {data.get('amount', 'N/A')}

⏰ **查询时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                return response
        
        return f"❌ 获取实时行情失败: {result.get('errmsg', '未知错误')}"
        
    except Exception as e:
        logger.error(f"获取实时行情异常: {e}")
        return f"❌ 查询异常: {str(e)}"

@mcp_server.tool()
async def analyze_technical_indicators(
    query: str = "",
    codes: str = "",
    indicators: str = "MACD,RSI,KDJ",
    period: str = "1M"
) -> str:
    """
    技术指标分析
    
    Args:
        query: 自然语言查询，如 "分析贵州茅台的MACD指标"
        codes: 股票代码，如 "600519.SH"
        indicators: 技术指标，如 "MACD,RSI,KDJ"
        period: 分析周期，如 "1M"（1个月）
    
    Returns:
        技术指标分析结果
    """
    try:
        # 如果提供了query，从中提取股票代码
        if query and not codes:
            codes = extract_stock_codes(query)
        
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码（如600519.SH）或股票名称"
        
        # 计算时间范围
        end_date = datetime.now()
        if period == "1M":
            start_date = end_date - timedelta(days=30)
        elif period == "3M":
            start_date = end_date - timedelta(days=90)
        else:
            start_date = end_date - timedelta(days=30)
        
        result = await api_client.get_technical_indicators(
            codes=codes,
            starttime=start_date.strftime("%Y-%m-%d"),
            endtime=end_date.strftime("%Y-%m-%d"),
            indicators=indicators
        )
        
        if result.get('errorcode') == 0:
            tables = result.get('data', {}).get('tables', [])
            if tables and tables[0].get('table'):
                data = tables[0]['table'][0]
                response = f"""
📊 **技术指标分析报告**

🏷️ **股票代码**: {data.get('code', codes)}
📅 **分析日期**: {data.get('date', datetime.now().strftime('%Y-%m-%d'))}
⏱️ **分析周期**: {period}

📈 **技术指标数据**:
- **MACD**: {data.get('MACD', 'N/A')}
- **RSI**: {data.get('RSI', 'N/A')}
- **KDJ_K**: {data.get('KDJ_K', 'N/A')}

🎯 **分析建议**:
基于当前技术指标数据，建议关注趋势变化和关键支撑阻力位。

⏰ **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                return response
        
        return f"❌ 获取技术指标失败: {result.get('errmsg', '未知错误')}"
        
    except Exception as e:
        logger.error(f"技术指标分析异常: {e}")
        return f"❌ 分析异常: {str(e)}"

@mcp_server.tool()
async def financial_query(
    query: str,
    query_type: str = "auto"
) -> str:
    """
    智能金融查询
    
    Args:
        query: 自然语言查询，如 "查询贵州茅台的实时行情"
        query_type: 查询类型，auto为自动检测
    
    Returns:
        查询结果
    """
    try:
        # 提取股票代码
        codes = extract_stock_codes(query)
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码或股票名称"
        
        # 自动检测查询类型
        if query_type == "auto":
            query_type = detect_query_type(query)
        
        # 根据查询类型调用相应功能
        if query_type == "technical_indicators":
            return await analyze_technical_indicators(query=query, codes=codes)
        else:
            # 默认返回实时行情
            return await get_stock_real_time(codes=codes)
            
    except Exception as e:
        logger.error(f"智能查询异常: {e}")
        return f"❌ 查询异常: {str(e)}"

# 创建FastAPI应用
app = FastAPI(title="Financial MCP Server")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# 集成MCP服务器到FastAPI
app.mount("/mcp", mcp_server.create_app())

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("IFIND_REFRESH_TOKEN"):
        logger.warning("未设置IFIND_REFRESH_TOKEN环境变量")
    
    logger.info("启动金融分析MCP服务器...")
    logger.info("SSE端点: http://localhost:8080/mcp/sse")
    logger.info("健康检查: http://localhost:8080/health")
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
