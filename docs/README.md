![Agent Zero Logo](res/header.png)
# Agent Zero Documentation
To begin with Agent Zero, follow the links below for detailed guides on various topics:

- **[Installation](installation.md):** Set up (or [update](installation.md#how-to-update-agent-zero)) Agent Zero on your system.
- **[Usage Guide](usage.md):** Explore GUI features and usage scenarios.
- **[Architecture Overview](architecture.md):** Understand the internal workings of the framework.
- **[Contributing](contribution.md):** Learn how to contribute to the Agent Zero project.
- **[Troubleshooting and FAQ](troubleshooting.md):** Find answers to common issues and questions.

### Your experience with Agent Zero starts now!

- **Download Agent Zero:** Follow the [installation guide](installation.md) to download and run Agent Zero.
- **Join the Community:** Join the Agent Zero [Skool](https://www.skool.com/agent-zero) or [Discord](https://discord.gg/Z2tun2N3) community to discuss ideas, ask questions, and collaborate with other contributors.
- **Share your Work:** Share your Agent Zero creations, workflows and discoverings on our [Show and Tell](https://github.com/frdel/agent-zero/discussions/categories/show-and-tell) area on GitHub.
- **Report Issues:** Use the [GitHub issue tracker](https://github.com/frdel/agent-zero/issues) to report framework-relative bugs or suggest new features.

## Table of Contents

- [Welcome to the Agent Zero Documentation](#agent-zero-documentation)
  - [Your Experience with Agent Zero](#your-experience-with-agent-zero-starts-now)
  - [Table of Contents](#table-of-contents)
- [Installation Guide](installation.md)
  - [Windows, macOS and Linux Setup](installation.md#windows-macos-and-linux-setup-guide)
  - [Settings Configuration](installation.md#settings-configuration)
  - [Choosing Your LLMs](installation.md#choosing-your-llms)
  - [Installing and Using Ollama](installation.md#installing-and-using-ollama-local-models)
  - [Using Agent Zero on Mobile](installation.md#using-agent-zero-on-your-mobile-device)
  - [How to Update Agent Zero](installation.md#how-to-update-agent-zero)
  - [Full Binaries Installation](installation.md#in-depth-guide-for-full-binaries-installation)
- [Usage Guide](usage.md)
  - [Basic Operations](usage.md#basic-operations)
    - [Restart Framework](usage.md#restart-framework)
    - [Action Buttons](usage.md#action-buttons)
    - [File Attachments](usage.md#file-attachments)
  - [Tool Usage](usage.md#tool-usage)
  - [Example of Tools Usage](usage.md#example-of-tools-usage-web-search-and-code-execution)
  - [Multi-Agent Cooperation](usage.md#multi-agent-cooperation)
  - [Prompt Engineering](usage.md#prompt-engineering)
  - [Voice Interface](usage.md#voice-interface)
  - [Mathematical Expressions](usage.md#mathematical-expressions)
  - [File Browser](usage.md#file-browser)
  - [Backup & Restore](usage.md#backup--restore)
- [Architecture Overview](architecture.md)
  - [System Architecture](architecture.md#system-architecture)
  - [Runtime Architecture](architecture.md#runtime-architecture)
  - [Implementation Details](architecture.md#implementation-details)
  - [Core Components](architecture.md#core-components)
    - [Agents](architecture.md#1-agents)
    - [Tools](architecture.md#2-tools)
    - [SearXNG Integration](architecture.md#searxng-integration)
    - [Memory System](architecture.md#3-memory-system)
    - [Messages History and Summarization](archicture.md#messages-history-and-summarization)
    - [Prompts](architecture.md#4-prompts)
    - [Knowledge](architecture.md#5-knowledge)
    - [Instruments](architecture.md#6-instruments)
    - [Extensions](architecture.md#7-extensions)
  - [Contributing](contribution.md)
  - [Getting Started](contribution.md#getting-started)
  - [Making Changes](contribution.md#making-changes)
  - [Submitting a Pull Request](contribution.md#submitting-a-pull-request)
  - [Documentation Stack](contribution.md#documentation-stack)
- [Troubleshooting and FAQ](troubleshooting.md)
  - [Frequently Asked Questions](troubleshooting.md#frequently-asked-questions)
  - [Troubleshooting](troubleshooting.md#troubleshooting)
