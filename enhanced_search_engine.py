#!/usr/bin/env python3
"""
Enhanced Search Engine - 增强搜索引擎 (MCP版本)
移植自Agent-Zero项目，提供多轮搜索策略和智能结果分析
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from searxng_adapter import SearXNGAdapter
from config import Config

logger = logging.getLogger(__name__)

class EnhancedSearchEngine:
    """
    增强搜索引擎 - MCP版本
    
    功能特点：
    - 多轮搜索策略（基础+扩展+相关）
    - 结果质量评估和排序
    - 智能摘要生成
    - 更全面的信息收集
    """
    
    def __init__(self, searxng_adapter: SearXNGAdapter, config: Config):
        self.searxng = searxng_adapter
        self.config = config
        
        # 统计信息
        self.stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'total_results_processed': 0,
            'avg_quality_score': 0.0
        }
    
    async def execute(
        self, 
        query: str, 
        depth: str = "deep", 
        max_results: int = 20,
        enable_cache: bool = True
    ) -> str:
        """
        执行增强搜索
        
        Args:
            query: 搜索查询内容
            depth: 搜索深度 ("basic", "deep", "comprehensive")
            max_results: 最大结果数量
            enable_cache: 是否启用缓存
            
        Returns:
            格式化的搜索结果和摘要
        """
        start_time = time.time()
        self.stats['total_searches'] += 1
        
        try:
            logger.info(f"开始增强搜索: '{query}' (深度: {depth})")
            
            # 根据深度决定搜索策略
            if depth == "basic":
                results = await self._basic_search_only(query)
            elif depth == "comprehensive":
                results = await self._comprehensive_search(query)
            else:  # deep (默认)
                results = await self._deep_search(query)
            
            # 整合和质量评估
            integrated_results = await self._integrate_results(results)
            
            # 生成智能摘要
            summary = await self._generate_summary(query, integrated_results)
            
            # 格式化最终结果
            final_result = self._format_enhanced_result(
                query, summary, integrated_results[:max_results]
            )
            
            # 更新统计
            self.stats['successful_searches'] += 1
            self.stats['total_results_processed'] += len(integrated_results)
            
            execution_time = time.time() - start_time
            logger.info(f"增强搜索完成: {len(integrated_results)}个结果, 耗时{execution_time:.2f}秒")
            
            return final_result
            
        except Exception as e:
            self.stats['failed_searches'] += 1
            error_msg = f"增强搜索执行失败: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    async def _basic_search_only(self, query: str) -> List[Dict[str, Any]]:
        """仅执行基础搜索"""
        logger.info("执行基础搜索...")
        basic_results = await self._basic_search(query)
        return [basic_results]
    
    async def _deep_search(self, query: str) -> List[Dict[str, Any]]:
        """执行深度搜索（基础+扩展）"""
        logger.info("执行深度搜索...")
        
        # 并发执行基础搜索和扩展搜索
        tasks = [
            self._basic_search(query),
            self._expanded_search(query)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.warning(f"搜索任务失败: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def _comprehensive_search(self, query: str) -> List[Dict[str, Any]]:
        """执行全面搜索（基础+扩展+相关）"""
        logger.info("执行全面搜索...")
        
        # 并发执行所有搜索
        tasks = [
            self._basic_search(query),
            self._expanded_search(query),
            self._related_search(query)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.warning(f"搜索任务失败: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def _basic_search(self, query: str) -> List[Dict[str, Any]]:
        """执行基础SearXNG搜索"""
        try:
            result = await self.searxng.search(
                query=query,
                engines=self.config.DEFAULT_ENGINES
            )
            
            results = result.get('results', [])
            logger.debug(f"基础搜索获得 {len(results)} 个结果")
            
            return results[:self.config.MAX_RESULTS_PER_QUERY]
            
        except Exception as e:
            logger.error(f"基础搜索失败: {e}")
            return []
    
    async def _expanded_search(self, query: str) -> List[Dict[str, Any]]:
        """执行扩展关键词搜索"""
        if not self.config.ENABLE_EXPANDED_SEARCH:
            return []
        
        try:
            # 生成扩展关键词
            expanded_queries = self._generate_expanded_queries(query)
            all_results = []
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(self.config.MAX_CONCURRENT_SEARCHES)
            
            async def search_with_semaphore(expanded_query):
                async with semaphore:
                    return await self._basic_search(expanded_query)
            
            # 并发执行扩展搜索
            tasks = [
                search_with_semaphore(eq) 
                for eq in expanded_queries[:self.config.EXPANDED_QUERIES_LIMIT]
            ]
            
            results_list = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整合结果
            for results in results_list:
                if isinstance(results, Exception):
                    logger.warning(f"扩展搜索任务失败: {results}")
                else:
                    all_results.extend(results[:5])  # 每个查询取前5个结果
            
            logger.debug(f"扩展搜索获得 {len(all_results)} 个结果")
            return all_results
            
        except Exception as e:
            logger.error(f"扩展搜索失败: {e}")
            return []
    
    async def _related_search(self, query: str) -> List[Dict[str, Any]]:
        """执行相关主题搜索"""
        if not self.config.ENABLE_RELATED_SEARCH:
            return []
        
        try:
            # 生成相关主题查询
            related_queries = self._generate_related_queries(query)
            all_results = []
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(self.config.MAX_CONCURRENT_SEARCHES)
            
            async def search_with_semaphore(related_query):
                async with semaphore:
                    return await self._basic_search(related_query)
            
            # 并发执行相关搜索
            tasks = [
                search_with_semaphore(rq) 
                for rq in related_queries[:self.config.RELATED_QUERIES_LIMIT]
            ]
            
            results_list = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整合结果
            for results in results_list:
                if isinstance(results, Exception):
                    logger.warning(f"相关搜索任务失败: {results}")
                else:
                    all_results.extend(results[:3])  # 每个查询取前3个结果
            
            logger.debug(f"相关搜索获得 {len(all_results)} 个结果")
            return all_results
            
        except Exception as e:
            logger.error(f"相关搜索失败: {e}")
            return []
    
    def _generate_expanded_queries(self, query: str) -> List[str]:
        """生成扩展关键词查询"""
        expanded_queries = []
        
        # 添加深度研究关键词
        for keyword in self.config.DEPTH_KEYWORDS:
            expanded_queries.append(f"{query} {keyword}")
        
        # 添加时间相关关键词
        for keyword in self.config.TIME_KEYWORDS:
            expanded_queries.append(f"{query} {keyword}")
        
        # 添加类型相关关键词
        for keyword in self.config.TYPE_KEYWORDS:
            expanded_queries.append(f"{query} {keyword}")
        
        return expanded_queries
    
    def _generate_related_queries(self, query: str) -> List[str]:
        """生成相关主题查询"""
        related_queries = []
        
        # 添加对比关键词
        for keyword in self.config.COMPARE_KEYWORDS:
            related_queries.append(f"{query} {keyword}")
        
        # 添加实践关键词
        for keyword in self.config.PRACTICE_KEYWORDS:
            related_queries.append(f"{query} {keyword}")
        
        return related_queries
    
    async def _integrate_results(self, result_lists: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """整合多轮搜索结果并去重"""
        all_results = []
        seen_urls = set()
        
        for result_list in result_lists:
            for result in result_list:
                url = result.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    # 添加质量评分
                    result['quality_score'] = self._calculate_quality_score(result)
                    all_results.append(result)
        
        # 按质量评分排序
        all_results.sort(key=lambda x: x.get('quality_score', 0), reverse=True)
        
        # 更新平均质量评分统计
        if all_results:
            avg_score = sum(r.get('quality_score', 0) for r in all_results) / len(all_results)
            self.stats['avg_quality_score'] = avg_score
        
        logger.debug(f"整合后获得 {len(all_results)} 个去重结果")
        return all_results[:self.config.MAX_TOTAL_RESULTS]

    def _calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """计算搜索结果质量评分 (0-10分)"""
        score = 0.0

        title = result.get('title', '').lower()
        content = result.get('content', '').lower()
        url = result.get('url', '').lower()

        # 基础分数 (所有结果都有的基础分)
        score += 3.0  # 基础分3分

        # 标题质量评分 (最高2.5分)
        title_score = 0.0
        if len(title) > 10:
            title_score += 0.5
        if len(title) > 30:
            title_score += 0.5

        # 质量关键词匹配
        if any(keyword in title for keyword in self.config.QUALITY_KEYWORDS):
            title_score += 1.5

        score += title_score * self.config.TITLE_RELEVANCE_WEIGHT / 0.3

        # 内容质量评分 (最高2分)
        content_score = 0.0
        if len(content) > 50:
            content_score += 0.5
        if len(content) > 150:
            content_score += 0.5
        if len(content) > 300:
            content_score += 0.5
        if len(content) > 500:
            content_score += 0.5

        score += content_score * self.config.CONTENT_QUALITY_WEIGHT / 0.25

        # URL质量评分 (最高2分)
        url_score = 0.0
        if any(domain in url for domain in self.config.AUTHORITY_DOMAINS):
            url_score += 2.0
        elif any(domain in url for domain in ['com', 'net', 'io', 'co']):
            url_score += 0.5  # 商业域名给少量分数

        score += url_score * self.config.SOURCE_AUTHORITY_WEIGHT / 0.2

        # 内容相关性加分 (最高1分)
        if title and content:
            # 标题和内容都有内容
            relevance_score = 0.5
            # 内容不是重复标题
            if content.strip() != title.strip():
                relevance_score += 0.5
            score += relevance_score

        # 避免低质量内容 (扣分)
        if any(indicator in title + content for indicator in self.config.LOW_QUALITY_INDICATORS):
            score -= 1.5

        # 确保分数在0-10范围内
        return max(0.0, min(score, 10.0))

    async def _generate_summary(self, query: str, results: List[Dict[str, Any]]) -> str:
        """生成智能摘要"""
        if not results:
            return f"关于'{query}'的搜索未找到相关结果。"

        # 提取关键信息
        key_points = []
        for result in results[:10]:  # 使用前10个高质量结果
            title = result.get('title', '')
            content = result.get('content', '')
            if title and content:
                key_points.append(f"• {title}: {content[:100]}...")

        # 计算统计信息
        avg_score = sum(r.get('quality_score', 0) for r in results) / len(results) if results else 0

        # 生成摘要
        summary = f"""📊 关于'{query}'的增强搜索摘要

🔍 搜索范围: 执行了多轮搜索策略（基础+扩展+相关主题）
📈 结果质量: 找到 {len(results)} 个相关结果，已按质量评分排序
⭐ 质量评分: 平均分 {avg_score:.1f}/10

📝 主要发现:
{chr(10).join(key_points[:5])}

💡 建议: 基于搜索结果，建议进一步关注以下方面：
{self._generate_suggestions(results)}"""

        return summary.strip()

    def _generate_suggestions(self, results: List[Dict[str, Any]]) -> str:
        """基于搜索结果生成建议"""
        suggestions = []

        # 分析高质量结果的共同主题
        all_titles = ' '.join([r.get('title', '') for r in results[:5]])

        # 提取常见关键词
        common_keywords = ['技术', '方法', '应用', '发展', '趋势', '原理', '实践']
        found_keywords = [kw for kw in common_keywords if kw in all_titles]

        if found_keywords:
            suggestions.append(f"深入了解: {', '.join(found_keywords[:3])}")

        suggestions.append("查看权威来源的详细文档")
        suggestions.append("关注最新发展动态和趋势")

        return '\n'.join([f"  - {s}" for s in suggestions])

    def _format_enhanced_result(
        self,
        query: str,
        summary: str,
        results: List[Dict[str, Any]]
    ) -> str:
        """格式化增强搜索结果"""

        # 限制结果数量，避免输出过长
        top_results = results[:5]  # 只显示前5个结果

        formatted_results = []
        for i, result in enumerate(top_results, 1):
            title = result.get('title', '无标题')
            url = result.get('url', '')
            content = result.get('content', '无描述')
            quality_score = result.get('quality_score', 0)

            # 简化格式，减少特殊字符
            formatted_result = f"{i}. {title[:80]}{'...' if len(title) > 80 else ''}\n   评分: {quality_score:.1f}/10 | {content[:100]}{'...' if len(content) > 100 else ''}\n   链接: {url}"
            formatted_results.append(formatted_result)

        # 简化最终输出格式
        final_result = f"""📊 关于'{query}'的增强搜索结果

{summary}

🔍 主要结果 (前5项):
{chr(10).join(formatted_results)}

✅ 搜索完成，共分析 {len(results)} 个结果。
📊 服务统计: 总搜索 {self.stats['total_searches']} 次，成功率 {(self.stats['successful_searches']/max(self.stats['total_searches'], 1)*100):.1f}%"""

        return final_result.strip()

    async def generate_search_suggestions(self, query: str) -> List[str]:
        """生成搜索建议"""
        try:
            suggestions = await self.searxng.suggest(query)

            # 如果SearXNG没有建议，生成基于关键词的建议
            if not suggestions:
                suggestions = []
                for keyword in self.config.DEPTH_KEYWORDS[:3]:
                    suggestions.append(f"{query} {keyword}")
                for keyword in self.config.TIME_KEYWORDS[:2]:
                    suggestions.append(f"{query} {keyword}")

            return suggestions[:10]  # 最多返回10个建议

        except Exception as e:
            logger.error(f"生成搜索建议失败: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'failed_searches': 0,
            'total_results_processed': 0,
            'avg_quality_score': 0.0
        }
