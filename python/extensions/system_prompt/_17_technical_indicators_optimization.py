"""
技术指标查询优化扩展
专门优化技术指标查询的效率，减少不必要的推理步骤
"""

from typing import Any
from python.helpers.extension import Extension
from agent import Agent, LoopData
import re


class TechnicalIndicatorsOptimization(Extension):
    """技术指标查询优化扩展类"""

    def __init__(self, agent: Agent):
        super().__init__(agent)
        
        # 技术指标关键词 (支持中英文)
        self.technical_keywords = [
            'MACD', 'RSI', 'KDJ', 'BOLL', 'MA', 'EMA', 'SMA',
            'OBV', 'VR', 'WR', 'CCI', 'DMA', 'TRIX', 'BIAS',
            '技术指标', '技术分析', '买卖信号', '超买', '超卖',
            '金叉', '死叉', '背离', '趋势', '支撑', '阻力',
            'technical indicator', 'technical analysis', 'indicators',
            'operation recommendation', 'trading signal', 'analysis'
        ]
        
        # 股票名称和代码模式 (支持中英文)
        self.stock_patterns = [
            r'五粮液|Wuliangye|000858',
            r'茅台|贵州茅台|Moutai|Kweichow Moutai|600519',
            r'平安|平安银行|Ping An|000001',
            r'招行|招商银行|China Merchants Bank|600036',
            r'比亚迪|BYD|002594',
            r'万科|万科A|Vanke|000002',
            r'美的|美的集团|Midea|000333',
            r'格力|格力电器|Gree|000651',
            r'汾酒|山西汾酒|Fenjiu|Shanxi Fenjiu|600809',  # 支持英文名称
            r'中芯国际|SMIC|688981',
            r'宁德时代|CATL|300750',
            r'海康威视|Hikvision|002415',
            r'\d{6}\.(SH|SZ|HK)'  # 股票代码格式
        ]

    async def execute(self, system_prompt: list[str] = [], loop_data: LoopData = LoopData(), **kwargs: Any):
        """执行扩展，添加技术指标优化提示"""
        try:
            # 获取用户输入
            user_input = self._get_user_input(loop_data)
            
            if user_input and self._is_technical_indicator_query(user_input):
                # 添加技术指标优化提示
                optimization_prompt = self._generate_optimization_prompt(user_input)
                system_prompt.append(optimization_prompt)
                
                # 记录后台日志（不显示在用户界面）
                # self.agent.context.log.log(
                #     type="info",
                #     content=f"技术指标查询优化已激活: {user_input[:50]}..."
                # )
        
        except Exception as e:
            # 优化失败不应该影响主流程
            self.agent.context.log.log(
                type="warning",
                content=f"技术指标优化扩展执行失败: {e}"
            )

    def _get_user_input(self, loop_data: LoopData) -> str:
        """获取用户输入"""
        try:
            # 方法1：从 loop_data 获取
            if hasattr(loop_data, 'message') and loop_data.message:
                if isinstance(loop_data.message, str):
                    return loop_data.message.strip()
                elif hasattr(loop_data.message, 'content'):
                    return str(loop_data.message.content).strip()
            
            # 方法2：从 agent.history 获取最后一条用户消息
            if hasattr(self.agent, 'history') and self.agent.history:
                try:
                    if hasattr(self.agent.history, 'current') and self.agent.history.current:
                        messages = self.agent.history.current.messages
                        if messages:
                            for message in reversed(messages):
                                if not message.ai and message.content:
                                    if isinstance(message.content, str):
                                        return message.content.strip()
                except Exception:
                    pass
            
            return ""
        except Exception:
            return ""

    def _is_technical_indicator_query(self, user_input: str) -> bool:
        """判断是否为技术指标查询"""
        user_input_lower = user_input.lower()
        
        # 检查技术指标关键词
        has_technical_keyword = any(
            keyword.lower() in user_input_lower 
            for keyword in self.technical_keywords
        )
        
        # 检查股票相关内容
        has_stock_reference = any(
            re.search(pattern, user_input, re.IGNORECASE)
            for pattern in self.stock_patterns
        )
        
        return has_technical_keyword and has_stock_reference

    def _generate_optimization_prompt(self, user_input: str) -> str:
        """生成技术指标优化提示"""
        
        # 提取股票信息
        stock_info = self._extract_stock_info(user_input)
        technical_indicators = self._extract_technical_indicators(user_input)
        
        return f"""
## 🎯 技术指标查询优化指导

**检测到技术指标查询**: {user_input}

### ⚡ 高效执行策略:

1. **直接使用financial_data_tool**:
   - 这是技术指标查询，请直接使用financial_data_tool
   - 不需要进行复杂的推理或考虑其他工具
   - 不需要使用subordinate或其他代理

2. **推荐参数**:
   ```json
   {{
     "tool_name": "financial_data_tool",
     "tool_args": {{
       "query": "{user_input}"
     }}
   }}
   ```

3. **关键信息**:
   - 检测到股票: {stock_info}
   - 检测到指标: {', '.join(technical_indicators)}
   - 工具会自动识别为technical_indicators查询类型
   - 工具会自动解析股票代码和指标参数

### 🚫 避免的操作:
- ❌ 不要使用real_time查询类型（会失败）
- ❌ 不要使用history查询类型（不是技术指标）
- ❌ 不要进行长时间的推理分析
- ❌ 不要考虑使用subordinate
- ❌ 不要尝试多种不同的工具

### ✅ 正确做法:
- ✅ 直接调用financial_data_tool，传入完整的query参数
- ✅ 工具内部会自动处理所有技术指标逻辑
- ✅ 相信工具的智能识别和处理能力

**请立即执行，无需进一步分析！**
"""

    def _extract_stock_info(self, user_input: str) -> str:
        """提取股票信息"""
        # 检查股票代码
        code_match = re.search(r'(\d{6}\.(SH|SZ|HK))', user_input, re.IGNORECASE)
        if code_match:
            return code_match.group(1)
        
        # 检查股票名称
        for pattern in self.stock_patterns:
            if re.search(pattern, user_input, re.IGNORECASE):
                # 提取匹配的部分
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    return match.group(0)
        
        return "未明确指定"

    def _extract_technical_indicators(self, user_input: str) -> list:
        """提取技术指标"""
        found_indicators = []
        user_input_upper = user_input.upper()
        
        for keyword in self.technical_keywords:
            if keyword.upper() in user_input_upper:
                found_indicators.append(keyword)
        
        return found_indicators if found_indicators else ["技术指标"]
