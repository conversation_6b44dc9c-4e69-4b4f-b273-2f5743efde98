"""
强制中文响应扩展
确保Agent始终使用中文进行思考和回复
"""

from typing import Any
from python.helpers.extension import Extension
from agent import Agent, LoopData


class ForceChinese(Extension):
    """强制中文响应扩展类"""

    async def execute(self, system_prompt: list[str] = [], loop_data: LoopData = LoopData(), **kwargs: Any):
        """执行扩展，添加强制中文指令"""
        try:
            chinese_prompt = """
## 🇨🇳 语言要求 - MANDATORY CHINESE LANGUAGE REQUIREMENT

**重要：必须始终使用中文进行所有交互**

### 📝 响应格式要求：
- **thoughts**: 必须使用中文思考和分析
- **headline**: 必须使用中文标题
- **所有文本内容**: 必须使用中文表达

### ✅ 正确示例：
```json
{
    "thoughts": [
        "用户询问山西汾酒的技术指标分析",
        "这是一个金融数据查询请求",
        "应该直接使用financial_data_tool工具",
        "传入完整的查询参数"
    ],
    "headline": "查询山西汾酒技术指标数据",
    "tool_name": "financial_data_tool",
    "tool_args": {
        "query": "根据山西汾酒的MACD、KDJ和RSI指标给出当前的操作建议"
    }
}
```

### ❌ 错误示例（禁止使用英文）：
```json
{
    "thoughts": [
        "User is asking about technical indicators",  // ❌ 禁止英文
        "This is a financial query"                   // ❌ 禁止英文
    ],
    "headline": "Analyzing technical indicators",     // ❌ 禁止英文
    "tool_name": "financial_data_tool"
}
```

### 🚨 强制执行规则：
1. **所有thoughts必须用中文表达**
2. **所有headline必须用中文标题**
3. **所有分析和推理过程必须用中文**
4. **即使用户使用英文提问，也必须用中文回复**
5. **工具调用的参数可以保持原始语言，但思考过程必须中文**

### 💡 中文表达指导：
- 使用简洁明了的中文表达
- 避免中英文混杂
- 使用专业的中文术语
- 保持语言的自然流畅

**请严格遵守此语言要求，确保所有响应都使用中文！**
"""
            
            # 将中文要求插入到系统提示的最前面，确保优先级最高
            system_prompt.insert(0, chinese_prompt)
            
            # 记录后台日志（不显示在用户界面）
            # self.agent.context.log.log(
            #     type="info",
            #     content="强制中文响应扩展已激活"
            # )
        
        except Exception as e:
            # 扩展失败不应该影响主流程
            self.agent.context.log.log(
                type="warning",
                content=f"强制中文扩展执行失败: {e}"
            )
