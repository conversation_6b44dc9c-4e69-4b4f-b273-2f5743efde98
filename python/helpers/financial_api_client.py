"""
同花顺iFinD金融数据API客户端
提供股票实时行情、历史数据、基础数据查询功能
"""

import os
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from python.helpers import dotenv


class FinancialAPIClient:
    """同花顺iFinD API客户端"""

    # 根据手册第1385-1454行的错误码映射
    ERROR_CODES = {
        -1010: "token已失效",
        -1000: "数据服务器错误",
        -1001: "网关服务器错误",
        -1002: "请求超时",
        -1003: "access-token不能为空",
        -1004: "行情数据服务器错误",
        -1005: "用户认证错误",
        -4001: "数据为空",
        -4100: "请先登录iFind",
        -4101: "数据库执行错误",
        -4102: "服务器内部错误",
        -4201: "数据服务器取值错误",
        -4203: "请求格式错误",
        -4204: "错误的时间格式",
        -4205: "开始时间不能大于结束时间",
        -4206: "含有错误的同花顺代码",
        -4211: "时间区间内无交易日",
        -4212: "时间区间内股票未上市",
        -4213: "开始日期大于截止日期",
        -4301: "基础数据使用量超过500万/周",
        -4302: "行情数据使用量超过1.5亿/周",
        -4303: "EDB数据使用量超过500万/周",
        -4304: "高频序列单条命令请求数据量过大(200万)",
        -4305: "基础数据单条命令请求数据量过大(20万)",
        -4306: "快照单条命令请求数据量过大(200万)",
        -4400: "请求频率超限(600次/分钟)"
    }

    def __init__(self, refresh_token: Optional[str] = None):
        # API配置
        self.base_url = "https://ft.10jqka.com.cn"
        self.token_url = f"{self.base_url}/api/v1/get_access_token"
        
        # Token管理
        self.refresh_token = refresh_token or dotenv.get_dotenv_value("IFIND_REFRESH_TOKEN")
        self.access_token = None
        self.token_expires_at = None
        
        # 请求配置
        self.timeout = int(os.getenv('IFIND_API_TIMEOUT', '30'))
        self.max_retries = int(os.getenv('IFIND_MAX_RETRIES', '3'))

    def _get_error_message(self, error_code: int) -> str:
        """根据错误码获取友好的错误信息"""
        return self.ERROR_CODES.get(error_code, f"未知错误码: {error_code}")
    
    def _get_access_token(self) -> str:
        """获取有效的access_token"""
        # 检查当前token是否有效（提前5分钟刷新）
        if self.access_token and self.token_expires_at:
            if datetime.now() < self.token_expires_at - timedelta(minutes=5):
                return self.access_token
        
        # 刷新token
        return self._refresh_access_token()
    
    def _refresh_access_token(self) -> str:
        """刷新access_token"""
        if not self.refresh_token:
            raise Exception("REFRESH_TOKEN未配置，请在.env文件中设置IFIND_REFRESH_TOKEN")

        # 根据手册第20行，refresh_token应该放在headers中
        headers = {
            'Content-Type': 'application/json',
            'refresh_token': self.refresh_token
        }

        try:
            # 根据手册，也可以将refresh_token放在请求体中作为备选方案
            response = requests.post(self.token_url, headers=headers, timeout=self.timeout)

            if response.status_code == 200:
                result = response.json()
                if result.get('errorcode') == 0:
                    self.access_token = result['data']['access_token']
                    # 解析过期时间
                    expired_time_str = result['data'].get('expired_time')
                    if expired_time_str:
                        self.token_expires_at = datetime.strptime(expired_time_str, '%Y-%m-%d %H:%M:%S')
                    else:
                        # 根据手册第13行，access_token默认7天过期
                        self.token_expires_at = datetime.now() + timedelta(days=7)
                    return self.access_token
                else:
                    raise Exception(f"Token刷新失败: {result.get('errmsg', '未知错误')}")
            else:
                raise Exception(f"HTTP请求失败: {response.status_code}")

        except Exception as e:
            raise Exception(f"Token刷新异常: {str(e)}")
    
    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        access_token = self._get_access_token()
        url = f"{self.base_url}{endpoint}"
        
        headers = {
            'Content-Type': 'application/json',
            'access_token': access_token
        }
        
        # 重试机制
        for attempt in range(self.max_retries):
            try:
                response = requests.post(url, headers=headers, json=params, timeout=self.timeout)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('errorcode') == 0:
                        return result
                    else:
                        error_code = result.get('errorcode')
                        error_msg = self._get_error_message(error_code)
                        return {
                            'errorcode': error_code,
                            'reason': error_msg,
                            'original_msg': result.get('errmsg', '未知错误')
                        }
                else:
                    return {
                        'status_code': response.status_code,
                        'reason': f'HTTP错误: {response.status_code}'
                    }
                    
            except Exception as e:
                if attempt < self.max_retries - 1:
                    time.sleep(1)  # 重试前等待
                    continue
                else:
                    return {
                        'status_code': -1,
                        'reason': f'请求异常: {str(e)}'
                    }
    
    async def get_real_time_quotation(self, codes: str, indicators: str = "") -> Dict[str, Any]:
        """获取实时行情数据

        根据手册第445-630行，支持的指标包括：
        tradeDate, tradeTime, preClose, open, high, low, latest, change, changeRatio,
        volume, amount, turnoverRatio, pb, pe_ttm, totalShares, totalCapital等
        """
        if not indicators:
            # 使用手册推荐的常用实时行情指标
            indicators = "tradeDate,tradeTime,preClose,open,high,low,latest,change,changeRatio,volume,amount,turnoverRatio,pb,pe_ttm,totalShares,totalCapital"

        params = {
            "codes": codes,
            "indicators": indicators
        }

        result = self._make_request('/api/v1/real_time_quotation', params)

        # 修复数据结构：如果tables在根级别，移动到data字段下
        if 'tables' in result and 'data' not in result:
            result['data'] = {'tables': result['tables']}

        return result
    
    async def get_history_quotation(self, codes: str, startdate: str, enddate: str,
                                  indicators: str = "") -> Dict[str, Any]:
        """获取历史行情数据

        根据手册第149-207行，支持的指标包括：
        preClose, open, high, low, close, avgPrice, change, changeRatio,
        volume, amount, turnoverRatio, pe_ttm, pb, totalShares, totalCapital等
        """
        if not indicators:
            # 使用手册推荐的常用历史行情指标
            indicators = "preClose,open,high,low,close,volume,amount,turnoverRatio,changeRatio"

        params = {
            "codes": codes,
            "startdate": startdate,
            "enddate": enddate,
            "indicators": indicators
        }

        result = self._make_request('/api/v1/cmd_history_quotation', params)

        # 修复数据结构：如果tables在根级别，移动到data字段下
        if 'tables' in result and 'data' not in result:
            result['data'] = {'tables': result['tables']}

        return result
    
    async def get_basic_data(self, codes: str, indicators: str = "") -> Dict[str, Any]:
        """获取基础数据

        注意：基础数据API需要特殊的indipara参数格式，根据手册第64-93行
        由于复杂性，这里改用实时行情API获取基础财务数据
        """
        # 基础财务指标映射到实时行情API可用的指标
        basic_indicators = "totalShares,totalCapital,mv,pb,pe_ttm,latest,preClose,turnoverRatio"

        # 使用实时行情API获取基础数据（更稳定可靠）
        return await self.get_real_time_quotation(codes, basic_indicators)
    
    async def get_financial_report(self, codes: str, report_type: str = "", 
                                 period: str = "") -> Dict[str, Any]:
        """获取财务报表数据（使用实时行情API获取财务指标）"""
        # 基于官方文档研究，改用实时行情API获取可靠的财务指标
        financial_indicators = "pe_ttm,pb,totalShares,totalCapital,latest,preClose,mv,roe,turnoverRatio"
        result = await self.get_real_time_quotation(codes, financial_indicators)
        
        # 添加期间信息到结果中
        if result.get('errorcode') == 0 and period:
            result['period'] = period
            result['report_type'] = report_type
        
        return result

    def _format_time_for_high_frequency(self, time_str: str) -> str:
        """格式化时间为高频API要求的格式

        根据手册第240-241行，高频序列API要求时间格式为 "YYYY-MM-DD HH:mm:ss"

        Args:
            time_str: 输入时间字符串

        Returns:
            str: 格式化后的时间字符串

        Examples:
            "2025-01-14" -> "2025-01-14 09:15:00"
            "2025-01-14 15:00:00" -> "2025-01-14 15:00:00"
        """
        if len(time_str) == 10:  # "YYYY-MM-DD" 格式
            # 开始时间默认为开盘时间，结束时间默认为收盘时间
            return f"{time_str} 09:15:00"
        elif len(time_str) == 19:  # 已包含时间部分
            return time_str
        else:
            raise ValueError(f"不支持的时间格式: {time_str}")

    def _parse_technical_indicators(self, indicators: str) -> Dict[str, str]:
        """解析技术指标参数

        为每个技术指标生成符合API要求的参数字符串
        根据手册第344-407行的技术指标规则

        Args:
            indicators: 逗号分隔的指标列表，如 "MACD,RSI,KDJ"

        Returns:
            Dict[str, str]: 指标名称到参数字符串的映射
        """

        # 预设的常用技术指标参数
        default_params = {
            # 趋势类指标
            "MACD": "12,26,9,MACD",        # 快线12日，慢线26日，信号线9日，返回MACD值
            "MA": "20",                    # 默认20日移动平均线
            "EXPMA": "12",                 # 12日指数移动平均
            "DMA": "10,50,10,DDD",         # 10日和50日平均线差
            "TRIX": "12,20,TRIX",          # 12日和20日三重指数平滑

            # 震荡类指标
            "RSI": "14",                   # 14日相对强弱指标
            "KDJ": "9,3,3,K",             # 9日KDJ的K值
            "CCI": "14",                   # 14日顺势指标
            "WR": "14",                    # 14日威廉指标
            "ROC": "12,6,ROC",            # 12日间隔，6日平滑的变动速率

            # 成交量指标
            "OBV": "OBV",                  # 能量潮
            "VR": "26",                    # 26日成交量比率
            "VRSI": "14",                  # 14日量相对强弱
            "VMACD": "12,26,9,DIFF",      # 量MACD（修复参数）
            "VMA": "20",                   # 20日量移动平均
            "VOSC": "12,26",              # 成交量震荡指标
            "VSTD": "20",                  # 20日成交量标准差

            # 支撑阻力指标
            "BOLL": "20,2,MID",           # 20日布林线中轨
            "CDP": "CDP",                  # CDP值
            "MIKE": "12,WR",              # 12日麦克指标弱阻力

            # 波动率指标
            "ATR": "14,ATR",              # 14日真实波幅
            "STD": "20",                   # 20日标准差
            "BIAS": "6"                    # 6日乖离率
        }

        calculate_params = {}

        for indicator in indicators.split(','):
            indicator = indicator.strip().upper()

            # 特殊处理MA多周期指标
            if indicator.startswith('MA') and len(indicator) > 2:
                # 提取周期数字，如MA5 -> 5, MA10 -> 10
                period_str = indicator[2:]  # 去掉"MA"前缀
                if period_str.isdigit():
                    # 使用MA作为指标名，周期作为参数
                    calculate_params['MA'] = period_str
                    continue

            if indicator in default_params:
                calculate_params[indicator] = default_params[indicator]
            else:
                # 对于未预设的指标，使用空参数（使用默认值）
                calculate_params[indicator] = ""

        return calculate_params

    async def _get_ma_indicators(self, codes: str, starttime: str, endtime: str,
                               ma_periods: List[int]) -> Dict[str, Any]:
        """获取多个周期的MA指标数据"""
        all_ma_data = {}

        for period in ma_periods:
            try:
                result = await self.get_technical_indicators(
                    codes=codes,
                    starttime=starttime,
                    endtime=endtime,
                    indicators='MA',
                    calculate={'MA': str(period)}
                )

                if result.get('errorcode') == 0:
                    # 将MA数据重命名为MA{period}
                    tables = result.get('data', {}).get('tables', [])
                    if tables:
                        table_data = tables[0].get('table', [])
                        if isinstance(table_data, list):
                            for record in table_data:
                                if 'MA' in record:
                                    record[f'MA{period}'] = record.pop('MA')

                        all_ma_data[f'MA{period}'] = table_data

            except Exception as e:
                print(f"获取MA{period}失败: {str(e)}")
                continue

        # 合并所有MA数据
        if all_ma_data:
            # 构造返回格式
            return {
                'errorcode': 0,
                'reason': 'success',
                'data': {
                    'tables': [{'table': all_ma_data}]
                }
            }
        else:
            return {
                'errorcode': -1,
                'reason': '所有MA指标获取失败'
            }

    async def get_technical_indicators(self, codes: str, starttime: str, endtime: str,
                                     indicators: str = "MACD,RSI,KDJ",
                                     calculate: Dict[str, str] = None) -> Dict[str, Any]:
        """获取技术指标数据

        使用高频序列API获取技术指标数据
        API端点: /api/v1/high_frequency

        Args:
            codes: 股票代码，支持多个代码用逗号分隔
            starttime: 开始时间
            endtime: 结束时间
            indicators: 技术指标列表

        Returns:
            Dict[str, Any]: API返回结果

        Raises:
            Exception: API调用失败或参数错误
        """

        # 1. 格式化时间参数
        starttime = self._format_time_for_high_frequency(starttime)
        endtime = self._format_time_for_high_frequency(endtime)

        # 2. 解析技术指标参数
        if calculate:
            calculate_params = calculate
        else:
            calculate_params = self._parse_technical_indicators(indicators)

        # 3. 构建API请求参数
        params = {
            "codes": codes,
            "indicators": f"open,high,low,close,volume,{indicators}",  # 包含基础数据和技术指标
            "starttime": starttime,
            "endtime": endtime,
            "functionpara": {
                "Interval": "D",           # 日线数据（可配置为1/5/15/30/60分钟）
                "Fill": "Previous",        # 数据填充方式：沿用之前数据
                "calculate": calculate_params  # 技术指标计算参数
            }
        }

        # 4. 调用API
        result = self._make_request('/api/v1/high_frequency', params)

        # 修复数据结构：如果tables在根级别，移动到data字段下
        if 'tables' in result and 'data' not in result:
            result['data'] = {'tables': result['tables']}

        return result

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查Token配置
            if not self.refresh_token:
                return {
                    'status': 'unhealthy',
                    'error': 'REFRESH_TOKEN未配置',
                    'timestamp': datetime.now().isoformat()
                }
            
            # 测试Token获取
            token = self._get_access_token()
            if not token:
                return {
                    'status': 'unhealthy',
                    'error': 'ACCESS_TOKEN获取失败',
                    'timestamp': datetime.now().isoformat()
                }
            
            return {
                'status': 'healthy',
                'token_valid': True,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
