import asyncio
import json
import os
import time
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig, CacheMode, LLMConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy, JsonCssExtractionStrategy

from python.helpers.tool import Tool, Response
from python.helpers.print_style import PrintStyle


class WebCrawler(Tool):
    """
    智能网页爬虫工具 - 基于Crawl4AI v0.6.x

    支持功能：
    - 智能网站类型识别和策略生成
    - LLM驱动的爬取策略优化
    - 多种提取方式（Markdown、结构化数据、纯文本）
    - 智能内容过滤和噪音去除
    - 自动重试和错误处理机制
    - 高性能异步爬取

    关键词：爬取、抓取、收集、采集、获取、crawl、scrape、extract
    """

    async def execute(self, url: str = "", user_intent: str = "", extract_type: str = "auto",
                     css_selector: str = "", auto_strategy: bool = True, max_retries: int = 3,
                     download_images: bool = False, download_path: str = "",
                     image_filter: str = "all", **kwargs):
        """执行智能网页爬取"""
        # 获取参数 - 优先使用直接传入的参数，然后是self.args
        url = url or self.args.get("url", "").strip()
        user_intent = user_intent or self.args.get("user_intent", "").strip()
        extract_type = extract_type if extract_type != "auto" else self.args.get("extract_type", "auto")
        css_selector = css_selector or self.args.get("css_selector", "")
        auto_strategy = auto_strategy if 'auto_strategy' not in kwargs else self.args.get("auto_strategy", True)
        max_retries = max_retries if max_retries == 3 else self.args.get("max_retries", 3)

        # 图片下载相关参数 - 优先使用传入参数
        download_images = download_images if 'download_images' not in kwargs else self.args.get("download_images", False)
        download_path = download_path or self.args.get("download_path", "")
        image_filter = image_filter if image_filter != "all" else self.args.get("image_filter", "all")
        
        # 验证URL
        if not url:
            return Response(message="❌ 请提供要爬取的URL", break_loop=False)

        # 🔧 设置当前URL供图片过滤器使用
        self.current_url = url
        
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        try:
            print(f"🕷️ 开始智能网页爬取: {url}")
            
            # 1. 网站分析和策略生成
            if auto_strategy:
                print("🧠 分析网站并生成智能爬取策略...")
                strategy_config = await self._generate_enhanced_strategy(url, user_intent, download_images)
                print(f"📋 策略: {strategy_config.get('reasoning', '智能策略已生成')}")
            else:
                strategy_config = self._create_manual_strategy(extract_type, css_selector, download_images)
            
            # 2. 创建爬取配置
            crawl_config = await self._create_crawl_config(strategy_config, user_intent, download_images, image_filter)
            browser_config = self._create_browser_config(download_images, download_path)
            
            # 3. 执行爬取（带重试机制）
            result = await self._crawl_with_retry(url, crawl_config, browser_config, max_retries)
            
            if not result or not result.success:
                error_msg = result.error_message if result else "爬取失败"
                return Response(message=f"❌ 爬取失败: {error_msg}", break_loop=False)
            
            # 4. 处理结果
            return await self._process_crawl_result(result, strategy_config, url)
            
        except Exception as e:
            PrintStyle.error(f"网页爬取异常: {e}")
            return Response(message=f"❌ 爬取异常: {str(e)}", break_loop=False)

    async def _generate_enhanced_strategy(self, url: str, user_intent: str, download_images: bool = False) -> Dict[str, Any]:
        """生成增强的爬取策略"""
        try:
            # 获取页面基本信息
            page_info = await self._analyze_page(url)
            
            # 构建策略生成提示
            strategy_prompt = self._create_strategy_prompt(url, user_intent, page_info, download_images)
            
            # 使用项目LLM生成策略
            response = await self.agent.call_utility_model(
                system="你是一个专业的网页爬取策略专家，擅长分析网站结构并生成最优的爬取策略。",
                message=strategy_prompt
            )
            
            # 解析策略响应
            strategy = self._parse_strategy_response(response)
            
            # 验证和优化策略
            return self._validate_strategy(strategy, url, user_intent, download_images)
            
        except Exception as e:
            PrintStyle.error(f"策略生成失败: {e}")
            return self._get_fallback_strategy(url, user_intent)

    async def _analyze_page(self, url: str) -> Dict[str, Any]:
        """分析页面基本信息"""
        page_info = {
            "domain": urlparse(url).netloc,
            "title": None,
            "description": None,
            "preview": None,
            "content_type": "unknown"
        }
        
        try:
            # 快速预览页面
            browser_config = BrowserConfig(headless=True, verbose=False)
            crawl_config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
            
            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=crawl_config)
                
                if result.success:
                    page_info["preview"] = result.markdown[:1000] if result.markdown else None
                    
                    # 提取标题和描述
                    if result.html:
                        import re
                        title_match = re.search(r'<title[^>]*>([^<]+)</title>', result.html, re.IGNORECASE)
                        if title_match:
                            page_info["title"] = title_match.group(1).strip()
                        
                        desc_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', result.html, re.IGNORECASE)
                        if desc_match:
                            page_info["description"] = desc_match.group(1).strip()
                            
        except Exception as e:
            PrintStyle.debug(f"页面分析失败: {e}")
            
        return page_info

    def _create_strategy_prompt(self, url: str, user_intent: str, page_info: Dict[str, Any], download_images: bool = False) -> str:
        """创建策略生成提示"""
        return f"""
你是一个专业的网页爬取策略专家。请分析以下信息并生成最优的爬取策略：

## 分析目标
- **URL**: {url}
- **域名**: {page_info.get('domain', '未知')}
- **页面标题**: {page_info.get('title', '未提供')}
- **页面描述**: {page_info.get('description', '未提供')}
- **用户意图**: {user_intent}
- **页面预览**: {page_info.get('preview', '未提供')[:400] if page_info.get('preview') else '未提供'}
- **图片下载需求**: {'是' if download_images else '否'}

## 网站类型识别与策略选择

### 1. 网站类型判断
根据URL、标题、描述和内容预览判断网站类型：
- 新闻/媒体: 提取文章、标题、作者、发布时间
- 电商/购物: 提取产品信息、价格、评价、规格
- 博客/个人: 提取文章内容、作者信息、标签
- 企业/公司: 提取公司信息、产品服务、联系方式
- 数据/统计: 提取表格数据、图表信息、统计数字
- 搜索结果: 提取结果列表、标题、链接、摘要
- 社交媒体: 提取帖子、用户信息、互动数据

### 2. 提取策略选择
- **markdown**: 适合文章、博客、新闻等文本内容丰富的页面
- **structured**: 适合产品列表、数据表格、搜索结果等结构化信息
- **text**: 适合简单文本提取或作为备选方案

### 2.1 结构化提取Schema格式要求
当选择structured类型时，extraction_schema必须包含以下字段：
- **baseSelector** (必需): 基础容器选择器，定义每个数据项的容器元素
- **fields** (必需): 字段定义数组，每个字段包含：
  - name: 字段名称
  - selector: 相对于baseSelector的CSS选择器
  - type: 数据类型 (text获取文本内容, attribute获取属性值)
  - attribute: 当type为attribute时，指定要获取的属性名

示例：对于产品列表页面
- baseSelector: ".product-item" (每个产品的容器)
- fields中的selector都相对于.product-item元素

### 3. CSS选择器优化
根据网站类型提供精确的选择器：
- 新闻网站: `article, .article-content, .post-content, main .content, .story-body`
- 电商网站: `.product, .product-item, .search-result, .listing-item, .product-card`
- 博客网站: `.post, .entry-content, .article-body, .blog-post`
- 数据网站: `table, .data-table, .grid, .list-container, .results`

### 4. 内容过滤策略
- **pruning**: 去除导航、广告、侧边栏等噪音内容
- **bm25**: 基于用户意图的关键词相关性过滤

### 5. 图片处理策略
- **如果需要下载图片**: 考虑图片的相关性和质量
- **图片过滤**: 是否排除外部图片、广告图片等
- **图片类型**: 关注与用户意图相关的图片类型

请返回详细的JSON策略配置：
{{
    "website_type": "识别的网站类型",
    "confidence": 0.95,
    "extract_type": "markdown|structured|text",
    "css_selector": "优化的CSS选择器",
    "content_filter": "pruning|bm25|none",
    "filter_threshold": 0.48,
    "wait_for": "需要等待的元素选择器（可选）",
    "js_code": "需要执行的JavaScript代码（可选）",
    "extraction_schema": {{
        "name": "数据结构名称",
        "baseSelector": "基础容器选择器(必需字段)",
        "fields": [
            {{"name": "字段名", "selector": "相对CSS选择器", "type": "text|attribute", "attribute": "属性名(仅当type为attribute时)"}},
            {{"name": "另一字段", "selector": "另一选择器", "type": "text"}}
        ]
    }},
    "image_strategy": "图片处理策略：include_all|exclude_external|exclude_all",
    "reasoning": "详细的策略选择理由，包括网站类型分析和策略依据",
    "fallback_strategy": "备选策略配置"
}}

## Schema示例说明

### 产品列表页面示例：
```json
"extraction_schema": {{
    "name": "产品信息",
    "baseSelector": ".product-item",
    "fields": [
        {{"name": "title", "selector": ".product-title", "type": "text"}},
        {{"name": "price", "selector": ".price", "type": "text"}},
        {{"name": "image", "selector": "img", "type": "attribute", "attribute": "src"}},
        {{"name": "link", "selector": "a", "type": "attribute", "attribute": "href"}}
    ]
}}
```

### 文章列表页面示例：
```json
"extraction_schema": {{
    "name": "文章信息",
    "baseSelector": ".article-item",
    "fields": [
        {{"name": "title", "selector": ".article-title", "type": "text"}},
        {{"name": "author", "selector": ".author", "type": "text"}},
        {{"name": "date", "selector": ".publish-date", "type": "text"}},
        {{"name": "summary", "selector": ".article-summary", "type": "text"}}
    ]
}}
```

**重要提醒**:
- baseSelector是必需字段，不能省略
- 所有fields中的selector都相对于baseSelector
- 如果页面有多个baseSelector匹配的元素，会为每个元素提取一组数据
"""

    def _parse_strategy_response(self, response: str) -> Dict[str, Any]:
        """解析策略响应"""
        try:
            # 清理响应
            response_clean = response.strip()
            
            # 提取JSON部分
            if "```json" in response_clean:
                json_start = response_clean.find("```json") + 7
                json_end = response_clean.find("```", json_start)
                if json_end != -1:
                    response_clean = response_clean[json_start:json_end].strip()
            elif "```" in response_clean:
                json_start = response_clean.find("```") + 3
                json_end = response_clean.find("```", json_start)
                if json_end != -1:
                    response_clean = response_clean[json_start:json_end].strip()
            
            # 找到JSON对象
            json_start = response_clean.find('{')
            json_end = response_clean.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                response_clean = response_clean[json_start:json_end]
            
            return json.loads(response_clean)
            
        except Exception as e:
            PrintStyle.error(f"策略解析失败: {e}")
            raise

    def _validate_strategy(self, strategy: Dict[str, Any], url: str, user_intent: str, download_images: bool = False) -> Dict[str, Any]:
        """验证和优化策略"""
        # 设置默认值
        strategy.setdefault("extract_type", "markdown")
        strategy.setdefault("css_selector", "")
        strategy.setdefault("content_filter", "pruning")
        strategy.setdefault("filter_threshold", 0.48)
        strategy.setdefault("confidence", 0.8)
        strategy.setdefault("image_strategy", "include_all" if download_images else "exclude_external")
        
        # 验证提取类型
        if strategy["extract_type"] not in ["markdown", "structured", "text"]:
            strategy["extract_type"] = "markdown"
        
        # 验证过滤阈值
        threshold = strategy.get("filter_threshold", 0.48)
        if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
            strategy["filter_threshold"] = 0.48
        
        return strategy

    def _get_fallback_strategy(self, url: str, user_intent: str) -> Dict[str, Any]:
        """获取备选策略"""
        domain = urlparse(url).netloc.lower()
        
        # 基于域名的简单策略
        if any(keyword in domain for keyword in ['news', 'blog', 'article']):
            return {
                "website_type": "news/blog",
                "extract_type": "markdown",
                "css_selector": "article, .content, .post",
                "content_filter": "pruning",
                "filter_threshold": 0.48,
                "reasoning": "基于域名判断为新闻/博客网站"
            }
        elif any(keyword in domain for keyword in ['shop', 'store', 'buy', 'product']):
            return {
                "website_type": "ecommerce",
                "extract_type": "structured",
                "css_selector": ".product, .item, .listing",
                "content_filter": "pruning",
                "filter_threshold": 0.5,
                "reasoning": "基于域名判断为电商网站"
            }
        else:
            return {
                "website_type": "general",
                "extract_type": "markdown",
                "css_selector": "main, .content, article",
                "content_filter": "pruning",
                "filter_threshold": 0.48,
                "reasoning": "通用网站策略"
            }

    def _create_manual_strategy(self, extract_type: str, css_selector: str, download_images: bool = False) -> Dict[str, Any]:
        """创建手动策略"""
        return {
            "website_type": "manual",
            "extract_type": extract_type if extract_type != "auto" else "markdown",
            "css_selector": css_selector,
            "content_filter": "pruning",
            "filter_threshold": 0.48,
            "image_strategy": "include_all" if download_images else "exclude_external",
            "reasoning": "用户手动指定策略"
        }

    def _get_fallback_selectors(self, url: str, original_selector: str) -> list:
        """获取回退选择器列表"""
        fallback_selectors = []

        # 添加原始选择器
        if original_selector:
            fallback_selectors.append(original_selector)

        # 基于URL判断网站类型，添加特定选择器
        if "unsplash.com" in url:
            fallback_selectors.extend([
                "img[src*='images.unsplash.com']",  # Unsplash图片URL
                "img[data-testid*='photo']",  # 测试ID属性
                "img[alt*='photo']",  # 包含photo的alt属性
                "main img",  # 主要内容区域的图片
                "article img",  # 文章中的图片
                "figure img",  # figure元素中的图片
            ])
        elif "pixabay.com" in url:
            fallback_selectors.extend([
                "img[src*='pixabay.com']",
                "img[data-lazy]",
                ".item img",
            ])
        elif "pexels.com" in url:
            fallback_selectors.extend([
                "img[src*='pexels.com']",
                ".photo-item img",
                "article img",
            ])

        # 添加通用选择器
        fallback_selectors.extend([
            "img[src]",  # 有src属性的图片
            "picture img",  # picture元素中的图片
            "[data-src]",  # 懒加载图片
            "img",  # 最基本的选择器
        ])

        # 去重并保持顺序
        seen = set()
        unique_selectors = []
        for selector in fallback_selectors:
            if selector not in seen:
                seen.add(selector)
                unique_selectors.append(selector)

        return unique_selectors

    async def _create_crawl_config(self, strategy: Dict[str, Any], user_intent: str, download_images: bool = False, image_filter: str = "all") -> CrawlerRunConfig:
        """创建爬取配置"""
        # 创建提取策略
        extraction_strategy = None
        extract_type = strategy.get("extract_type", "markdown")

        if extract_type == "structured" and strategy.get("extraction_schema"):
            # LLM结构化提取
            extraction_strategy = LLMExtractionStrategy(
                llm_config=LLMConfig(
                    provider=self._get_llm_provider(),
                    api_token=self._get_llm_api_key()
                ),
                schema=strategy["extraction_schema"],
                extraction_type="schema",
                instruction=f"Extract structured data according to schema. User intent: {user_intent}",
                chunk_token_threshold=1200,
                apply_chunking=True,
                input_format="html"
            )
        elif strategy.get("css_selector"):
            # CSS选择器提取
            extraction_strategy = JsonCssExtractionStrategy(
                schema={},
                css_selector=strategy["css_selector"]
            )

        # 在v0.6.x中，内容过滤通过CrawlerRunConfig的参数实现
        # 不再需要单独的content_filter对象

        # 创建爬取配置 - 使用v0.6.x的内置过滤参数
        filter_type = strategy.get("content_filter", "none")
        image_strategy = strategy.get("image_strategy", "exclude_external")

        # 图片处理设置
        exclude_external_images = image_strategy in ["exclude_external", "exclude_all"] or image_filter == "internal"
        exclude_all_images = image_strategy == "exclude_all" and not download_images
        wait_for_images = download_images  # 如果要下载图片，等待图片加载

        config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=extraction_strategy,
            css_selector=strategy.get("css_selector"),
            wait_for=strategy.get("wait_for"),
            js_code=strategy.get("js_code"),
            page_timeout=30000,
            delay_before_return_html=2.0,
            # 内容过滤参数
            word_count_threshold=10 if filter_type == "pruning" else 0,
            excluded_tags=['nav', 'footer', 'header'] if filter_type == "pruning" else [],
            exclude_external_links=filter_type == "pruning",
            exclude_social_media_links=filter_type == "pruning",
            # 图片处理参数
            exclude_external_images=exclude_external_images,
            exclude_all_images=exclude_all_images,
            wait_for_images=wait_for_images
        )

        return config

    def _create_browser_config(self, download_images: bool = False, download_path: str = "") -> BrowserConfig:
        """创建浏览器配置"""
        # 🔧 修复: 正确处理用户指定的下载路径
        if download_images:
            if download_path:
                # 用户指定了具体文件路径，提取目录部分
                if download_path.startswith("/a0/"):
                    from python.helpers import files
                    download_path = files.fix_dev_path(download_path)

                # 如果是文件路径，提取目录
                if download_path.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                    download_dir = os.path.dirname(download_path)
                else:
                    download_dir = download_path

                # 确保目录存在
                os.makedirs(download_dir, exist_ok=True)
                PrintStyle.debug(f"WebCrawler: 使用用户指定下载目录: {download_dir}")
            else:
                # 用户未指定路径，使用最优路径
                download_dir = self._get_optimal_download_path()
                os.makedirs(download_dir, exist_ok=True)
                PrintStyle.debug(f"WebCrawler: 使用最优下载目录: {download_dir}")
        else:
            download_dir = None

        return BrowserConfig(
            headless=True,
            verbose=False,
            # 反检测设置
            user_agent_mode="random",
            # 性能优化
            ignore_https_errors=True,
            # 媒体处理和下载
            accept_downloads=download_images,
            downloads_path=download_dir if download_images else None,
            # 视口设置
            viewport_width=1920,
            viewport_height=1080
        )

    async def _crawl_with_retry(self, url: str, crawl_config: CrawlerRunConfig,
                               browser_config: BrowserConfig, max_retries: int = 3):
        """带重试机制的爬取"""
        last_error = None
        original_css_selector = getattr(crawl_config.extraction_strategy, 'css_selector', None) if crawl_config.extraction_strategy else None

        for attempt in range(max_retries):
            try:
                print(f"🔄 尝试 {attempt + 1}/{max_retries}")

                async with AsyncWebCrawler(config=browser_config) as crawler:
                    result = await crawler.arun(url=url, config=crawl_config)

                    if result.success:
                        print(f"✅ 爬取成功")
                        return result
                    else:
                        last_error = result.error_message
                        print(f"⚠️ 尝试 {attempt + 1} 失败: {last_error}")

                        # 降级策略
                        if attempt < max_retries - 1:
                            crawl_config = self._create_fallback_config(crawl_config, attempt, url, original_css_selector)

            except Exception as e:
                last_error = str(e)
                print(f"⚠️ 尝试 {attempt + 1} 异常: {e}")

                # 🔧 特殊处理CSS选择器超时错误
                if "Wait condition failed" in str(e) and "waiting for selector" in str(e):
                    print(f"🔍 检测到CSS选择器超时，尝试回退策略")
                    if attempt < max_retries - 1:
                        crawl_config = self._create_fallback_config(crawl_config, attempt, url, original_css_selector)

                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避

        # 所有尝试都失败了
        print(f"❌ 所有重试都失败了，最后错误: {last_error}")
        return None

    def _create_fallback_config(self, original_config: CrawlerRunConfig, attempt: int = 0, url: str = "", original_css_selector: str = None) -> CrawlerRunConfig:
        """创建降级配置"""
        print(f"🔄 创建降级配置 (尝试 {attempt + 1})")

        if attempt == 0 and original_css_selector:
            # 第一次重试：尝试回退选择器
            fallback_selectors = self._get_fallback_selectors(url, original_css_selector)
            if len(fallback_selectors) > 1:  # 有回退选择器可用
                next_selector = fallback_selectors[1]  # 使用第二个选择器
                print(f"🔍 尝试回退选择器: {next_selector}")

                # 创建新的CSS提取策略
                from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
                new_extraction_strategy = JsonCssExtractionStrategy(
                    schema={},
                    css_selector=next_selector
                )

                return CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=new_extraction_strategy,
                    wait_for=None,  # 移除等待条件
                    page_timeout=20000,  # 稍微增加超时时间
                    delay_before_return_html=2.0,
                    word_count_threshold=0,
                    excluded_tags=[],
                    exclude_external_links=False,
                    exclude_social_media_links=False
                )

        # 第二次重试或没有回退选择器：完全移除CSS选择器
        print(f"🔄 使用通用降级策略")
        return CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=None,  # 移除复杂的提取策略
            css_selector=None,         # 移除CSS选择器
            wait_for=None,             # 移除等待
            js_code=None,              # 移除JavaScript
            page_timeout=15000,        # 减少超时时间
            delay_before_return_html=1.0,
            # 移除所有过滤
            word_count_threshold=0,
            excluded_tags=[],
            exclude_external_links=False,
            exclude_social_media_links=False
        )

    async def _process_crawl_result(self, result, strategy: Dict[str, Any], url: str) -> Response:
        """处理爬取结果"""
        try:
            # 获取内容
            content = ""
            extract_type = strategy.get("extract_type", "markdown")

            if extract_type == "structured" and result.extracted_content:
                # 结构化数据
                content = result.extracted_content
                content_type = "结构化数据"
            elif hasattr(result, 'markdown') and hasattr(result.markdown, 'fit_markdown') and result.markdown.fit_markdown:
                # 过滤后的markdown (新API)
                content = result.markdown.fit_markdown
                content_type = "过滤后内容"
            elif hasattr(result, 'fit_markdown') and result.fit_markdown:
                # 过滤后的markdown (旧API兼容)
                content = result.fit_markdown
                content_type = "过滤后内容"
            elif hasattr(result, 'markdown') and result.markdown:
                # 原始markdown
                content = result.markdown
                content_type = "Markdown内容"
            elif hasattr(result, 'cleaned_html') and result.cleaned_html:
                # 清理后的HTML
                content = result.cleaned_html[:5000]  # 限制长度
                content_type = "清理后HTML"
            else:
                content = "无法提取内容"
                content_type = "错误"

            # 生成统计信息
            stats = self._generate_stats(result, strategy)

            # 处理图片信息
            images_info = self._process_images(result)
            downloads_info = self._process_downloads(result)

            # 构建响应消息
            response_msg = f"""
🎯 **网页爬取完成**

📊 **爬取统计**:
{stats}

📋 **策略信息**:
- 网站类型: {strategy.get('website_type', '未知')}
- 提取方式: {extract_type}
- 内容类型: {content_type}
- 策略理由: {strategy.get('reasoning', '无')}

{images_info}

{downloads_info}

📄 **爬取内容**:
{content[:3000]}{'...' if len(content) > 3000 else ''}
"""

            # 保存详细结果
            kvps = {
                "url": url,
                "content": content,
                "content_type": content_type,
                "extract_type": extract_type,
                "website_type": strategy.get('website_type', '未知'),
                "word_count": len(content.split()) if content else 0,
                "char_count": len(content) if content else 0,
                "strategy": strategy.get('reasoning', '无'),
                "success": True
            }

            # 添加图片信息
            if hasattr(result, 'media') and result.media:
                images = result.media.get('images', [])
                kvps["images_count"] = len(images)
                kvps["images"] = images[:10]  # 保存前10张图片信息

            # 添加下载文件信息
            if hasattr(result, 'downloaded_files') and result.downloaded_files:
                kvps["downloaded_files_count"] = len(result.downloaded_files)
                kvps["downloaded_files"] = result.downloaded_files

                # 🔧 验证下载文件是否实际存在
                verified_files = []
                for file_path in result.downloaded_files:
                    if os.path.exists(file_path):
                        verified_files.append(file_path)
                        PrintStyle.debug(f"WebCrawler: 验证下载文件存在: {file_path}")
                    else:
                        PrintStyle.error(f"WebCrawler: 下载文件不存在: {file_path}")

                kvps["verified_files_count"] = len(verified_files)
                kvps["verified_files"] = verified_files

            return Response(message=response_msg, break_loop=False)

        except Exception as e:
            PrintStyle.error(f"结果处理失败: {e}")
            return Response(message=f"❌ 结果处理失败: {str(e)}", break_loop=False)

    def _generate_stats(self, result, strategy: Dict[str, Any]) -> str:
        """生成统计信息"""
        stats = []

        if hasattr(result, 'markdown') and result.markdown:
            word_count = len(result.markdown.split())
            char_count = len(result.markdown)
            stats.append(f"- 内容长度: {word_count} 词, {char_count} 字符")

        if hasattr(result, 'links') and result.links:
            stats.append(f"- 链接数量: {len(result.links)}")

        if hasattr(result, 'media') and result.media:
            stats.append(f"- 媒体文件: {len(result.media)}")

        stats.append(f"- 置信度: {strategy.get('confidence', 0.8):.1%}")

        return "\n".join(stats) if stats else "- 无统计信息"

    def _filter_main_images(self, images: list, url: str) -> list:
        """过滤出主要图片"""
        if not images:
            return []

        # 1. 基于URL过滤
        main_images = []
        for img in images:
            src = img.get('src', '')
            alt = img.get('alt', '').lower()

            # 过滤UI元素
            if any(keyword in src.lower() for keyword in ['icon', 'logo', 'avatar', 'thumb', 'profile']):
                continue
            if any(keyword in alt for keyword in ['icon', 'logo', 'avatar', 'profile', 'button']):
                continue

            # Unsplash特定过滤
            if 'unsplash.com' in url:
                if 'images.unsplash.com' in src:
                    # 优先选择高质量图片
                    if any(param in src for param in ['w=1920', 'w=1080', 'q=80', 'q=90']):
                        img['priority'] = 10
                    elif any(param in src for param in ['w=400', 'w=600']):
                        img['priority'] = 5
                    else:
                        img['priority'] = 3
                else:
                    img['priority'] = 1
            else:
                img['priority'] = 3

            main_images.append(img)

        # 2. 基于尺寸过滤 (如果有尺寸信息)
        filtered_images = []
        for img in main_images:
            width = img.get('width', 0)
            height = img.get('height', 0)

            # 过滤太小的图片
            if width and height:
                if width < 200 or height < 200:
                    continue

            filtered_images.append(img)

        # 3. 按优先级排序
        filtered_images.sort(key=lambda x: x.get('priority', 0), reverse=True)

        # 4. 返回前5张最重要的图片
        return filtered_images[:5]

    def _identify_primary_image(self, images: list, url: str) -> dict:
        """识别主要图片"""
        filtered_images = self._filter_main_images(images, url)

        if not filtered_images:
            return None

        # 返回优先级最高的图片
        primary = filtered_images[0]

        # 添加置信度评分
        confidence = 0.5
        if primary.get('priority', 0) >= 10:
            confidence = 0.9
        elif primary.get('priority', 0) >= 5:
            confidence = 0.7

        primary['confidence'] = confidence
        primary['is_primary'] = True

        return primary

    def _process_images(self, result) -> str:
        """增强的图片处理"""
        if not hasattr(result, 'media') or not result.media:
            return ""

        images = result.media.get('images', [])
        if not images:
            return ""

        # 识别主要图片
        primary_image = self._identify_primary_image(images, getattr(self, 'current_url', ''))
        filtered_images = self._filter_main_images(images, getattr(self, 'current_url', ''))

        image_info = [f"🖼️ **图片分析**:"]
        image_info.append(f"- 总计发现: {len(images)} 张图片")
        image_info.append(f"- 主要图片: {len(filtered_images)} 张")

        if primary_image:
            image_info.append(f"\n🎯 **主图片**:")
            src = primary_image.get('src', '')
            alt = primary_image.get('alt', '无描述')
            confidence = primary_image.get('confidence', 0)
            image_info.append(f"- URL: {src[:80]}{'...' if len(src) > 80 else ''}")
            image_info.append(f"- 描述: {alt}")
            image_info.append(f"- 置信度: {confidence:.1%}")

        if len(filtered_images) > 1:
            image_info.append(f"\n📋 **其他主要图片**:")
            for i, img in enumerate(filtered_images[1:4], 2):  # 显示2-4张
                alt = img.get('alt', '无描述')
                image_info.append(f"  [{i}] {alt[:40]}{'...' if len(alt) > 40 else ''}")

        if len(images) > len(filtered_images):
            filtered_count = len(images) - len(filtered_images)
            image_info.append(f"\n🗑️ **已过滤**: {filtered_count} 张辅助图片 (推荐、缩略图、UI元素等)")

        return "\n".join(image_info)

    def _process_downloads(self, result) -> str:
        """处理下载文件信息"""
        if not hasattr(result, 'downloaded_files') or not result.downloaded_files:
            return ""

        download_info = [f"📥 **下载文件**:"]
        download_info.append(f"- 下载文件: {len(result.downloaded_files)} 个")

        # 🔧 添加实际下载目录信息
        if result.downloaded_files:
            import os
            first_file = result.downloaded_files[0]
            download_dir = os.path.dirname(first_file)
            download_info.append(f"- 保存目录: {download_dir}")

        for i, file_path in enumerate(result.downloaded_files[:5]):
            try:
                import os
                file_size = os.path.getsize(file_path)
                file_name = os.path.basename(file_path)
                download_info.append(f"  [{i+1}] {file_name} ({file_size} bytes)")
            except:
                download_info.append(f"  [{i+1}] {file_path}")

        if len(result.downloaded_files) > 5:
            download_info.append(f"  ... 还有 {len(result.downloaded_files) - 5} 个文件")

        return "\n".join(download_info)

    def _get_llm_provider(self) -> str:
        """获取LLM提供商"""
        try:
            provider = self.agent.config.utility_model.provider.value.lower()
            model = self.agent.config.utility_model.name
            return f"{provider}/{model}"
        except:
            return "openai/gpt-4o-mini"  # 默认值

    def _get_llm_api_key(self) -> str:
        """获取LLM API密钥"""
        try:
            provider = self.agent.config.utility_model.provider.value.lower()
            return self._get_api_key_for_provider(provider)
        except:
            return os.getenv("OPENAI_API_KEY", "")

    def _get_api_key_for_provider(self, provider: str) -> str:
        """根据提供商获取API密钥"""
        key_mapping = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY",
            "deepseek": "DEEPSEEK_API_KEY",
            "siliconflow": "SILICONFLOW_API_KEY",
            "volcengine": "VOLCENGINE_API_KEY"
        }

        env_key = key_mapping.get(provider, "OPENAI_API_KEY")
        return os.getenv(env_key, "")

    def _get_optimal_download_path(self) -> str:
        """获取最优的下载路径"""
        import os
        from pathlib import Path

        # 优先级1: 项目目录下的downloads文件夹
        current_dir = os.getcwd()
        if "agent-zero" in current_dir:
            project_downloads = os.path.join(current_dir, "downloads", "images")
            try:
                os.makedirs(project_downloads, exist_ok=True)
                # 测试写入权限
                test_file = os.path.join(project_downloads, ".test_write")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                return project_downloads
            except:
                pass

        # 优先级2: 检查是否在WSL环境，使用Linux原生路径
        try:
            with open('/proc/version', 'r') as f:
                version_info = f.read()
            if 'microsoft' in version_info.lower() or 'wsl' in version_info.lower():
                # WSL环境，使用Linux原生路径
                linux_downloads = os.path.join(os.path.expanduser("~"), "crawl4ai_downloads", "images")
                try:
                    os.makedirs(linux_downloads, exist_ok=True)
                    test_file = os.path.join(linux_downloads, ".test_write")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    return linux_downloads
                except:
                    pass
        except:
            pass

        # 优先级3: 项目tmp目录
        try:
            tmp_downloads = os.path.join(current_dir, "tmp", "downloads", "images")
            os.makedirs(tmp_downloads, exist_ok=True)
            test_file = os.path.join(tmp_downloads, ".test_write")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return tmp_downloads
        except:
            pass

        # 优先级4: 用户家目录（原始默认路径）
        try:
            home_downloads = os.path.join(Path.home(), ".crawl4ai", "downloads", "images")
            os.makedirs(home_downloads, exist_ok=True)
            test_file = os.path.join(home_downloads, ".test_write")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return home_downloads
        except:
            pass

        # 最后备选: 系统临时目录
        import tempfile
        return os.path.join(tempfile.gettempdir(), "agent_zero_downloads", "images")
