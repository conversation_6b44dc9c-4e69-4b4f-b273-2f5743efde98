"""
Sequential Thinking - 序列化思维工具 (SEQUENTIAL THINKING)
结构化分析专用工具，当用户说"sequential thinking"、"系统"、"结构"、"分步"、"逻辑"时使用此工具
关键词触发: sequential thinking, systematic, structured, 系统, 结构, 分步, 逻辑, 分析, 框架
"""
import asyncio
import json
from python.helpers.tool import Tool, Response
from python.helpers import files
import re
from datetime import datetime


class SequentialThinking(Tool):
    """
    🧠 SEQUENTIAL THINKING - 序列化思维工具

    ⚠️ 重要：当用户明确说"sequential thinking"时，必须使用此工具！

    适用场景：
    - 用户说"sequential thinking"、"sequential analysis"
    - 需要"系统"、"结构"、"分步"、"逻辑"的分析
    - 要求"systematic analysis"、"structured thinking"的场景

    功能特点：
    - 5步结构化分析流程（问题分解→结构分析→逐步推理→结论整合→报告生成）
    - 多种分析框架（PDCA、鱼骨图、决策树等）
    - 系统性问题解决方案
    - 逻辑推理和论证
    """

    async def execute(self, problem: str = "", **kwargs):
        """
        🚀 执行序列化思维分析 - SEQUENTIAL THINKING

        当检测到以下关键词时使用：
        - "sequential thinking", "sequential analysis"
        - "系统", "结构", "分步", "逻辑", "分析", "框架"
        - "systematic", "structured", "logical", "analysis"

        执行5步结构化分析：问题分解→结构分析→逐步推理→结论整合→报告生成
        """
        if not problem:
            return Response(message="请提供需要分析的问题或主题", break_loop=False)

        try:
            print("🧠 启动序列化思维分析...")

            # 第一步：问题理解与分解
            print("📋 第1步: 问题理解与分解...")
            problem_analysis = await self._analyze_problem(problem)

            # 第二步：结构化分析
            print("🔍 第2步: 结构化分析...")
            structured_analysis = await self._structured_analysis(problem, problem_analysis)

            # 第三步：逐步推理
            print("⚡ 第3步: 逐步推理...")
            step_by_step_reasoning = await self._step_by_step_reasoning(structured_analysis)

            # 第四步：结论整合
            print("📊 第4步: 结论整合...")
            integrated_conclusion = await self._integrate_conclusions(
                problem, problem_analysis, structured_analysis, step_by_step_reasoning
            )

            # 第五步：生成最终报告
            print("📝 第5步: 生成分析报告...")
            final_report = self._generate_final_report(
                problem, problem_analysis, structured_analysis,
                step_by_step_reasoning, integrated_conclusion
            )

            return Response(message=final_report, break_loop=False)

        except Exception as e:
            error_msg = f"序列化思维分析失败: {str(e)}"
            print(f"❌ {error_msg}")
            return Response(message=error_msg, break_loop=False)

    async def _analyze_problem(self, problem: str) -> dict:
        """第一步：问题理解与分解"""
        analysis = {
            'original_problem': problem,
            'problem_type': self._identify_problem_type(problem),
            'key_components': self._extract_key_components(problem),
            'sub_problems': self._decompose_problem(problem),
            'complexity_level': self._assess_complexity(problem),
            'required_approaches': self._identify_required_approaches(problem)
        }

        return analysis

    def _identify_problem_type(self, problem: str) -> str:
        """识别问题类型"""
        problem_lower = problem.lower()

        if any(keyword in problem_lower for keyword in ['如何', '怎么', '方法', '步骤']):
            return "方法论问题"
        elif any(keyword in problem_lower for keyword in ['为什么', '原因', '机制', '原理']):
            return "因果分析问题"
        elif any(keyword in problem_lower for keyword in ['比较', '对比', '差异', '优缺点']):
            return "比较分析问题"
        elif any(keyword in problem_lower for keyword in ['评估', '评价', '判断', '选择']):
            return "评估决策问题"
        elif any(keyword in problem_lower for keyword in ['预测', '趋势', '未来', '发展']):
            return "预测分析问题"
        elif any(keyword in problem_lower for keyword in ['设计', '规划', '方案', '策略']):
            return "设计规划问题"
        else:
            return "综合分析问题"

    def _extract_key_components(self, problem: str) -> list:
        """提取关键组成部分"""
        components = []

        # 提取名词短语作为关键组件
        # 简化版本：基于常见模式提取
        words = problem.split()

        # 查找重要的名词和概念
        important_patterns = [
            r'[\u4e00-\u9fff]+技术',  # 中文+技术
            r'[\u4e00-\u9fff]+系统',  # 中文+系统
            r'[\u4e00-\u9fff]+方法',  # 中文+方法
            r'[\u4e00-\u9fff]+模式',  # 中文+模式
            r'[\u4e00-\u9fff]+策略',  # 中文+策略
        ]

        for pattern in important_patterns:
            matches = re.findall(pattern, problem)
            components.extend(matches)

        # 如果没有找到特定模式，提取关键词
        if not components:
            key_words = [word for word in words if len(word) > 1]
            components = key_words[:5]  # 取前5个关键词

        return list(set(components))  # 去重

    def _decompose_problem(self, problem: str) -> list:
        """分解问题为子问题"""
        sub_problems = []
        problem_type = self._identify_problem_type(problem)

        if problem_type == "方法论问题":
            sub_problems = [
                "明确目标和需求",
                "分析现状和条件",
                "制定具体步骤",
                "识别潜在风险",
                "制定评估标准"
            ]
        elif problem_type == "因果分析问题":
            sub_problems = [
                "识别现象和结果",
                "分析直接原因",
                "探索根本原因",
                "分析影响因素",
                "验证因果关系"
            ]
        elif problem_type == "比较分析问题":
            sub_problems = [
                "确定比较维度",
                "收集对比数据",
                "分析相似点",
                "分析差异点",
                "得出比较结论"
            ]
        elif problem_type == "评估决策问题":
            sub_problems = [
                "明确评估标准",
                "收集相关信息",
                "分析各选项优缺点",
                "权衡利弊得失",
                "做出最终决策"
            ]
        else:
            # 通用分解方式
            sub_problems = [
                "问题背景分析",
                "关键要素识别",
                "深入分析研究",
                "方案制定评估",
                "结论总结建议"
            ]

        return sub_problems

    def _assess_complexity(self, problem: str) -> str:
        """评估问题复杂度"""
        complexity_indicators = {
            '简单': ['基础', '简单', '入门', '概念'],
            '中等': ['分析', '比较', '评估', '方法'],
            '复杂': ['系统', '综合', '深入', '全面', '战略', '架构'],
            '高度复杂': ['多维', '跨领域', '创新', '变革', '生态']
        }

        problem_lower = problem.lower()

        for level, indicators in complexity_indicators.items():
            if any(indicator in problem_lower for indicator in indicators):
                return level

        # 基于问题长度和关键词数量评估
        if len(problem) > 100:
            return "复杂"
        elif len(problem) > 50:
            return "中等"
        else:
            return "简单"

    def _identify_required_approaches(self, problem: str) -> list:
        """识别所需的分析方法"""
        approaches = []
        problem_lower = problem.lower()

        approach_keywords = {
            '逻辑分析': ['逻辑', '推理', '因果', '关系'],
            '数据分析': ['数据', '统计', '量化', '指标'],
            '系统分析': ['系统', '整体', '结构', '架构'],
            '比较分析': ['比较', '对比', '差异', '优劣'],
            '历史分析': ['历史', '发展', '演变', '趋势'],
            '案例分析': ['案例', '实例', '经验', '实践'],
            '理论分析': ['理论', '模型', '框架', '原理']
        }

        for approach, keywords in approach_keywords.items():
            if any(keyword in problem_lower for keyword in keywords):
                approaches.append(approach)

        if not approaches:
            approaches = ['逻辑分析', '系统分析']  # 默认方法

        return approaches

    async def _structured_analysis(self, problem: str, problem_analysis: dict) -> dict:
        """第二步：结构化分析"""
        analysis = {
            'framework': self._select_analysis_framework(problem_analysis),
            'dimensions': self._identify_analysis_dimensions(problem, problem_analysis),
            'relationships': self._analyze_relationships(problem_analysis),
            'constraints': self._identify_constraints(problem),
            'assumptions': self._identify_assumptions(problem)
        }

        return analysis

    def _select_analysis_framework(self, problem_analysis: dict) -> str:
        """选择分析框架"""
        problem_type = problem_analysis['problem_type']
        complexity = problem_analysis['complexity_level']

        frameworks = {
            '方法论问题': 'PDCA循环框架',
            '因果分析问题': '鱼骨图分析框架',
            '比较分析问题': '对比矩阵框架',
            '评估决策问题': '决策树框架',
            '预测分析问题': 'SWOT分析框架',
            '设计规划问题': '设计思维框架',
            '综合分析问题': '系统思维框架'
        }

        return frameworks.get(problem_type, '系统思维框架')

    def _identify_analysis_dimensions(self, problem: str, problem_analysis: dict) -> list:
        """识别分析维度"""
        dimensions = []

        # 基于问题类型确定维度
        problem_type = problem_analysis['problem_type']

        if problem_type == "方法论问题":
            dimensions = ['可行性', '效率性', '成本效益', '风险控制', '可持续性']
        elif problem_type == "因果分析问题":
            dimensions = ['时间维度', '空间维度', '主体维度', '环境维度', '机制维度']
        elif problem_type == "比较分析问题":
            dimensions = ['功能性', '性能性', '经济性', '可用性', '可靠性']
        elif problem_type == "评估决策问题":
            dimensions = ['重要性', '紧急性', '可行性', '影响性', '资源需求']
        else:
            dimensions = ['技术维度', '经济维度', '社会维度', '环境维度', '时间维度']

        return dimensions

    def _analyze_relationships(self, problem_analysis: dict) -> dict:
        """分析要素关系"""
        components = problem_analysis['key_components']

        relationships = {
            'core_relationships': [],
            'dependency_chains': [],
            'feedback_loops': [],
            'conflict_points': []
        }

        # 简化的关系分析
        if len(components) >= 2:
            relationships['core_relationships'] = [
                f"{components[0]} 与 {components[1]} 的相互作用",
                f"{components[0]} 对整体系统的影响"
            ]

        return relationships

    def _identify_constraints(self, problem: str) -> list:
        """识别约束条件"""
        constraints = []
        problem_lower = problem.lower()

        constraint_keywords = {
            '时间约束': ['时间', '期限', '截止', '紧急'],
            '资源约束': ['资源', '预算', '成本', '人力'],
            '技术约束': ['技术', '能力', '水平', '限制'],
            '政策约束': ['政策', '法规', '规定', '要求'],
            '环境约束': ['环境', '条件', '背景', '现状']
        }

        for constraint_type, keywords in constraint_keywords.items():
            if any(keyword in problem_lower for keyword in keywords):
                constraints.append(constraint_type)

        return constraints if constraints else ['一般约束条件']

    def _identify_assumptions(self, problem: str) -> list:
        """识别基本假设"""
        assumptions = [
            "假设相关信息是准确和完整的",
            "假设分析环境相对稳定",
            "假设有足够的资源支持分析",
            "假设分析结果将被合理使用"
        ]

        return assumptions

    async def _step_by_step_reasoning(self, structured_analysis: dict) -> dict:
        """第三步：逐步推理"""
        reasoning = {
            'reasoning_steps': [],
            'logical_flow': [],
            'evidence_analysis': [],
            'intermediate_conclusions': []
        }

        framework = structured_analysis['framework']
        dimensions = structured_analysis['dimensions']

        # 基于框架进行逐步推理
        for i, dimension in enumerate(dimensions, 1):
            step = {
                'step_number': i,
                'dimension': dimension,
                'analysis_focus': f"从{dimension}角度进行深入分析",
                'reasoning_process': f"运用{framework}分析{dimension}的关键要素",
                'preliminary_finding': f"在{dimension}方面的初步发现和洞察"
            }
            reasoning['reasoning_steps'].append(step)

        return reasoning

    async def _integrate_conclusions(self, problem: str, problem_analysis: dict,
                                   structured_analysis: dict, reasoning: dict) -> dict:
        """第四步：结论整合"""
        conclusion = {
            'main_findings': self._extract_main_findings(reasoning),
            'key_insights': self._generate_key_insights(problem_analysis, structured_analysis),
            'recommendations': self._generate_recommendations(problem, problem_analysis),
            'action_items': self._generate_action_items(problem_analysis),
            'risk_assessment': self._assess_risks(structured_analysis),
            'success_factors': self._identify_success_factors(problem_analysis)
        }

        return conclusion

    def _extract_main_findings(self, reasoning: dict) -> list:
        """提取主要发现"""
        findings = []

        for step in reasoning['reasoning_steps']:
            finding = f"在{step['dimension']}维度上: {step['preliminary_finding']}"
            findings.append(finding)

        return findings

    def _generate_key_insights(self, problem_analysis: dict, structured_analysis: dict) -> list:
        """生成关键洞察"""
        insights = []

        # 基于问题类型生成洞察
        problem_type = problem_analysis['problem_type']
        framework = structured_analysis['framework']

        insights.append(f"采用{framework}能够有效解决{problem_type}")
        insights.append(f"问题的复杂度为{problem_analysis['complexity_level']}，需要相应的分析深度")

        if problem_analysis['key_components']:
            insights.append(f"关键要素{problem_analysis['key_components'][0]}起到核心作用")

        return insights

    def _generate_recommendations(self, problem: str, problem_analysis: dict) -> list:
        """生成建议"""
        recommendations = []

        problem_type = problem_analysis['problem_type']

        if problem_type == "方法论问题":
            recommendations = [
                "建议采用分阶段实施的方法",
                "建立清晰的里程碑和检查点",
                "确保充分的资源配置和支持"
            ]
        elif problem_type == "因果分析问题":
            recommendations = [
                "深入调研根本原因",
                "建立因果关系验证机制",
                "制定针对性的解决方案"
            ]
        else:
            recommendations = [
                "采用系统性的分析方法",
                "确保多角度全面考虑",
                "建立持续改进机制"
            ]

        return recommendations

    def _generate_action_items(self, problem_analysis: dict) -> list:
        """生成行动项"""
        action_items = []

        for i, sub_problem in enumerate(problem_analysis['sub_problems'], 1):
            action_items.append(f"行动项{i}: 针对'{sub_problem}'制定具体实施计划")

        return action_items

    def _assess_risks(self, structured_analysis: dict) -> list:
        """评估风险"""
        risks = []

        constraints = structured_analysis['constraints']

        for constraint in constraints:
            risks.append(f"来自{constraint}的潜在风险需要重点关注")

        risks.append("分析结果的准确性依赖于信息的完整性")
        risks.append("外部环境变化可能影响分析结论的有效性")

        return risks

    def _identify_success_factors(self, problem_analysis: dict) -> list:
        """识别成功因素"""
        success_factors = []

        approaches = problem_analysis['required_approaches']

        for approach in approaches:
            success_factors.append(f"有效运用{approach}是成功的关键")

        success_factors.extend([
            "充分的前期准备和规划",
            "持续的监控和调整",
            "相关方的积极参与和支持"
        ])

        return success_factors

    def _generate_final_report(self, problem: str, problem_analysis: dict,
                             structured_analysis: dict, reasoning: dict,
                             conclusion: dict) -> str:
        """生成最终分析报告"""

        report = f"""
🧠 序列化思维分析报告
{'='*50}

📋 原始问题: {problem}

🔍 问题分析:
• 问题类型: {problem_analysis['problem_type']}
• 复杂程度: {problem_analysis['complexity_level']}
• 关键组件: {', '.join(problem_analysis['key_components'])}
• 分析方法: {', '.join(problem_analysis['required_approaches'])}

🏗️ 结构化分析:
• 分析框架: {structured_analysis['framework']}
• 分析维度: {', '.join(structured_analysis['dimensions'])}
• 约束条件: {', '.join(structured_analysis['constraints'])}

⚡ 逐步推理过程:
{self._format_reasoning_steps(reasoning['reasoning_steps'])}

📊 主要发现:
{self._format_list(conclusion['main_findings'])}

💡 关键洞察:
{self._format_list(conclusion['key_insights'])}

🎯 建议方案:
{self._format_list(conclusion['recommendations'])}

📝 行动计划:
{self._format_list(conclusion['action_items'])}

⚠️ 风险评估:
{self._format_list(conclusion['risk_assessment'])}

✅ 成功因素:
{self._format_list(conclusion['success_factors'])}

📈 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎉 序列化思维分析完成！本报告提供了系统性的分析框架和具体的行动指导。
"""

        return report.strip()

    def _format_reasoning_steps(self, steps: list) -> str:
        """格式化推理步骤"""
        formatted_steps = []
        for step in steps:
            formatted_step = f"  步骤{step['step_number']}: {step['dimension']}\n    - {step['analysis_focus']}\n    - {step['reasoning_process']}"
            formatted_steps.append(formatted_step)
        return '\n'.join(formatted_steps)

    def _format_list(self, items: list) -> str:
        """格式化列表"""
        return '\n'.join([f"  • {item}" for item in items])
