"""
金融数据工具
提供股票实时行情、历史数据、基础数据查询功能
"""

import re
from typing import Dict, Any
from python.helpers.tool import Tool, Response
from python.helpers.financial_api_client import FinancialAPIClient


class FinancialDataTool(Tool):
    """金融数据查询工具"""
    
    def __init__(self, agent, name="financial_data_tool", method=None, args=None, message="", **kwargs):
        # 明确指定Tool基类的构造参数
        super().__init__(agent, name, method, args or {}, message, **kwargs)
        self.api_client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化API客户端"""
        try:
            self.api_client = FinancialAPIClient()
            # 后台日志（不显示在用户界面）
            # self.agent.context.log.log("info", "金融数据API客户端", "初始化成功")
        except Exception as e:
            # 正确的错误日志记录
            self.agent.context.log.log("error", "金融数据API客户端", f"初始化失败: {e}")
            self.api_client = None
    
    def _extract_stock_codes(self, text: str) -> str:
        """从文本中提取股票代码"""
        # 匹配6位数字+.SZ/SH格式 (支持大小写，不使用单词边界)
        pattern = r'\d{6}\.(SZ|SH|sz|sh)'
        matches = re.findall(pattern, text, re.IGNORECASE)
        
        if matches:
            codes = []
            for match in re.finditer(pattern, text, re.IGNORECASE):
                full_code = match.group(0).upper()  # 转为大写
                codes.append(full_code)
            return ','.join(codes)
        
        # 股票名称映射（包含常用别名）
        stock_mapping = {
            '贵州茅台': '600519.SH',
            '茅台': '600519.SH',
            '比亚迪': '002594.SZ',
            '平安银行': '000001.SZ',
            '平安': '000001.SZ',
            '五粮液': '000858.SZ',
            '招商银行': '600036.SH',
            '招行': '600036.SH',
            '中国平安': '601318.SH',
            '万科A': '000002.SZ',
            '万科': '000002.SZ',
            '美的集团': '000333.SZ',
            '美的': '000333.SZ',
            '格力电器': '000651.SZ',
            '格力': '000651.SZ',
            '腾讯控股': '00700.HK',
            '腾讯': '00700.HK',
            '山西汾酒': '600809.SH',
            '汾酒': '600809.SH',
            '中芯国际': '688981.SH',
            '宁德时代': '300750.SZ',
            '海康威视': '002415.SZ'
        }
        
        for name, code in stock_mapping.items():
            if name in text:
                return code
        
        return ""
    
    def _detect_query_type(self, query: str) -> str:
        """检测查询类型"""
        query_lower = query.lower()
        query_upper = query.upper()

        # 技术指标查询
        technical_keywords = [
            'MACD', 'RSI', 'KDJ', 'MA', 'BOLL', 'OBV', 'ATR', 'CCI', 'WR', 'ROC',
            'VR', 'VRSI', 'VMACD', 'VMA', 'VOSC', 'VSTD', 'VWAP',
            '技术指标', '技术分析', '买卖信号', '超买', '超卖', '金叉', '死叉',
            '背离', '趋势', '震荡', '移动平均', '均线', '相对强弱', '随机指标', '布林线',
            '成交量指标', '能量潮', '量比', '量相对强弱', '成交量加权平均价'
        ]
        if any(keyword in query_upper for keyword in technical_keywords):
            return "technical_indicators"

        # 财务报表查询
        if any(word in query_lower for word in ['财报', '财务报表', '利润表', '资产负债表', '现金流量表', '季报', '年报']):
            return "financial_report"
        elif any(word in query_lower for word in ['历史', '过去', 'history']):
            return "history"
        elif any(word in query_lower for word in ['基础', '财务', 'basic']):
            return "basic"
        else:
            return "real_time"
    
    def _parse_financial_report_query(self, query: str) -> tuple:
        """解析财务报表查询"""
        # 解析报表类型
        report_type = "利润表"  # 默认
        if "资产负债表" in query:
            report_type = "资产负债表"
        elif "现金流量表" in query:
            report_type = "现金流量表"
        
        # 解析期间 - 优化正则表达式，避免误匹配股票代码
        year_match = re.search(r'(20[0-9]{2})年?', query)
        if not year_match:
            year_match = re.search(r'(?<![0-9])(20[0-9]{2})(?![0-9])', query)
        
        quarter_match = re.search(r'([1-4])季', query)
        
        if year_match:
            year = year_match.group(1)
            if quarter_match:
                quarter = quarter_match.group(1)
                period = f"{year}Q{quarter}"
            else:
                period = f"{year}年报"
        else:
            period = "最新"
        
        return report_type, period
    
    def _format_single_stock_data(self, code: str, data: Dict[str, Any]) -> str:
        """格式化单个股票数据"""
        # 提取数据，处理可能的列表或单值
        def get_value(key, index=0):
            value = data.get(key)
            if isinstance(value, list) and len(value) > index:
                return value[index]
            elif not isinstance(value, list):
                return value
            return None
        
        # 基础数据
        latest = get_value('latest')
        pre_close = get_value('preClose')
        open_price = get_value('open')
        high = get_value('high')
        low = get_value('low')
        volume = get_value('volume')
        amount = get_value('amount')
        
        # 计算涨跌幅
        change_amount = 0
        change_pct = 0
        if latest and pre_close and pre_close != 0:
            change_amount = latest - pre_close
            change_pct = (change_amount / pre_close) * 100
        
        # 格式化输出
        formatted = f"**{code}**\n"
        formatted += f"- 最新价: {latest or 'N/A'}"
        if change_amount != 0:
            change_sign = "+" if change_amount > 0 else ""
            formatted += f" ({change_sign}{change_amount:.2f}, {change_sign}{change_pct:.2f}%)"
        formatted += "\n"
        
        if open_price:
            formatted += f"- 开盘价: {open_price}\n"
        if high:
            formatted += f"- 最高价: {high}\n"
        if low:
            formatted += f"- 最低价: {low}\n"
        if volume:
            formatted += f"- 成交量: {volume:,.0f}股\n"
        if amount:
            formatted += f"- 成交额: {amount:,.0f}元\n"
        
        # 技术指标
        pe_ttm = get_value('pe_ttm')
        pb = get_value('pb')
        turnover_ratio = get_value('turnoverRatio')

        if pe_ttm:
            formatted += f"- 市盈率(TTM): {pe_ttm:.2f}\n"
        if pb:
            formatted += f"- 市净率: {pb:.2f}\n"
        if turnover_ratio:
            formatted += f"- 换手率: {turnover_ratio:.2f}%\n"

        # 市值信息（根据手册新增）
        total_shares = get_value('totalShares')
        total_capital = get_value('totalCapital')

        if total_shares:
            formatted += f"- 总股本: {total_shares:,.0f}股\n"
        if total_capital:
            formatted += f"- 总市值: {total_capital:,.0f}元\n"

        # 交易时间信息（根据手册新增）
        trade_date = get_value('tradeDate')
        trade_time = get_value('tradeTime')

        if trade_date:
            formatted += f"- 交易日期: {trade_date}\n"
        if trade_time:
            formatted += f"- 交易时间: {trade_time}\n"
        
        return formatted
    
    def _format_real_time_result(self, result: Dict[str, Any], codes: str) -> str:
        """格式化实时行情结果"""
        if result.get('errorcode') != 0:
            return f"❌ 数据获取失败: {result.get('reason', '未知错误')}"

        tables = result.get('data', {}).get('tables', [])
        if not tables:
            # 在非交易时间，实时数据可能为空，提供更有用的提示
            from datetime import datetime, time
            now = datetime.now()
            current_time = now.time()

            # 中国股市交易时间
            morning_start = time(9, 30)
            morning_end = time(11, 30)
            afternoon_start = time(13, 0)
            afternoon_end = time(15, 0)

            is_trading_hours = (
                (morning_start <= current_time <= morning_end) or
                (afternoon_start <= current_time <= afternoon_end)
            ) and now.weekday() < 5

            if not is_trading_hours:
                return f"📊 **非交易时间提示**\n\n当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}\n\n实时行情数据在非交易时间不可用。\n\n💡 **建议**：\n- 查询历史数据获取最近交易日的收盘价\n- 使用技术指标分析工具\n- 等待交易时间（周一至周五 9:30-11:30, 13:00-15:00）"
            else:
                return f"❌ **数据获取异常**\n\n可能原因：\n1. 股票代码 {codes} 错误或股票暂停交易\n2. 网络连接问题\n3. API服务临时不可用\n\n💡 **建议**：\n- 检查股票代码格式（如：000858.SZ）\n- 尝试查询历史数据\n- 稍后重试"
        
        table_data = tables[0]['table']
        code_list = codes.split(',')
        
        formatted_result = "📊 **实时行情数据**\n\n"
        
        # 判断数据结构类型
        if isinstance(table_data, dict):
            # 单个股票的情况
            data = table_data
            code = code_list[0].strip()
            formatted_result += self._format_single_stock_data(code, data)
        elif isinstance(table_data, list):
            # 多个股票的情况
            for i, code in enumerate(code_list):
                if i < len(table_data):
                    data = table_data[i]
                    formatted_result += self._format_single_stock_data(code.strip(), data)
                    if i < len(code_list) - 1:
                        formatted_result += "\n"
        
        return formatted_result
    
    def _format_financial_report_result(self, result: Dict[str, Any], codes: str, 
                                      report_type: str, period: str) -> str:
        """格式化财务报表结果"""
        if result.get('errorcode') != 0:
            return f"❌ 财务数据获取失败: {result.get('reason', '未知错误')}"
        
        tables = result.get('data', {}).get('tables', [])
        if not tables:
            return "❌ 未获取到财务数据"
        
        table_data = tables[0]['table']
        
        # 实时行情API返回的财务指标映射
        indicator_names = {
            'latest': '最新股价',
            'pe_ttm': '市盈率(TTM)',
            'pb': '市净率',
            'totalShares': '总股本',
            'totalCapital': '总市值',
            'mv': '流通市值',
            'roe': '净资产收益率',
            'turnoverRatio': '换手率'
        }
        
        formatted_result = f"📊 **{period} 财务报表数据**\n\n"
        formatted_result += f"**{codes}**\n"
        
        # 处理数据格式
        if isinstance(table_data, dict):
            data = table_data
        elif isinstance(table_data, list) and len(table_data) > 0:
            data = table_data[0]
        else:
            return "❌ 财务数据格式异常"
        
        # 格式化财务指标
        for key, name in indicator_names.items():
            value = data.get(key)
            if value is not None:
                if isinstance(value, list) and len(value) > 0:
                    value = value[0]
                
                if key in ['totalShares', 'totalCapital', 'mv']:
                    # 股本和市值类指标，转换为亿
                    if value > 100000000:  # 大于1亿
                        formatted_result += f"- **{name}**: {value/100000000:.2f}亿元\n"
                    else:
                        formatted_result += f"- **{name}**: {value:,.0f}元\n"
                elif key in ['pe_ttm', 'pb']:
                    # 比率类指标
                    formatted_result += f"- **{name}**: {value:.2f}倍\n"
                elif key in ['roe', 'turnoverRatio']:
                    # 百分比类指标
                    formatted_result += f"- **{name}**: {value:.2f}%\n"
                else:
                    # 其他指标
                    formatted_result += f"- **{name}**: {value:.2f}元\n"
        
        formatted_result += f"\n*数据来源: 同花顺iFinD实时行情API*"
        return formatted_result

    async def _handle_vwap_query(self, codes: str, kwargs: Dict[str, Any]) -> Response:
        """处理VWAP（成交量加权平均价）查询"""
        try:
            # 检查是否需要历史VWAP数据
            query_text = kwargs.get('query', '').lower()

            if any(word in query_text for word in ['历史', '过去', '趋势', '走势']):
                # 获取历史VWAP数据
                from datetime import datetime, timedelta

                # 默认获取最近30天的数据
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)

                # 如果用户指定了时间范围，尝试解析
                start_date_str = kwargs.get('start_date', start_date.strftime('%Y-%m-%d'))
                end_date_str = kwargs.get('end_date', end_date.strftime('%Y-%m-%d'))

                result = await self.api_client.get_history_quotation(
                    codes=codes,
                    startdate=start_date_str,
                    enddate=end_date_str,
                    indicators="open,high,low,close,volume,amount,avgPrice"
                )

                return Response(message=self._format_vwap_result(result, codes, "历史"), break_loop=False)

            else:
                # 获取实时VWAP数据
                result = await self.api_client.get_real_time_quotation(
                    codes=codes,
                    indicators="latest,avgPrice,volume,amount,preClose"
                )

                return Response(message=self._format_vwap_result(result, codes, "实时"), break_loop=False)

        except Exception as e:
            return Response(message=f"❌ VWAP数据获取失败: {str(e)}", break_loop=False)

    def _format_vwap_result(self, result: Dict[str, Any], codes: str, data_type: str) -> str:
        """格式化VWAP结果"""
        if result.get('errorcode') != 0:
            return f"❌ VWAP数据获取失败: {result.get('reason', '未知错误')}"

        tables = result.get('data', {}).get('tables', [])
        if not tables:
            return "❌ 未获取到VWAP数据"

        table_data = tables[0]['table']
        code_list = codes.split(',')

        formatted_result = f"📊 **{data_type}VWAP数据（成交量加权平均价）**\n\n"

        if data_type == "实时":
            # 实时数据格式化
            if isinstance(table_data, dict):
                data = table_data
                code = code_list[0].strip()
                formatted_result += self._format_single_vwap_realtime(code, data)
            elif isinstance(table_data, list):
                for i, code in enumerate(code_list):
                    if i < len(table_data):
                        data = table_data[i]
                        formatted_result += self._format_single_vwap_realtime(code.strip(), data)
                        if i < len(code_list) - 1:
                            formatted_result += "\n"
        else:
            # 历史数据格式化
            if isinstance(table_data, list) and len(table_data) > 0:
                formatted_result += f"**{code_list[0].strip()}**\n"
                formatted_result += f"📈 **VWAP历史走势** (最近{len(table_data)}个交易日)\n\n"

                # 显示最近几天的数据
                recent_data = table_data[-5:] if len(table_data) > 5 else table_data
                for record in recent_data:
                    date = record.get('time', '未知日期')
                    vwap = record.get('avgPrice', 0)
                    volume = record.get('volume', 0)
                    amount = record.get('amount', 0)
                    close = record.get('close', 0)

                    formatted_result += f"📅 **{date}**\n"
                    formatted_result += f"   - VWAP: {vwap:.2f}元\n"
                    formatted_result += f"   - 收盘价: {close:.2f}元\n"
                    formatted_result += f"   - 成交量: {volume:,.0f}股\n"
                    formatted_result += f"   - 成交额: {amount:,.0f}元\n\n"

        formatted_result += f"*💡 VWAP说明: 成交量加权平均价，反映当日平均成交价格水平*\n"
        formatted_result += f"*数据来源: 同花顺iFinD API*"
        return formatted_result

    def _format_single_vwap_realtime(self, code: str, data: Dict[str, Any]) -> str:
        """格式化单个股票的实时VWAP数据"""
        result = f"**{code}**\n"

        latest = data.get('latest', [0])[0] if isinstance(data.get('latest'), list) else data.get('latest', 0)
        vwap = data.get('avgPrice', [0])[0] if isinstance(data.get('avgPrice'), list) else data.get('avgPrice', 0)
        volume = data.get('volume', [0])[0] if isinstance(data.get('volume'), list) else data.get('volume', 0)
        amount = data.get('amount', [0])[0] if isinstance(data.get('amount'), list) else data.get('amount', 0)
        preClose = data.get('preClose', [0])[0] if isinstance(data.get('preClose'), list) else data.get('preClose', 0)

        # 计算VWAP与当前价格的偏离
        vwap_deviation = ((latest - vwap) / vwap * 100) if vwap != 0 else 0

        result += f"📊 **当前价格**: {latest:.2f}元\n"
        result += f"📊 **VWAP**: {vwap:.2f}元\n"
        result += f"📊 **偏离度**: {vwap_deviation:+.2f}%\n"
        result += f"📊 **成交量**: {volume:,.0f}股\n"
        result += f"📊 **成交额**: {amount:,.0f}元\n"

        # 分析VWAP信号
        if latest > vwap:
            result += f"📈 **信号**: 当前价格高于VWAP，可能处于强势\n"
        elif latest < vwap:
            result += f"📉 **信号**: 当前价格低于VWAP，可能处于弱势\n"
        else:
            result += f"➡️ **信号**: 当前价格接近VWAP，处于均衡状态\n"

        return result

    def _format_fallback_real_time_result(self, result: Dict[str, Any], codes: str) -> str:
        """格式化备用实时行情结果（使用历史数据）"""

        tables = result.get('data', {}).get('tables', [])
        if not tables:
            return "❌ 无法获取数据"

        formatted_result = f"📊 **最新行情数据** (非交易时间，显示最近交易日数据)\n\n"

        code_list = codes.split(',')

        for i, table in enumerate(tables):
            if i < len(code_list):
                stock_code = code_list[i].strip()
                formatted_result += f"**{stock_code}**\n"

                table_data = table.get('table', {})
                times = table.get('time', [])

                if table_data and times:
                    # 获取最新的数据（最后一个交易日）
                    latest_time = times[-1]
                    formatted_result += f"- 最新交易日: {latest_time}\n"

                    # 显示主要价格信息
                    for field, values in table_data.items():
                        if values and len(values) > 0:
                            latest_value = values[-1]

                            if field == 'close':
                                formatted_result += f"- 收盘价: {latest_value:.2f}元\n"
                            elif field == 'preClose':
                                formatted_result += f"- 前收盘: {latest_value:.2f}元\n"
                            elif field == 'open':
                                formatted_result += f"- 开盘价: {latest_value:.2f}元\n"
                            elif field == 'high':
                                formatted_result += f"- 最高价: {latest_value:.2f}元\n"
                            elif field == 'low':
                                formatted_result += f"- 最低价: {latest_value:.2f}元\n"
                            elif field == 'volume':
                                formatted_result += f"- 成交量: {latest_value:,.0f}股\n"

                    # 计算涨跌幅
                    close_prices = table_data.get('close', [])
                    preclose_prices = table_data.get('preClose', [])

                    if close_prices and preclose_prices and len(close_prices) > 0 and len(preclose_prices) > 0:
                        close_price = close_prices[-1]
                        preclose_price = preclose_prices[-1]
                        change = close_price - preclose_price
                        change_ratio = (change / preclose_price) * 100

                        change_symbol = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                        formatted_result += f"- 涨跌额: {change_symbol} {change:.2f}元\n"
                        formatted_result += f"- 涨跌幅: {change_symbol} {change_ratio:.2f}%\n"

                if i < len(code_list) - 1:
                    formatted_result += "\n"

        formatted_result += f"\n💡 **提示**: 当前为非交易时间，显示最近交易日的收盘数据"
        formatted_result += f"\n*数据来源: 同花顺iFinD历史行情API*"
        return formatted_result

    def _format_history_result(self, result: Dict[str, Any], codes: str, startdate: str, enddate: str) -> str:
        """格式化历史行情结果"""
        if result.get('errorcode') != 0:
            return f"❌ 历史数据获取失败: {result.get('reason', '未知错误')}"

        formatted_result = f"📈 **历史行情数据**\n\n"
        formatted_result += f"**查询期间**: {startdate} 至 {enddate}\n"
        formatted_result += f"**股票代码**: {codes}\n\n"

        tables = result.get('data', {}).get('tables', [])
        if tables:
            for table in tables:
                table_data = table.get('table', {})
                if table_data:
                    formatted_result += "| 日期 | 开盘价 | 最高价 | 最低价 | 收盘价 | 成交量 |\n"
                    formatted_result += "|------|--------|--------|--------|--------|--------|\n"

                    # 显示最近10条记录
                    count = 0
                    for date, data in table_data.items():
                        if count >= 10:
                            break
                        if isinstance(data, list) and len(data) >= 5:
                            formatted_result += f"| {date} | {data[0]:.2f} | {data[1]:.2f} | {data[2]:.2f} | {data[3]:.2f} | {data[4]} |\n"
                            count += 1
        else:
            # 处理没有数据的情况
            formatted_result += f"❌ **未获取到历史数据**\n\n可能原因：\n1. 查询日期范围内无交易数据\n2. 股票代码 {codes} 错误\n3. 查询的日期都是非交易日\n\n💡 **建议**：\n- 扩大查询日期范围\n- 检查股票代码格式\n- 选择包含交易日的日期范围"

        formatted_result += f"\n*数据来源: 同花顺iFinD历史行情API*"
        return formatted_result

    def _format_basic_result(self, result: Dict[str, Any], codes: str) -> str:
        """格式化基础数据结果"""
        if result.get('errorcode') != 0:
            return f"❌ 基础数据获取失败: {result.get('reason', '未知错误')}"

        formatted_result = f"📋 **基础数据**\n\n"
        formatted_result += f"**股票代码**: {codes}\n\n"

        tables = result.get('data', {}).get('tables', [])
        if tables:
            for table in tables:
                table_data = table.get('table', {})
                if table_data:
                    for code, data in table_data.items():
                        if isinstance(data, list):
                            formatted_result += f"**{code}**:\n"
                            for item in data:
                                if isinstance(item, dict):
                                    for key, value in item.items():
                                        formatted_result += f"- {key}: {value}\n"
                            formatted_result += "\n"

        formatted_result += f"\n*数据来源: 同花顺iFinD基础数据API*"
        return formatted_result

    async def execute(self, query_type="auto", codes="", indicators="", **kwargs):
        """执行金融数据查询"""
        if not self.api_client:
            return Response(message="❌ 金融数据API客户端未初始化", break_loop=False)
        
        try:
            # 获取查询文本
            query_text = kwargs.get('query', '')
            
            # 自动检测查询类型（如果只提供了query参数）
            if query_type == "auto" or (query_text and not query_type):
                query_type = self._detect_query_type(query_text)

            # 特殊处理VWAP查询
            if 'VWAP' in query_text.upper() or '成交量加权平均价' in query_text:
                query_type = "vwap"
            
            # 提取股票代码
            if not codes and query_text:
                codes = self._extract_stock_codes(query_text)
            
            if not codes:
                return Response(message="❌ 未识别到有效的股票代码，请提供股票代码（如600519.SH）或股票名称", break_loop=False)
            
            # 财务报表查询优先处理
            if query_type == "financial_report":
                report_type, period = self._parse_financial_report_query(kwargs.get('query', ''))
                result = await self.api_client.get_financial_report(codes, report_type, period)
                formatted_result = self._format_financial_report_result(result, codes, report_type, period)
                return Response(message=formatted_result, break_loop=False)

            # VWAP查询（成交量加权平均价）
            elif query_type == "vwap":
                return await self._handle_vwap_query(codes, kwargs)

            # 技术指标查询
            elif query_type == "technical_indicators":
                # 调用技术指标工具
                from python.tools.technical_indicators_tool import TechnicalIndicatorsTool
                tech_tool = TechnicalIndicatorsTool(self.agent)

                # 如果有自然语言查询，优先使用query参数
                if 'query' in kwargs and kwargs['query']:
                    return await tech_tool.execute(query=kwargs['query'])
                else:
                    # 否则使用提取的参数
                    return await tech_tool.execute(codes=codes, indicators=indicators, **kwargs)

            # 其他查询类型
            elif query_type == "real_time":
                result = await self.api_client.get_real_time_quotation(codes, indicators)

                # 检查实时数据是否可用
                tables = result.get('data', {}).get('tables', []) if result.get('errorcode') == 0 else []

                if not tables and result.get('errorcode') == 0:
                    # 实时数据不可用，尝试获取最近的历史数据
                    from datetime import datetime, timedelta

                    # 获取最近5个交易日的数据作为替代
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=7)  # 往前推7天确保包含交易日

                    history_result = await self.api_client.get_history_quotation(
                        codes=codes,
                        startdate=start_date.strftime('%Y-%m-%d'),
                        enddate=end_date.strftime('%Y-%m-%d'),
                        indicators=indicators or "preClose,open,high,low,close,volume"
                    )

                    if history_result.get('errorcode') == 0:
                        history_tables = history_result.get('data', {}).get('tables', [])
                        if history_tables:
                            # 格式化为类似实时数据的展示
                            formatted_result = self._format_fallback_real_time_result(history_result, codes)
                            return Response(message=formatted_result, break_loop=False)

                # 使用原有的格式化方法
                formatted_result = self._format_real_time_result(result, codes)
                return Response(message=formatted_result, break_loop=False)

            elif query_type == "history":
                startdate = kwargs.get('startdate', '2024-01-01')
                enddate = kwargs.get('enddate', '2024-12-31')
                result = await self.api_client.get_history_quotation(codes, startdate, enddate, indicators)
                formatted_result = self._format_history_result(result, codes, startdate, enddate)
                return Response(message=formatted_result, break_loop=False)

            elif query_type == "basic":
                result = await self.api_client.get_basic_data(codes, indicators)
                formatted_result = self._format_basic_result(result, codes)
                return Response(message=formatted_result, break_loop=False)

            else:
                return Response(message="❌ 不支持的查询类型", break_loop=False)

        except Exception as e:
            # 正确的错误日志记录
            self.agent.context.log.log("error", "金融数据查询", str(e))
            return Response(message=f"❌ 查询失败: {str(e)}", break_loop=False)
