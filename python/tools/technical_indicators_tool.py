"""
技术指标分析工具
提供股票技术指标的获取、分析和信号计算功能
支持MACD、RSI、KDJ等多种技术指标
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Any, List
from python.helpers.tool import Tool, Response
from python.helpers.financial_api_client import FinancialAPIClient


class TechnicalIndicatorsTool(Tool):
    """技术指标分析工具
    
    提供股票技术指标的获取、分析和信号计算功能
    支持50+种技术指标，包括MACD、RSI、KDJ等
    """
    
    def __init__(self, agent, name="technical_indicators_tool", method=None, args=None, message="", **kwargs):
        # 明确指定Tool基类的构造参数
        super().__init__(agent, name, method, args or {}, message, **kwargs)
        self.api_client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化API客户端"""
        try:
            self.api_client = FinancialAPIClient()
            # 后台日志（不显示在用户界面）
            # self.agent.context.log.log("info", "技术指标API客户端", "初始化成功")
        except Exception as e:
            # 正确的错误日志记录
            self.agent.context.log.log("error", "技术指标API客户端", f"初始化失败: {e}")
            self.api_client = None

    def get_description(self):
        return """技术指标分析工具，支持获取和分析股票技术指标。
        
支持的技术指标：
- 趋势类：MACD、MA(支持任意周期，如5/10/20/25/30/60/120/250日)、EXPMA、DMA、TRIX
- 震荡类：RSI、KDJ、CCI、WR、ROC
- 成交量：OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD
- 支撑阻力：BOLL、CDP、MIKE
- 波动率：ATR、STD、BIAS

MA均线特殊功能：
- 支持任意周期：1-500日任意周期均线
- 自然语言识别：如"25日均线"、"55日移动平均"
- 常用周期：MA5、MA10、MA20、MA30、MA60、MA120、MA250

使用方法：
- codes: 股票代码，如 "000858.SZ"
- indicators: 技术指标，如 "MACD,RSI,KDJ"
- period: 分析周期，如 "1M"（1个月）
- query: 自然语言查询，如 "分析五粮液的MACD指标"
"""
    
    def _validate_parameters(self, codes: str, indicators: str, period: str) -> tuple[bool, str]:
        """验证输入参数
        
        Args:
            codes: 股票代码
            indicators: 技术指标
            period: 时间周期
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        
        # 验证股票代码
        if not codes:
            return False, "❌ 请提供股票代码，如：000858.SZ"
        
        # 验证股票代码格式
        code_pattern = r'^\d{6}\.(SZ|SH)$'
        for code in codes.split(','):
            if not re.match(code_pattern, code.strip()):
                return False, f"❌ 股票代码格式错误: {code.strip()}，正确格式如：000858.SZ"
        
        # 验证技术指标
        if not indicators:
            return False, "❌ 请指定要分析的技术指标，如：MACD,RSI,KDJ"
        
        supported_indicators = [
            'MACD', 'RSI', 'KDJ', 'MA', 'MA5', 'MA10', 'MA20', 'MA30', 'MA60', 'MA120', 'MA250',
            'EXPMA', 'DMA', 'TRIX', 'CCI', 'WR', 'ROC', 'OBV', 'VR', 'VRSI', 'VMACD', 'VMA', 'VOSC', 'VSTD',
            'BOLL', 'CDP', 'MIKE', 'ATR', 'STD', 'BIAS'
        ]
        
        for indicator in indicators.split(','):
            indicator = indicator.strip().upper()
            if indicator not in supported_indicators:
                return False, f"❌ 不支持的技术指标: {indicator}，支持的指标：{', '.join(supported_indicators[:10])}等"
        
        # 验证时间周期
        supported_periods = ['1W', '1M', '3M', '6M', '1Y']
        if period not in supported_periods:
            return False, f"❌ 不支持的时间周期: {period}，支持的周期：{', '.join(supported_periods)}"
        
        return True, ""
    
    def _get_period_dates(self, period: str) -> tuple[str, str]:
        """根据周期获取开始和结束日期
        
        Args:
            period: 时间周期
            
        Returns:
            tuple[str, str]: (开始日期, 结束日期)
        """
        
        end_date = datetime.now()
        
        if period == '1W':
            start_date = end_date - timedelta(days=7)
        elif period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)  # 默认1个月
        
        return (
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )

    def _extract_ma_period(self, query: str) -> str:
        """从查询中提取MA周期"""
        import re

        # 匹配各种MA周期表达方式
        patterns = [
            r'MA(\d+)',           # MA25
            r'(\d+)日均线',        # 25日均线
            r'(\d+)日移动平均',     # 25日移动平均
            r'(\d+)日MA',         # 25日MA
            r'(\d+)天均线',        # 25天均线
        ]

        for pattern in patterns:
            match = re.search(pattern, query)
            if match:
                period = match.group(1)
                if period.isdigit() and 1 <= int(period) <= 500:  # 合理的周期范围
                    return period

        return None

    def _parse_natural_query(self, query: str) -> Dict[str, str]:
        """解析自然语言查询
        
        支持中文自然语言查询技术指标
        
        Args:
            query: 自然语言查询字符串
            
        Returns:
            Dict[str, str]: 解析后的参数
        """
        
        result = {
            "codes": "",
            "indicators": "",
            "period": "1M",
            "analysis_type": "basic"
        }
        
        # 1. 股票名称/代码识别
        stock_patterns = {
            r'五粮液|000858': '000858.SZ',
            r'茅台|贵州茅台|600519': '600519.SH',
            r'平安|平安银行|000001': '000001.SZ',
            r'招行|招商银行|600036': '600036.SH',
            r'比亚迪|002594': '002594.SZ',
            r'万科|万科A|000002': '000002.SZ',
            r'美的|美的集团|000333': '000333.SZ',
            r'格力|格力电器|000651': '000651.SZ',
            r'汾酒|山西汾酒|600809': '600809.SH',
            r'中芯国际|688981': '688981.SH',
            r'宁德时代|300750': '300750.SZ',
            r'海康威视|002415': '002415.SZ'
        }
        
        for pattern, code in stock_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                result["codes"] = code
                break
        
        # 2. 技术指标识别
        indicator_patterns = {
            r'MACD|macd|指数平滑|异同平均': 'MACD',
            r'RSI|rsi|相对强弱': 'RSI',
            r'KDJ|kdj|随机指标': 'KDJ',
            r'布林线|BOLL|boll': 'BOLL',
            r'成交量|OBV|obv|能量潮': 'OBV',
            r'量比|VR|vr': 'VR',
            r'量相对强弱|VRSI|vrsi': 'VRSI',
            r'量MACD|VMACD|vmacd': 'VMACD',
            r'量移动平均|VMA|vma': 'VMA',
            r'成交量震荡|VOSC|vosc': 'VOSC',
            r'成交量标准差|VSTD|vstd': 'VSTD'
        }

        indicators = []

        # 首先检查是否有特定周期的MA查询
        ma_period = self._extract_ma_period(query)
        if ma_period:
            # 如果找到特定周期，使用MA指标并记录周期
            indicators.append('MA')
            result["ma_period"] = ma_period
        elif re.search(r'移动平均|MA|ma|均线', query, re.IGNORECASE):
            # 如果是一般的MA查询，使用默认MA
            indicators.append('MA')

        # 检查其他指标
        for pattern, indicator in indicator_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                indicators.append(indicator)

        result["indicators"] = ",".join(indicators) if indicators else "MACD,RSI,KDJ"
        
        # 3. 时间周期识别
        period_patterns = {
            r'一周|1周|7天': '1W',
            r'一个月|1月|30天|最近一个月': '1M',
            r'三个月|3月|90天|一季度': '3M',
            r'半年|6月|180天': '6M',
            r'一年|1年|12月|365天': '1Y'
        }
        
        for pattern, period in period_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                result["period"] = period
                break
        
        # 4. 分析类型识别
        if re.search(r'信号|买卖|交易', query):
            result["analysis_type"] = "signals"
        elif re.search(r'详细|深入|全面', query):
            result["analysis_type"] = "advanced"
        
        return result
    
    def _extract_stock_codes(self, query: str) -> str:
        """从查询文本中提取股票代码"""
        # 匹配标准股票代码格式 (不使用单词边界，避免中文环境问题)
        code_pattern = r'\d{6}\.(SZ|SH|sz|sh)'
        matches = re.finditer(code_pattern, query, re.IGNORECASE)

        codes = []
        for match in matches:
            full_code = match.group(0).upper()  # 转为大写
            codes.append(full_code)

        if codes:
            return ','.join(codes)

        # 如果没有找到标准格式，尝试从自然语言解析
        parsed = self._parse_natural_query(query)
        return parsed.get("codes", "")

    async def _get_multiple_ma_indicators(self, codes: str, starttime: str, endtime: str,
                                        ma_periods: List[str]) -> Dict[str, Any]:
        """获取多个MA周期的指标数据"""
        all_ma_data = {}

        for period in ma_periods:
            try:
                result = await self.api_client.get_technical_indicators(
                    codes=codes,
                    starttime=starttime,
                    endtime=endtime,
                    indicators='MA',
                    calculate={'MA': period}
                )

                if result.get('errorcode') == 0:
                    tables = result.get('data', {}).get('tables', [])
                    if tables:
                        table_data = tables[0].get('table', {})
                        if isinstance(table_data, dict) and 'MA' in table_data:
                            # 将MA数据重命名为MA{period}
                            ma_values = table_data['MA']
                            all_ma_data[f'MA{period}'] = ma_values

                            # 保留其他基础数据（只在第一次获取时）
                            if not any(key in all_ma_data for key in ['open', 'high', 'low', 'close', 'volume']):
                                for key, value in table_data.items():
                                    if key != 'MA':
                                        all_ma_data[key] = value

            except Exception as e:
                print(f"获取MA{period}失败: {str(e)}")
                continue

        return all_ma_data

    def _parse_ma_indicators(self, indicators: str) -> List[str]:
        """解析MA指标，提取所有MA周期"""
        ma_periods = []

        for indicator in indicators.split(','):
            indicator = indicator.strip().upper()

            if indicator.startswith('MA') and len(indicator) > 2:
                # 提取周期数字，如MA5 -> 5, MA10 -> 10
                period_str = indicator[2:]
                if period_str.isdigit():
                    ma_periods.append(period_str)
            elif indicator == 'MA':
                # 默认MA使用20日
                ma_periods.append('20')

        return ma_periods

    async def execute(self, codes: str = "", indicators: str = "",
                     period: str = "1M", analysis_type: str = "basic", **kwargs):
        """执行技术指标分析
        
        Args:
            codes: 股票代码
            indicators: 技术指标列表
            period: 分析周期 (1W/1M/3M/6M/1Y)
            analysis_type: 分析类型 (basic/advanced/signals)
        """
        
        try:
            # 处理自然语言查询
            query_text = kwargs.get('query', '')
            ma_period = None
            if query_text and not codes:
                parsed = self._parse_natural_query(query_text)
                codes = codes or parsed.get("codes", "")
                indicators = indicators or parsed.get("indicators", "")
                period = period if period != "1M" else parsed.get("period", "1M")
                analysis_type = analysis_type if analysis_type != "basic" else parsed.get("analysis_type", "basic")
                ma_period = parsed.get("ma_period")  # 获取自定义MA周期
            
            # 如果仍然没有代码，尝试从查询文本提取
            if not codes and query_text:
                codes = self._extract_stock_codes(query_text)
            
            # 设置默认指标
            if not indicators:
                indicators = "MACD,RSI,KDJ"
            
            # 验证参数
            is_valid, error_msg = self._validate_parameters(codes, indicators, period)
            if not is_valid:
                return Response(message=error_msg, break_loop=False)
            
            # 获取时间范围
            starttime, endtime = self._get_period_dates(period)
            
            # 调用API获取技术指标数据
            # 检查是否有多个MA指标
            ma_periods = self._parse_ma_indicators(indicators)

            if len(ma_periods) > 1:
                # 有多个MA周期，需要分批获取
                print(f"检测到多个MA周期: {ma_periods}，使用分批获取")

                # 获取非MA指标
                non_ma_indicators = ','.join([ind for ind in indicators.split(',')
                                           if not (ind.startswith('MA') and len(ind) > 2)])

                # 先获取非MA指标
                if non_ma_indicators:
                    result = await self.api_client.get_technical_indicators(
                        codes=codes,
                        starttime=starttime,
                        endtime=endtime,
                        indicators=non_ma_indicators
                    )
                else:
                    # 创建一个空结果
                    result = {'errorcode': 0, 'reason': 'success', 'data': {'tables': [{'table': {}}]}}

                # 然后获取多个MA指标
                ma_data = await self._get_multiple_ma_indicators(codes, starttime, endtime, ma_periods)

                # 合并结果
                if result.get('errorcode') == 0 and ma_data:
                    tables = result.get('data', {}).get('tables', [])
                    if tables:
                        table_data = tables[0].get('table', {})
                        # 合并MA数据
                        for key, value in ma_data.items():
                            table_data[key] = value

            # 单个MA周期或无MA指标
            elif ma_period and 'MA' in indicators:
                result = await self.api_client.get_technical_indicators(
                    codes=codes,
                    starttime=starttime,
                    endtime=endtime,
                    indicators=indicators,
                    calculate={'MA': ma_period}
                )
            else:
                result = await self.api_client.get_technical_indicators(
                    codes=codes,
                    starttime=starttime,
                    endtime=endtime,
                    indicators=indicators
                )
            
            # 格式化结果
            formatted_result = self._format_technical_result(result, codes, indicators, period)
            
            return Response(message=formatted_result, break_loop=False)
            
        except Exception as e:
            # 错误日志记录
            self.agent.context.log.log("error", "技术指标分析", str(e))
            return Response(message=f"❌ 技术指标分析失败: {str(e)}", break_loop=False)

    def _format_technical_result(self, result: Dict[str, Any], codes: str,
                               indicators: str, period: str) -> str:
        """格式化技术指标分析结果

        将API返回的原始数据格式化为用户友好的分析报告

        Args:
            result: API返回的原始数据
            codes: 股票代码
            indicators: 技术指标列表
            period: 分析周期

        Returns:
            str: 格式化后的分析报告
        """

        if result.get('errorcode') != 0:
            error_code = result.get('errorcode')
            error_msg = self.api_client._get_error_message(error_code)
            return f"❌ 技术指标获取失败: {error_msg}"

        tables = result.get('data', {}).get('tables', [])
        if not tables:
            # 技术指标数据为空的智能处理
            from datetime import datetime as dt, time
            now = dt.now()
            current_time = now.time()

            # 检查是否为非交易时间
            morning_start = time(9, 30)
            morning_end = time(11, 30)
            afternoon_start = time(13, 0)
            afternoon_end = time(15, 0)

            is_trading_hours = (
                (morning_start <= current_time <= morning_end) or
                (afternoon_start <= current_time <= afternoon_end)
            ) and now.weekday() < 5

            if not is_trading_hours:
                return f"📊 **技术指标分析** - {codes}\n\n⏰ **非交易时间说明**\n\n当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}\n\n技术指标数据在非交易时间可能不可用，但这是正常现象。\n\n💡 **建议**：\n- 技术指标基于历史价格数据计算\n- 在交易时间内数据更新更频繁\n- 可以查询历史数据进行技术分析\n- 使用更长的时间周期（如1个月、3个月）可能获得更多数据\n\n🔄 **替代方案**：\n- 查询历史行情数据\n- 使用更大的时间范围\n- 等待下一个交易日"
            else:
                return f"❌ **技术指标数据获取异常**\n\n股票代码：{codes}\n指标：{indicators}\n时间周期：{period}\n\n可能原因：\n1. 股票代码错误或股票暂停交易\n2. 查询的时间范围内无足够数据\n3. 技术指标计算需要更多历史数据\n4. API服务临时不可用\n\n💡 **建议**：\n- 检查股票代码格式（如：000858.SZ）\n- 扩大查询时间范围\n- 尝试查询历史行情数据\n- 稍后重试"

        # 开始构建分析报告
        formatted_result = f"📈 **技术指标分析报告**\n\n"
        formatted_result += f"**股票代码**: {codes}\n"
        formatted_result += f"**分析指标**: {indicators}\n"
        formatted_result += f"**分析周期**: {period}\n"
        formatted_result += f"**数据点数**: {result.get('dataVol', 'N/A')}\n\n"

        # 处理每个股票的数据
        for table in tables:
            stock_code = table.get('thscode', 'Unknown')
            table_data = table.get('table', {})
            times = table.get('time', [])

            if not table_data or not times:
                continue

            formatted_result += f"## 📊 {stock_code} 技术指标分析\n\n"
            formatted_result += f"**最新数据时间**: {times[-1] if times else 'N/A'}\n\n"

            # 格式化各个技术指标
            # 首先处理原始指标列表中的指标
            for indicator in indicators.split(','):
                indicator = indicator.strip().upper()
                if indicator in table_data:
                    values = table_data[indicator]
                    if values and len(values) > 0:
                        formatted_result += self._format_single_indicator(
                            indicator, values, times
                        )

            # 然后处理多MA指标（MA5, MA10, MA20等）
            ma_indicators = [key for key in table_data.keys() if key.startswith('MA') and key[2:].isdigit()]
            if ma_indicators:
                # 按周期排序
                ma_indicators.sort(key=lambda x: int(x[2:]))

                formatted_result += "### 📊 移动平均线分析\n"
                for ma_indicator in ma_indicators:
                    values = table_data[ma_indicator]
                    if values and len(values) > 0:
                        period = ma_indicator[2:]  # 提取周期数字
                        latest_value = values[-1] if isinstance(values, list) else values
                        formatted_result += f"- **MA{period}({period}日均线)**: {latest_value:.4f}元\n"

                # 添加MA分析
                if len(ma_indicators) >= 2:
                    formatted_result += "\n**均线分析**:\n"
                    # 简单的多空排列分析
                    ma_values = []
                    for ma_indicator in ma_indicators:
                        values = table_data[ma_indicator]
                        latest_value = values[-1] if isinstance(values, list) else values
                        ma_values.append((int(ma_indicator[2:]), latest_value))

                    # 检查是否多头排列（短期均线在上）
                    is_bullish = all(ma_values[i][1] >= ma_values[i+1][1] for i in range(len(ma_values)-1))
                    is_bearish = all(ma_values[i][1] <= ma_values[i+1][1] for i in range(len(ma_values)-1))

                    if is_bullish:
                        formatted_result += "- **排列形态**: 多头排列，趋势向上\n"
                    elif is_bearish:
                        formatted_result += "- **排列形态**: 空头排列，趋势向下\n"
                    else:
                        formatted_result += "- **排列形态**: 均线交织，趋势不明\n"

                formatted_result += "\n"

            formatted_result += "\n"

        formatted_result += f"*数据来源: 同花顺iFinD高频序列API*\n"
        formatted_result += f"*分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"

        return formatted_result

    def _format_single_indicator(self, indicator: str, values: list, times: list) -> str:
        """格式化单个技术指标

        Args:
            indicator: 技术指标名称
            values: 指标数值列表
            times: 时间列表

        Returns:
            str: 格式化后的指标信息
        """

        if not values or len(values) == 0:
            return f"- **{indicator}**: 无数据\n"

        latest_value = values[-1]

        # 技术指标信息配置
        indicator_info = {
            'MACD': {
                'name': 'MACD指数平滑异同平均',
                'unit': '',
                'description': '趋势跟踪指标，用于判断买卖时机',
                'signal_logic': 'macd'
            },
            'RSI': {
                'name': 'RSI相对强弱指标',
                'unit': '',
                'description': '震荡指标，用于判断超买超卖',
                'signal_logic': 'rsi'
            },
            'KDJ': {
                'name': 'KDJ随机指标',
                'unit': '',
                'description': '短期买卖信号指标',
                'signal_logic': 'kdj'
            },
            'MA': {
                'name': '移动平均线',
                'unit': '元',
                'description': '趋势方向指标',
                'signal_logic': 'ma'
            },
            'BOLL': {
                'name': '布林线',
                'unit': '元',
                'description': '价格通道指标',
                'signal_logic': 'boll'
            }
        }

        info = indicator_info.get(indicator, {
            'name': indicator,
            'unit': '',
            'description': '技术指标',
            'signal_logic': 'default'
        })

        # 计算技术信号
        signal = self._calculate_technical_signal(indicator, values, info['signal_logic'])

        # 计算变化趋势
        trend = self._calculate_trend(values)

        formatted = f"### {info['name']} ({indicator})\n"
        formatted += f"- **当前值**: {latest_value:.4f}{info['unit']}\n"
        formatted += f"- **技术信号**: {signal}\n"
        formatted += f"- **变化趋势**: {trend}\n"
        formatted += f"- **说明**: {info['description']}\n\n"

        return formatted

    def _calculate_technical_signal(self, indicator: str, values: list, logic: str) -> str:
        """计算技术指标信号

        根据不同技术指标的特点计算买卖信号

        Args:
            indicator: 技术指标名称
            values: 数值列表
            logic: 信号计算逻辑

        Returns:
            str: 技术信号描述
        """

        if len(values) < 2:
            return "🟡 数据不足"

        latest_value = values[-1]
        previous_value = values[-2]

        if logic == 'macd':
            # MACD信号逻辑
            if latest_value > 0:
                if latest_value > previous_value:
                    return "🟢 强烈买入信号 (MACD上穿零轴且上升)"
                else:
                    return "🟡 买入信号减弱 (MACD在零轴上方但下降)"
            else:
                if latest_value < previous_value:
                    return "🔴 强烈卖出信号 (MACD下穿零轴且下降)"
                else:
                    return "🟡 卖出信号减弱 (MACD在零轴下方但上升)"

        elif logic == 'rsi':
            # RSI信号逻辑
            if latest_value > 80:
                return "🔴 严重超买 (建议卖出)"
            elif latest_value > 70:
                return "🟠 超买区域 (谨慎买入)"
            elif latest_value < 20:
                return "🟢 严重超卖 (建议买入)"
            elif latest_value < 30:
                return "🟡 超卖区域 (可考虑买入)"
            else:
                trend = "上升" if latest_value > previous_value else "下降"
                return f"🟡 正常区域 (趋势{trend})"

        elif logic == 'kdj':
            # KDJ信号逻辑
            if latest_value > 80:
                if latest_value > previous_value:
                    return "🔴 超买信号 (K值高位上升，建议卖出)"
                else:
                    return "🟠 超买回调 (K值高位下降，观望)"
            elif latest_value < 20:
                if latest_value < previous_value:
                    return "🟢 超卖信号 (K值低位下降，建议买入)"
                else:
                    return "🟡 超卖反弹 (K值低位上升，观望)"
            else:
                trend = "上升" if latest_value > previous_value else "下降"
                return f"🟡 震荡区间 (K值{trend})"

        elif logic == 'ma':
            # 移动平均线信号逻辑
            if len(values) >= 5:
                recent_trend = sum(values[-5:]) / 5
                if latest_value > recent_trend:
                    return "🟢 价格在均线上方 (多头趋势)"
                else:
                    return "🔴 价格在均线下方 (空头趋势)"
            else:
                return "🟡 趋势判断中"

        elif logic == 'boll':
            # 布林线信号逻辑（需要上轨、中轨、下轨数据）
            return "🟡 价格通道分析"

        else:
            # 默认信号逻辑
            trend = "上升" if latest_value > previous_value else "下降"
            return f"🟡 指标{trend}"

    def _calculate_trend(self, values: list) -> str:
        """计算变化趋势

        Args:
            values: 数值列表

        Returns:
            str: 趋势描述
        """

        if len(values) < 3:
            return "数据不足"

        # 计算短期趋势（最近3个点）
        recent_values = values[-3:]
        if recent_values[2] > recent_values[1] > recent_values[0]:
            return "📈 强势上升"
        elif recent_values[2] < recent_values[1] < recent_values[0]:
            return "📉 强势下降"
        elif recent_values[2] > recent_values[0]:
            return "📊 震荡上行"
        elif recent_values[2] < recent_values[0]:
            return "📊 震荡下行"
        else:
            return "➡️ 横盘整理"
