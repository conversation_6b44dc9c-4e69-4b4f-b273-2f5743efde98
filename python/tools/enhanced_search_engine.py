"""
Enhanced Search Engine - 增强搜索引擎 (ENHANCED SEARCH)
深度研究专用工具，当用户说"enhanced search"、"enhance search"、"深入"、"详细"、"全面"、"研究"时使用此工具
关键词触发: enhanced, enhance, enhanced search, 深入, 详细, 全面, 研究, 深度
"""
import asyncio
import json
import aiohttp
from python.helpers.tool import Tool, Response
from python.helpers import files
import re
from urllib.parse import quote


class EnhancedSearchEngine(Tool):
    """
    🔍 ENHANCED SEARCH ENGINE - 增强搜索引擎

    ⚠️ 重要：当用户明确说"enhanced search"或"enhance search"时，必须使用此工具！

    适用场景：
    - 用户说"enhanced search"、"enhance search"
    - 需要"深入"、"详细"、"全面"、"研究"的搜索
    - 要求"深度分析"、"系统性搜索"的场景

    功能特点：
    - 多轮搜索策略（基础+扩展+相关）
    - 结果质量评估和排序
    - 智能摘要生成
    - 更全面的信息收集
    """

    async def execute(self, query: str = "", **kwargs):
        """
        🚀 执行增强搜索 - ENHANCED SEARCH

        当检测到以下关键词时使用：
        - "enhanced search", "enhance search"
        - "深入", "详细", "全面", "研究", "深度"

        执行多轮搜索、结果质量评估、智能摘要生成
        """
        if not query:
            return Response(message="请提供搜索查询内容", break_loop=False)

        try:
            # 第一轮：基础搜索
            print("🔍 执行基础搜索...")
            basic_results = await self._basic_search(query)

            # 第二轮：扩展关键词搜索
            print("🔍 执行扩展关键词搜索...")
            expanded_results = await self._expanded_search(query)

            # 第三轮：相关主题搜索
            print("🔍 执行相关主题搜索...")
            related_results = await self._related_search(query)

            # 结果整合与质量评估
            print("📊 整合搜索结果...")
            integrated_results = await self._integrate_results(
                basic_results, expanded_results, related_results
            )

            # 生成智能摘要
            print("📝 生成智能摘要...")
            summary = await self._generate_summary(query, integrated_results)

            # 格式化最终结果
            final_result = self._format_enhanced_result(query, summary, integrated_results)

            return Response(message=final_result, break_loop=False)

        except Exception as e:
            error_msg = f"增强搜索执行失败: {str(e)}"
            print(f"❌ {error_msg}")
            return Response(message=error_msg, break_loop=False)

    async def _basic_search(self, query: str) -> list:
        """执行基础SearXNG搜索"""
        try:
            searxng_url = "http://localhost:8888/search"
            params = {
                'q': query,
                'format': 'json',
                'categories': 'general',
                'engines': 'google,bing,brave'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(searxng_url, params=params, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('results', [])[:10]  # 取前10个结果
                    else:
                        return []
        except Exception as e:
            print(f"基础搜索失败: {e}")
            return []

    async def _expanded_search(self, query: str) -> list:
        """执行扩展关键词搜索"""
        try:
            # 生成扩展关键词
            expanded_queries = self._generate_expanded_queries(query)
            all_results = []

            for expanded_query in expanded_queries[:3]:  # 限制为3个扩展查询
                results = await self._basic_search(expanded_query)
                all_results.extend(results[:5])  # 每个查询取前5个结果
                await asyncio.sleep(1)  # 避免请求过于频繁

            return all_results
        except Exception as e:
            print(f"扩展搜索失败: {e}")
            return []

    async def _related_search(self, query: str) -> list:
        """执行相关主题搜索"""
        try:
            # 生成相关主题查询
            related_queries = self._generate_related_queries(query)
            all_results = []

            for related_query in related_queries[:2]:  # 限制为2个相关查询
                results = await self._basic_search(related_query)
                all_results.extend(results[:3])  # 每个查询取前3个结果
                await asyncio.sleep(1)

            return all_results
        except Exception as e:
            print(f"相关搜索失败: {e}")
            return []

    def _generate_expanded_queries(self, query: str) -> list:
        """生成扩展关键词查询"""
        expanded_queries = []

        # 添加深度研究关键词
        depth_keywords = ["详细介绍", "深入分析", "全面解析", "研究报告"]
        for keyword in depth_keywords:
            expanded_queries.append(f"{query} {keyword}")

        # 添加时间相关关键词
        time_keywords = ["最新", "2024", "2025", "趋势"]
        for keyword in time_keywords:
            expanded_queries.append(f"{query} {keyword}")

        # 添加类型相关关键词
        type_keywords = ["原理", "方法", "技术", "应用"]
        for keyword in type_keywords:
            expanded_queries.append(f"{query} {keyword}")

        return expanded_queries

    def _generate_related_queries(self, query: str) -> list:
        """生成相关主题查询"""
        related_queries = []

        # 添加对比关键词
        compare_keywords = ["对比", "比较", "优缺点", "差异"]
        for keyword in compare_keywords:
            related_queries.append(f"{query} {keyword}")

        # 添加实践关键词
        practice_keywords = ["实践", "案例", "示例", "教程"]
        for keyword in practice_keywords:
            related_queries.append(f"{query} {keyword}")

        return related_queries

    async def _integrate_results(self, *result_lists) -> list:
        """整合多轮搜索结果并去重"""
        all_results = []
        seen_urls = set()

        for result_list in result_lists:
            for result in result_list:
                url = result.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    # 添加质量评分
                    result['quality_score'] = self._calculate_quality_score(result)
                    all_results.append(result)

        # 按质量评分排序
        all_results.sort(key=lambda x: x.get('quality_score', 0), reverse=True)

        return all_results[:20]  # 返回前20个高质量结果

    def _calculate_quality_score(self, result: dict) -> float:
        """计算搜索结果质量评分 (0-10分)"""
        score = 0.0

        title = result.get('title', '').lower()
        content = result.get('content', '').lower()
        url = result.get('url', '').lower()

        # 基础分数 (所有结果都有的基础分)
        score += 3.0  # 基础分3分

        # 标题质量评分 (最高2.5分)
        if len(title) > 10:
            score += 0.5
        if len(title) > 30:
            score += 0.5

        # 扩展关键词匹配 (中英文)
        quality_keywords = [
            # 中文关键词
            '详细', '全面', '深入', '研究', '分析', '完整', '系统', '专业', '权威',
            '介绍', '指南', '教程', '方法', '技术', '原理', '应用', '发展', '趋势',
            # 英文关键词
            'detailed', 'comprehensive', 'complete', 'guide', 'tutorial',
            'analysis', 'research', 'study', 'review', 'overview', 'introduction',
            'technical', 'professional', 'official', 'latest', 'update'
        ]
        if any(keyword in title for keyword in quality_keywords):
            score += 1.5

        # 内容质量评分 (最高2分)
        if len(content) > 50:
            score += 0.5
        if len(content) > 150:
            score += 0.5
        if len(content) > 300:
            score += 0.5
        if len(content) > 500:
            score += 0.5

        # URL质量评分 (最高2分)
        quality_domains = [
            'wikipedia', 'edu', 'gov', 'org', 'ac.', 'ieee', 'acm',
            'nature', 'science', 'arxiv', 'researchgate', 'scholar',
            'official', 'docs', 'documentation', 'manual', 'guide'
        ]
        if any(domain in url for domain in quality_domains):
            score += 2.0
        elif any(domain in url for domain in ['com', 'net', 'io', 'co']):
            score += 0.5  # 商业域名给少量分数

        # 内容相关性加分 (最高1分)
        if title and content:
            # 标题和内容都有内容
            score += 0.5
            # 内容不是重复标题
            if content.strip() != title.strip():
                score += 0.5

        # 避免低质量内容 (扣分)
        low_quality_indicators = [
            '广告', '推广', '购买', '下载', '免费', '优惠', '促销',
            'ad', 'ads', 'advertisement', 'promotion', 'buy', 'sale',
            'download', 'free', 'discount', 'offer'
        ]
        if any(indicator in title + content for indicator in low_quality_indicators):
            score -= 1.5

        # 确保分数在0-10范围内
        return max(0.0, min(score, 10.0))

    async def _generate_summary(self, query: str, results: list) -> str:
        """生成智能摘要"""
        if not results:
            return f"关于'{query}'的搜索未找到相关结果。"

        # 提取关键信息
        key_points = []
        for result in results[:10]:  # 使用前10个高质量结果
            title = result.get('title', '')
            content = result.get('content', '')
            if title and content:
                key_points.append(f"• {title}: {content[:100]}...")

        # 生成摘要
        summary = f"""
📊 关于'{query}'的增强搜索摘要

🔍 搜索范围: 执行了基础搜索、扩展关键词搜索和相关主题搜索
📈 结果质量: 找到 {len(results)} 个相关结果，已按质量评分排序
⭐ 质量评分: 平均分 {sum(r.get('quality_score', 0) for r in results) / len(results):.1f}/10

📝 主要发现:
{chr(10).join(key_points[:5])}

💡 建议: 基于搜索结果，建议进一步关注以下方面：
{self._generate_suggestions(results)}
"""
        return summary.strip()

    def _generate_suggestions(self, results: list) -> str:
        """基于搜索结果生成建议"""
        suggestions = []

        # 分析高质量结果的共同主题
        all_titles = ' '.join([r.get('title', '') for r in results[:5]])

        # 提取常见关键词
        common_keywords = ['技术', '方法', '应用', '发展', '趋势', '原理', '实践']
        found_keywords = [kw for kw in common_keywords if kw in all_titles]

        if found_keywords:
            suggestions.append(f"深入了解: {', '.join(found_keywords[:3])}")

        suggestions.append("查看权威来源的详细文档")
        suggestions.append("关注最新发展动态和趋势")

        return '\n'.join([f"  - {s}" for s in suggestions])

    def _format_enhanced_result(self, query: str, summary: str, results: list) -> str:
        """格式化增强搜索结果 - 简洁版本"""

        # 限制结果数量，避免输出过长
        top_results = results[:5]  # 只显示前5个结果

        formatted_results = []
        for i, result in enumerate(top_results, 1):
            title = result.get('title', '无标题')
            url = result.get('url', '')
            content = result.get('content', '无描述')
            quality_score = result.get('quality_score', 0)

            # 简化格式，减少特殊字符
            formatted_result = f"{i}. {title[:80]}{'...' if len(title) > 80 else ''}\n   评分: {quality_score:.1f}/10 | {content[:100]}{'...' if len(content) > 100 else ''}"
            formatted_results.append(formatted_result)

        # 简化最终输出格式
        final_result = f"""📊 关于'{query}'的搜索摘要

{summary}

🔍 主要结果 (前5项):
{chr(10).join(formatted_results)}

✅ 搜索完成，共分析 {len(results)} 个结果。"""

        return final_result.strip()
