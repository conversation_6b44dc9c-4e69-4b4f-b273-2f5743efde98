#!/usr/bin/env python3
"""
测试financial_data_tool的Response修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python.helpers.tool import Response

def test_response_creation():
    """测试Response类的正确创建"""
    try:
        # 测试正确的Response创建
        response1 = Response(message="测试消息", break_loop=False)
        print("✅ Response创建成功 - 正确参数")
        print(f"   message: {response1.message}")
        print(f"   break_loop: {response1.break_loop}")
        
        # 测试错误的Response创建（应该失败）
        try:
            response2 = Response(message="测试消息", data="测试数据")
            print("❌ 错误：不应该成功创建带有data参数的Response")
        except TypeError as e:
            print("✅ 正确：带有data参数的Response创建失败")
            print(f"   错误信息: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Response测试失败: {e}")
        return False

def test_financial_tool_import():
    """测试financial_data_tool的导入"""
    try:
        from python.tools.financial_data_tool import FinancialDataTool
        print("✅ FinancialDataTool导入成功")
        return True
    except Exception as e:
        print(f"❌ FinancialDataTool导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始测试financial_data_tool修复...")
    print("=" * 50)
    
    # 测试Response类
    print("\n1. 测试Response类:")
    response_ok = test_response_creation()
    
    # 测试FinancialDataTool导入
    print("\n2. 测试FinancialDataTool导入:")
    import_ok = test_financial_tool_import()
    
    print("\n" + "=" * 50)
    if response_ok and import_ok:
        print("✅ 所有测试通过！financial_data_tool修复成功")
    else:
        print("❌ 部分测试失败，需要进一步检查")
