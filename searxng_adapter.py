#!/usr/bin/env python3
"""
SearXNG适配器 - 封装SearXNG API调用
"""

import asyncio
import aiohttp
import logging
import time
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, quote
import json

logger = logging.getLogger(__name__)

class SearXNGAdapter:
    """SearXNG搜索引擎适配器"""
    
    def __init__(self, base_url: str = "http://localhost:8888", timeout: int = 30, max_retries: int = 3):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = None
        self._connector = None
        
        # 搜索引擎配置
        self.default_engines = "google,bing,brave"
        self.categories = "general"
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'cache_hits': 0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_session(self):
        """确保HTTP会话存在"""
        if self.session is None or self.session.closed:
            # 创建连接器
            self._connector = aiohttp.TCPConnector(
                limit=20,  # 总连接池大小
                limit_per_host=10,  # 每个主机的连接数
                ttl_dns_cache=300,  # DNS缓存TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            # 创建会话
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Enhanced-Search-MCP/1.0',
                    'Accept': 'application/json',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
            )
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            await self.session.close()
        if self._connector:
            await self._connector.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            await self._ensure_session()
            
            start_time = time.time()
            async with self.session.get(f"{self.base_url}/") as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    return {
                        'status': 'healthy',
                        'response_time': response_time,
                        'searxng_url': self.base_url
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'error': f'HTTP {response.status}',
                        'response_time': response_time
                    }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def search(
        self, 
        query: str, 
        engines: Optional[str] = None,
        categories: Optional[str] = None,
        language: str = "zh-CN",
        time_range: Optional[str] = None,
        safesearch: int = 1
    ) -> Dict[str, Any]:
        """
        执行搜索查询
        
        Args:
            query: 搜索查询
            engines: 搜索引擎列表，如 "google,bing,brave"
            categories: 搜索类别，如 "general"
            language: 搜索语言
            time_range: 时间范围，如 "day", "week", "month", "year"
            safesearch: 安全搜索级别 (0=关闭, 1=中等, 2=严格)
        
        Returns:
            搜索结果字典
        """
        await self._ensure_session()
        
        # 构建搜索参数
        params = {
            'q': query,
            'format': 'json',
            'categories': categories or self.categories,
            'engines': engines or self.default_engines,
            'language': language,
            'safesearch': safesearch
        }
        
        # 添加时间范围（如果指定）
        if time_range:
            params['time_range'] = time_range
        
        # 执行搜索请求
        return await self._make_request('/search', params)
    
    async def search_multiple_engines(
        self, 
        query: str, 
        engine_groups: List[str]
    ) -> List[Dict[str, Any]]:
        """
        使用多个引擎组并发搜索
        
        Args:
            query: 搜索查询
            engine_groups: 引擎组列表，如 ["google,bing", "brave,duckduckgo"]
        
        Returns:
            搜索结果列表
        """
        tasks = []
        for engines in engine_groups:
            task = self.search(query, engines=engines)
            tasks.append(task)
        
        # 并发执行搜索
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.warning(f"Engine group {engine_groups[i]} failed: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    async def suggest(self, query: str) -> List[str]:
        """
        获取搜索建议
        
        Args:
            query: 查询字符串
            
        Returns:
            建议列表
        """
        try:
            params = {
                'q': query,
                'format': 'json'
            }
            
            result = await self._make_request('/autocompleter', params)
            
            # 提取建议
            if isinstance(result, list):
                return [suggestion for suggestion in result if isinstance(suggestion, str)]
            elif isinstance(result, dict) and 'suggestions' in result:
                return result['suggestions']
            else:
                return []
                
        except Exception as e:
            logger.warning(f"Failed to get suggestions for '{query}': {e}")
            return []
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发起HTTP请求
        
        Args:
            endpoint: API端点
            params: 请求参数
            
        Returns:
            响应数据
        """
        url = urljoin(self.base_url, endpoint)
        
        # 更新统计
        self.stats['total_requests'] += 1
        start_time = time.time()
        
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                await self._ensure_session()
                
                async with self.session.get(url, params=params) as response:
                    response_time = time.time() - start_time
                    self.stats['total_response_time'] += response_time
                    
                    if response.status == 200:
                        data = await response.json()
                        self.stats['successful_requests'] += 1
                        
                        logger.debug(f"Search successful: {url} (attempt {attempt + 1}, {response_time:.2f}s)")
                        return data
                    
                    elif response.status == 429:  # Rate limited
                        wait_time = 2 ** attempt  # 指数退避
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                        await asyncio.sleep(wait_time)
                        continue
                    
                    else:
                        error_text = await response.text()
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}: {error_text}"
                        )
            
            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"Request timeout on attempt {attempt + 1}: {url}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
                    
            except aiohttp.ClientError as e:
                last_exception = e
                logger.warning(f"Client error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                last_exception = e
                logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1)
        
        # 所有重试都失败了
        self.stats['failed_requests'] += 1
        error_msg = f"Request failed after {self.max_retries} attempts: {last_exception}"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算平均响应时间
        if stats['successful_requests'] > 0:
            stats['avg_response_time'] = stats['total_response_time'] / stats['successful_requests']
        else:
            stats['avg_response_time'] = 0.0
        
        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'cache_hits': 0
        }
