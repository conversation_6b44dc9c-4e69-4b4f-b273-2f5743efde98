#!/usr/bin/env python3
"""
Enhanced Search Engine MCP服务器
基于SearXNG的增强搜索引擎MCP服务
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastmcp import FastMCP
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

# 导入模块
from enhanced_search_engine import EnhancedSearchEngine
from searxng_adapter import SearXNGAdapter
from config import load_config, get_config

# 设置日志
logger = logging.getLogger(__name__)

# 创建FastMCP服务器实例
mcp_server = FastMCP(
    name="Enhanced Search Engine MCP Server",
    instructions="""
    专业增强搜索引擎MCP服务器，提供以下功能：
    
    🔍 核心功能：
    - 多轮搜索策略（基础+扩展+相关主题）
    - 智能结果质量评估和排序
    - 自动摘要生成
    - 搜索结果缓存优化
    
    🎯 搜索深度：
    - basic: 仅基础搜索
    - deep: 基础+扩展搜索（默认）
    - comprehensive: 基础+扩展+相关搜索
    
    🚀 适用场景：
    - 深度研究和信息收集
    - 全面的主题分析
    - 高质量信息筛选
    - 智能搜索建议
    
    数据源：SearXNG多引擎聚合搜索
    """
)

# 全局组件实例
config = None
searxng_adapter = None
search_engine = None

async def initialize_components():
    """初始化组件"""
    global config, searxng_adapter, search_engine
    
    try:
        # 加载配置
        config = load_config()
        logger.info("配置加载成功")
        
        # 创建SearXNG适配器
        searxng_adapter = SearXNGAdapter(
            base_url=config.SEARXNG_URL,
            timeout=config.SEARXNG_TIMEOUT,
            max_retries=config.SEARXNG_MAX_RETRIES
        )
        logger.info(f"SearXNG适配器创建成功: {config.SEARXNG_URL}")
        
        # 创建搜索引擎
        search_engine = EnhancedSearchEngine(searxng_adapter, config)
        logger.info("增强搜索引擎创建成功")
        
        # 测试SearXNG连接
        health_status = await searxng_adapter.health_check()
        if health_status['status'] == 'healthy':
            logger.info("SearXNG连接测试成功")
        else:
            logger.warning(f"SearXNG连接异常: {health_status}")
        
    except Exception as e:
        logger.error(f"组件初始化失败: {e}")
        raise

@mcp_server.tool()
async def enhanced_search(
    query: str,
    search_depth: str = "deep",
    max_results: int = 20,
    enable_cache: bool = True
) -> str:
    """
    执行增强搜索
    
    Args:
        query: 搜索查询内容
        search_depth: 搜索深度 ("basic", "deep", "comprehensive")
        max_results: 最大结果数量 (1-50)
        enable_cache: 是否启用缓存
    
    Returns:
        格式化的搜索结果和摘要
    """
    try:
        # 参数验证
        if not query or not query.strip():
            return "❌ 请提供有效的搜索查询内容"
        
        if search_depth not in ["basic", "deep", "comprehensive"]:
            return "❌ 搜索深度必须是: basic, deep, comprehensive"
        
        if not (1 <= max_results <= 50):
            return "❌ 最大结果数量必须在1-50之间"
        
        logger.info(f"执行增强搜索: '{query}' (深度: {search_depth}, 最大结果: {max_results})")
        
        # 确保组件已初始化
        if search_engine is None:
            await initialize_components()
        
        # 执行搜索
        result = await search_engine.execute(
            query=query,
            depth=search_depth,
            max_results=max_results,
            enable_cache=enable_cache
        )
        
        return result
        
    except Exception as e:
        error_msg = f"增强搜索失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp_server.tool()
async def search_suggestions(query: str) -> str:
    """
    获取搜索建议
    
    Args:
        query: 原始查询
        
    Returns:
        搜索建议列表
    """
    try:
        if not query or not query.strip():
            return "❌ 请提供有效的查询内容"
        
        logger.info(f"生成搜索建议: '{query}'")
        
        # 确保组件已初始化
        if search_engine is None:
            await initialize_components()
        
        suggestions = await search_engine.generate_search_suggestions(query)
        
        if suggestions:
            return f"针对'{query}'的搜索建议：\n" + "\n".join([f"• {s}" for s in suggestions])
        else:
            return f"暂无针对'{query}'的搜索建议"
        
    except Exception as e:
        error_msg = f"生成搜索建议失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

@mcp_server.tool()
async def search_stats() -> str:
    """
    获取搜索统计信息
    
    Returns:
        统计信息
    """
    try:
        if search_engine is None:
            return "搜索引擎未初始化"
        
        stats = search_engine.get_stats()
        searxng_stats = searxng_adapter.get_stats() if searxng_adapter else {}
        
        return f"""📊 增强搜索引擎统计信息

🔍 搜索引擎统计:
• 总搜索次数: {stats['total_searches']}
• 成功搜索: {stats['successful_searches']}
• 失败搜索: {stats['failed_searches']}
• 成功率: {(stats['successful_searches']/max(stats['total_searches'], 1)*100):.1f}%
• 处理结果总数: {stats['total_results_processed']}
• 平均质量评分: {stats['avg_quality_score']:.2f}/10

🌐 SearXNG适配器统计:
• 总请求次数: {searxng_stats.get('total_requests', 0)}
• 成功请求: {searxng_stats.get('successful_requests', 0)}
• 失败请求: {searxng_stats.get('failed_requests', 0)}
• 平均响应时间: {searxng_stats.get('avg_response_time', 0):.2f}秒
• 成功率: {(searxng_stats.get('success_rate', 0)*100):.1f}%

⏰ 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
    except Exception as e:
        error_msg = f"获取统计信息失败: {str(e)}"
        logger.error(error_msg)
        return error_msg

# 创建FastAPI应用
app = FastAPI(
    title="Enhanced Search Engine MCP Server", 
    version="1.0.0",
    description="基于SearXNG的增强搜索引擎MCP服务"
)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    try:
        await initialize_components()
        logger.info("MCP服务器启动完成")
    except Exception as e:
        logger.error(f"MCP服务器启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    try:
        if searxng_adapter:
            await searxng_adapter.close()
        logger.info("MCP服务器关闭完成")
    except Exception as e:
        logger.error(f"MCP服务器关闭异常: {e}")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查组件状态
        if search_engine is None or searxng_adapter is None:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": "Components not initialized",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        # 检查SearXNG状态
        searxng_health = await searxng_adapter.health_check()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "search_engine": "initialized",
                "searxng_adapter": "initialized",
                "searxng_status": searxng_health
            },
            "config": {
                "searxng_url": config.SEARXNG_URL if config else "not configured",
                "max_concurrent_searches": config.MAX_CONCURRENT_SEARCHES if config else "not configured"
            }
        }
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/tools")
async def list_tools():
    """列出所有可用工具"""
    return {
        "tools": [
            {
                "name": "enhanced_search",
                "description": "执行增强搜索，包含多轮搜索策略和智能摘要",
                "parameters": {
                    "query": "搜索查询内容",
                    "search_depth": "搜索深度 (basic/deep/comprehensive)",
                    "max_results": "最大结果数量 (1-50)",
                    "enable_cache": "是否启用缓存"
                }
            },
            {
                "name": "search_suggestions",
                "description": "获取搜索建议和优化建议",
                "parameters": {
                    "query": "原始查询内容"
                }
            },
            {
                "name": "search_stats",
                "description": "获取搜索引擎统计信息",
                "parameters": {}
            }
        ]
    }

# 集成MCP服务器到FastAPI
app.mount("/mcp", mcp_server.sse_app)

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("SEARXNG_URL"):
        logger.warning("未设置SEARXNG_URL环境变量，使用默认值 http://localhost:8888")
    
    logger.info("🚀 启动增强搜索引擎MCP服务器...")
    logger.info("📡 SSE端点: http://localhost:8881/mcp/sse")
    logger.info("🏥 健康检查: http://localhost:8881/health")
    logger.info("🛠️ 工具列表: http://localhost:8881/tools")
    logger.info("✨ 功能: 多轮搜索、智能排序、自动摘要")
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8881,
        log_level="info"
    )
