#!/usr/bin/env python3
"""
测试financial_data_tool根据手册的改进
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python.helpers.financial_api_client import FinancialAPIClient

async def test_error_handling():
    """测试错误处理改进"""
    print("🔧 测试错误处理改进...")
    
    client = FinancialAPIClient()
    
    # 测试错误码映射
    test_codes = [-1010, -4001, -4206, -4400, -9999]
    
    for code in test_codes:
        error_msg = client._get_error_message(code)
        print(f"   错误码 {code}: {error_msg}")
    
    print("✅ 错误处理测试完成\n")

async def test_api_improvements():
    """测试API改进"""
    print("🔧 测试API改进...")
    
    try:
        client = FinancialAPIClient()
        
        # 测试健康检查
        health = client.health_check()
        print(f"   健康检查: {health['status']}")
        
        if health['status'] == 'healthy':
            print("   ✅ API客户端健康")
            
            # 测试实时行情（使用新的指标）
            print("   测试实时行情API...")
            result = await client.get_real_time_quotation("000858.SZ", "tradeDate,tradeTime,latest,preClose,totalShares,totalCapital")
            
            if result.get('errorcode') == 0:
                print("   ✅ 实时行情API调用成功")
                # 显示返回的数据结构
                tables = result.get('data', {}).get('tables', [])
                if tables:
                    print(f"   数据表数量: {len(tables)}")
                    table_data = tables[0].get('table', {})
                    if table_data:
                        print(f"   数据字段: {list(table_data.keys())}")
            else:
                error_code = result.get('errorcode')
                error_msg = client._get_error_message(error_code)
                print(f"   ❌ 实时行情API调用失败: {error_msg}")
                
        else:
            print(f"   ❌ API客户端不健康: {health.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"   ❌ API测试异常: {e}")
    
    print("✅ API改进测试完成\n")

async def test_historical_data():
    """测试历史数据改进"""
    print("🔧 测试历史数据改进...")
    
    try:
        client = FinancialAPIClient()
        
        # 测试历史行情（使用新的指标）
        result = await client.get_history_quotation(
            "000858.SZ", 
            "2024-01-01", 
            "2024-01-05",
            "preClose,open,high,low,close,volume,amount,turnoverRatio,changeRatio"
        )
        
        if result.get('errorcode') == 0:
            print("   ✅ 历史行情API调用成功")
            tables = result.get('data', {}).get('tables', [])
            if tables:
                print(f"   历史数据表数量: {len(tables)}")
        else:
            error_code = result.get('errorcode')
            error_msg = client._get_error_message(error_code) if hasattr(client, '_get_error_message') else '未知错误'
            print(f"   ❌ 历史行情API调用失败: {error_msg}")
            
    except Exception as e:
        print(f"   ❌ 历史数据测试异常: {e}")
    
    print("✅ 历史数据改进测试完成\n")

async def test_basic_data():
    """测试基础数据改进"""
    print("🔧 测试基础数据改进...")
    
    try:
        client = FinancialAPIClient()
        
        # 测试基础数据（现在使用实时行情API）
        result = await client.get_basic_data("000858.SZ")
        
        if result.get('errorcode') == 0:
            print("   ✅ 基础数据API调用成功")
            print("   注意: 基础数据现在通过实时行情API获取，更加稳定")
        else:
            error_code = result.get('errorcode')
            error_msg = client._get_error_message(error_code) if hasattr(client, '_get_error_message') else '未知错误'
            print(f"   ❌ 基础数据API调用失败: {error_msg}")
            
    except Exception as e:
        print(f"   ❌ 基础数据测试异常: {e}")
    
    print("✅ 基础数据改进测试完成\n")

def test_token_handling():
    """测试Token处理改进"""
    print("🔧 测试Token处理改进...")
    
    try:
        client = FinancialAPIClient()
        
        # 检查refresh_token配置
        if client.refresh_token:
            print("   ✅ REFRESH_TOKEN已配置")
            print("   注意: Token现在按照手册要求放在headers中")
        else:
            print("   ⚠️  REFRESH_TOKEN未配置，某些功能可能受限")
            
    except Exception as e:
        print(f"   ❌ Token处理测试异常: {e}")
    
    print("✅ Token处理改进测试完成\n")

async def main():
    """主测试函数"""
    print("🚀 === Financial Data Tool 手册改进测试 ===")
    print("根据HTTP20230404用户手册.txt进行的改进验证\n")
    
    # 运行所有测试
    await test_error_handling()
    test_token_handling()
    await test_api_improvements()
    await test_historical_data()
    await test_basic_data()
    
    print("🎉 === 所有改进测试完成 ===")
    print("\n主要改进内容:")
    print("1. ✅ Token处理：refresh_token现在放在headers中")
    print("2. ✅ 错误处理：添加了完整的错误码映射")
    print("3. ✅ API指标：使用手册推荐的标准指标")
    print("4. ✅ 基础数据：改用更稳定的实时行情API")
    print("5. ✅ 数据格式：支持更多手册中的数据字段")

if __name__ == "__main__":
    asyncio.run(main())
