{"mcpServers": {"agent-zero": {"type": "sse", "serverUrl": "http://localhost:50001/mcp/t-0/sse", "category": "agent", "description": "Autonomous AI agent for complex task execution", "enabled": true, "keywords": ["agent", "autonomous", "ai agent", "intelligent agent", "automation", "execute task", "run command", "perform action", "agent zero", "complex task", "multi-step", "workflow", "orchestration", "search", "find", "look up", "browse", "web search", "information about", "what is", "who is", "when did", "where is", "how to", "latest news", "read file", "write file", "create file", "list files", "file content", "analyze", "solve", "help me", "write code", "generate", "create", "calculate", "compute", "process", "handle", "manage", "organize", "write", "code", "programming", "develop", "build", "make", "智能代理", "自动化", "执行任务", "复杂任务", "工作流", "搜索", "查找", "查询", "信息", "帮助", "分析", "解决", "处理"]}, "excel-stdio": {"type": "stdio", "command": "uvx", "args": ["excel-mcp-server", "stdio"], "category": "file", "description": "Excel MCP Server - Excel file processing", "enabled": true, "keywords": ["excel", "xlsx", "xls", "spreadsheet", "表格", "电子表格", "read excel", "write excel", "excel processing", "excel data", "工作表", "数据处理", "表格处理"]}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"], "category": "documentation", "description": "Context7 MCP Server - Up-to-date code documentation", "enabled": true, "keywords": ["documentation", "docs", "code docs", "api docs", "文档", "代码文档", "API文档", "技术文档", "开发文档"]}, "postgres-basic": {"type": "sse", "url": "http://************:5000/mcp/sse", "category": "database", "description": "Google AI Toolbox for Database Operations", "enabled": true, "timeout": 30, "sse_read_timeout": 300, "keywords": ["database", "postgres", "postgresql", "sql", "数据库", "查询", "数据查询", "数据库操作", "SQL查询"]}, "postgres-expert": {"type": "sse", "url": "http://************:8000/sse", "category": "database", "description": "PostgreSQL Expert MCP Server", "enabled": true, "timeout": 10, "sse_read_timeout": 300, "keywords": ["postgresql", "postgres expert", "advanced sql", "database expert", "数据库专家", "高级SQL", "PostgreSQL专家", "数据库优化"]}}, "default_server": "agent-zero", "timeout": 30, "retry_attempts": 3}