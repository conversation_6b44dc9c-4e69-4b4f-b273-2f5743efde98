#!/bin/bash

# Enhanced Search Engine MCP服务器启动脚本

set -e  # 遇到错误立即退出

echo "🚀 启动增强搜索引擎MCP服务器..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查当前目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

echo -e "${BLUE}项目目录: $PROJECT_DIR${NC}"
echo -e "${BLUE}MCP目录: $MCP_DIR${NC}"

# 检查MCP目录是否存在
if [ ! -d "$MCP_DIR" ]; then
    echo -e "${RED}❌ MCP目录不存在: $MCP_DIR${NC}"
    exit 1
fi

# 切换到MCP目录
cd "$MCP_DIR"

# 检查SearXNG是否运行
echo -e "${YELLOW}🔍 检查SearXNG服务状态...${NC}"
SEARXNG_URL="${SEARXNG_URL:-http://localhost:8888}"

if curl -s --connect-timeout 5 "$SEARXNG_URL" > /dev/null; then
    echo -e "${GREEN}✅ SearXNG服务运行正常: $SEARXNG_URL${NC}"
else
    echo -e "${RED}❌ SearXNG服务未运行或无法访问: $SEARXNG_URL${NC}"
    echo -e "${YELLOW}请先启动SearXNG服务，然后重新运行此脚本${NC}"
    exit 1
fi

# 检查Python环境
echo -e "${YELLOW}🐍 检查Python环境...${NC}"
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo -e "${RED}❌ 未找到Python解释器${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Python解释器: $PYTHON_CMD${NC}"
$PYTHON_CMD --version

# 检查虚拟环境
if [ -n "$VIRTUAL_ENV" ]; then
    echo -e "${GREEN}✅ 虚拟环境已激活: $VIRTUAL_ENV${NC}"
else
    echo -e "${YELLOW}⚠️  未检测到虚拟环境，建议使用虚拟环境${NC}"
fi

# 安装依赖
echo -e "${YELLOW}📦 检查并安装依赖...${NC}"
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
elif [ -f "../enhanced_search_requirements.txt" ]; then
    pip install -r ../enhanced_search_requirements.txt
else
    echo -e "${YELLOW}⚠️  未找到requirements.txt，手动安装核心依赖...${NC}"
    pip install fastmcp fastapi uvicorn aiohttp python-dotenv
fi

# 检查必要文件
echo -e "${YELLOW}📁 检查必要文件...${NC}"
REQUIRED_FILES=("server.py" "enhanced_search_engine.py" "searxng_adapter.py" "config.py")

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ 缺少必要文件: $file${NC}"
        exit 1
    fi
done

# 设置环境变量
echo -e "${YELLOW}⚙️  设置环境变量...${NC}"
export SEARXNG_URL="${SEARXNG_URL:-http://localhost:8888}"
export MCP_HOST="${MCP_HOST:-0.0.0.0}"
export MCP_PORT="${MCP_PORT:-8881}"
export LOG_LEVEL="${LOG_LEVEL:-INFO}"

echo -e "${BLUE}SEARXNG_URL: $SEARXNG_URL${NC}"
echo -e "${BLUE}MCP_HOST: $MCP_HOST${NC}"
echo -e "${BLUE}MCP_PORT: $MCP_PORT${NC}"
echo -e "${BLUE}LOG_LEVEL: $LOG_LEVEL${NC}"

# 创建日志目录
mkdir -p logs

# 检查端口是否被占用
echo -e "${YELLOW}🔌 检查端口 $MCP_PORT...${NC}"
if lsof -Pi :$MCP_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${RED}❌ 端口 $MCP_PORT 已被占用${NC}"
    echo -e "${YELLOW}正在尝试终止占用进程...${NC}"
    lsof -ti:$MCP_PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 启动服务器
echo -e "${GREEN}🚀 启动增强搜索引擎MCP服务器...${NC}"
echo -e "${BLUE}📡 SSE端点: http://localhost:$MCP_PORT/mcp/sse${NC}"
echo -e "${BLUE}🏥 健康检查: http://localhost:$MCP_PORT/health${NC}"
echo -e "${BLUE}🛠️  工具列表: http://localhost:$MCP_PORT/tools${NC}"
echo ""
echo -e "${YELLOW}按 Ctrl+C 停止服务器${NC}"
echo ""

# 启动服务器（前台运行）
exec $PYTHON_CMD server.py
