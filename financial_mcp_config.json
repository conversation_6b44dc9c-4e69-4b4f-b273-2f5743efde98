{"mcpServers": {"financial-analysis-sse": {"type": "sse", "url": "http://localhost:8080/mcp/sse", "description": "专业金融数据分析MCP服务器 - SSE版本，提供实时行情、技术指标分析等功能", "category": "financial", "enabled": true, "init_timeout": 10, "tool_timeout": 30, "sse_read_timeout": 300, "keywords": ["股票", "股价", "行情", "金融", "financial", "stock", "技术指标", "MACD", "RSI", "KDJ", "均线", "MA", "实时行情", "历史数据", "财务报表", "基础数据", "成交量", "涨跌幅", "市值", "贵州茅台", "五粮液", "比亚迪", "平安银行", "山西汾酒", "招商银行", "中国平安", "宁德时代", "查询股票", "分析股票", "股票分析", "技术分析", "指标分析", "趋势分析", "投资分析"]}, "financial-analysis-stdio": {"type": "stdio", "command": "python3", "args": ["financial_mcp_server_stdio.py"], "description": "金融数据分析MCP服务器 - Stdio版本（备用方案）", "category": "financial", "enabled": false, "init_timeout": 5, "tool_timeout": 20, "keywords": ["股票", "金融", "financial"]}}}