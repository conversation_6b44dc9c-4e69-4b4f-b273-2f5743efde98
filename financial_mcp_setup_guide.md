# 金融MCP服务器环境配置指南

## 📁 **项目路径配置**

**项目位置**: `E:\AI\financial-mcp-server`  
**虚拟环境**: 推荐使用Conda（与现有AZ091环境保持一致）

---

## 🐍 **Conda虚拟环境配置**

### **方案1: 创建独立环境（推荐）**

```bash
# 创建专用的conda环境
conda create -n financial-mcp python=3.11 -y

# 激活环境
conda activate financial-mcp

# 安装基础依赖
conda install -c conda-forge fastapi uvicorn aiohttp requests python-dotenv -y
pip install fastmcp pydantic orjson
```

### **方案2: 复用现有AZ091环境**

```bash
# 激活现有环境
conda activate AZ091

# 安装MCP相关依赖
pip install fastmcp
pip install uvicorn[standard]

# 检查是否有冲突
pip check
```

### **推荐选择方案1的原因**
- ✅ **环境隔离**: 避免与现有项目依赖冲突
- ✅ **版本控制**: 可以使用最新的Python 3.11
- ✅ **独立管理**: MCP服务器独立升级维护
- ✅ **故障隔离**: 不影响现有Agent-Zero环境

---

## 📂 **项目结构创建**

### **完整目录结构**
```
E:\AI\financial-mcp-server\
├── server.py                    # MCP服务器主入口
├── requirements.txt              # 依赖包列表
├── environment.yml               # Conda环境配置
├── start_server.bat              # Windows启动脚本
├── start_server.sh               # Linux启动脚本
├── .env.example                  # 环境变量模板
├── README.md                     # 项目说明
├── clients\
│   ├── __init__.py
│   └── ifind_client.py           # 同花顺API客户端
├── tools\
│   ├── __init__.py
│   ├── base_tool.py              # 工具基类
│   ├── financial_data.py         # 金融数据工具
│   ├── technical_indicators.py   # 技术指标工具
│   └── stock_utils.py            # 股票代码处理
├── config\
│   ├── __init__.py
│   ├── settings.py               # 配置管理
│   ├── stock_mapping.py          # 股票名称映射
│   └── indicators_config.py      # 技术指标配置
├── utils\
│   ├── __init__.py
│   ├── logger.py                 # 日志工具
│   ├── validators.py             # 数据验证
│   └── formatters.py             # 数据格式化
├── tests\
│   ├── __init__.py
│   ├── test_tools.py             # 工具测试
│   ├── test_client.py            # 客户端测试
│   └── test_server.py            # 服务器测试
├── logs\                         # 日志目录
└── docs\
    ├── API.md                    # API文档
    ├── DEPLOYMENT.md             # 部署文档
    └── INTEGRATION.md            # 集成文档
```

---

## 🔧 **环境配置文件**

### **environment.yml**
```yaml
name: financial-mcp
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.11
  - fastapi=0.104.*
  - aiohttp=3.9.*
  - requests=2.31.*
  - python-dotenv=1.0.*
  - numpy=1.24.*
  - pandas=2.0.*
  - pip
  - pip:
    - fastmcp>=0.3.0
    - uvicorn[standard]>=0.24.0
    - pydantic>=2.5.0
    - orjson>=3.9.0
    - pytest>=7.4.0
    - pytest-asyncio>=0.21.0
```

### **requirements.txt**
```txt
# 核心MCP依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
fastmcp==0.3.0
pydantic==2.5.0

# HTTP客户端
aiohttp==3.9.1
requests==2.31.0

# 工具库
python-dotenv==1.0.0
orjson==3.9.10

# 数据处理（如果需要）
numpy==1.24.4
pandas==2.0.3

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
```

---

## 🚀 **环境创建和激活脚本**

### **setup_environment.bat (Windows)**
```batch
@echo off
echo 🐍 创建金融MCP服务器环境...

REM 检查conda是否可用
conda --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到conda，请先安装Anaconda或Miniconda
    pause
    exit /b 1
)

REM 创建conda环境
echo 📦 创建conda环境 financial-mcp...
conda env create -f environment.yml

if %errorlevel% neq 0 (
    echo ❌ 环境创建失败
    pause
    exit /b 1
)

echo ✅ 环境创建成功！

echo.
echo 🎯 下一步操作:
echo 1. 激活环境: conda activate financial-mcp
echo 2. 配置环境变量: 复制 .env.example 为 .env 并填写配置
echo 3. 启动服务器: python server.py
echo.
pause
```

### **setup_environment.sh (Linux/WSL)**
```bash
#!/bin/bash

echo "🐍 创建金融MCP服务器环境..."

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: 未找到conda，请先安装Anaconda或Miniconda"
    exit 1
fi

# 创建conda环境
echo "📦 创建conda环境 financial-mcp..."
conda env create -f environment.yml

if [ $? -ne 0 ]; then
    echo "❌ 环境创建失败"
    exit 1
fi

echo "✅ 环境创建成功！"
echo ""
echo "🎯 下一步操作:"
echo "1. 激活环境: conda activate financial-mcp"
echo "2. 配置环境变量: cp .env.example .env && nano .env"
echo "3. 启动服务器: python server.py"
```

---

## 🔄 **启动脚本配置**

### **start_server.bat (Windows)**
```batch
@echo off
title 金融MCP服务器

echo 🚀 启动金融MCP服务器...

REM 激活conda环境
call conda activate financial-mcp
if %errorlevel% neq 0 (
    echo ❌ 错误: 无法激活conda环境 financial-mcp
    echo 请先运行 setup_environment.bat 创建环境
    pause
    exit /b 1
)

REM 检查环境变量
if not exist .env (
    echo ⚠️  警告: 未找到.env文件，请复制.env.example并配置
    echo 继续使用默认配置...
)

REM 创建日志目录
if not exist logs mkdir logs

REM 启动服务器
echo 🌐 启动SSE服务器在端口8080...
echo 📡 SSE端点: http://localhost:8080/mcp/sse
echo 🏥 健康检查: http://localhost:8080/health
echo.
echo 按 Ctrl+C 停止服务器
echo ==========================
python server.py

pause
```

### **start_server.sh (Linux/WSL)**
```bash
#!/bin/bash

echo "🚀 启动金融MCP服务器..."

# 激活conda环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate financial-mcp

if [ $? -ne 0 ]; then
    echo "❌ 错误: 无法激活conda环境 financial-mcp"
    echo "请先运行 ./setup_environment.sh 创建环境"
    exit 1
fi

# 检查环境变量
if [ ! -f .env ]; then
    echo "⚠️  警告: 未找到.env文件，请复制.env.example并配置"
    echo "继续使用默认配置..."
fi

# 创建日志目录
mkdir -p logs

# 启动服务器
echo "🌐 启动SSE服务器在端口8080..."
echo "📡 SSE端点: http://localhost:8080/mcp/sse"
echo "🏥 健康检查: http://localhost:8080/health"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================="
python server.py
```

---

## 📋 **环境配置步骤总结**

### **1. 创建项目目录**
```bash
# 已完成: E:\AI\financial-mcp-server
```

### **2. 创建conda环境**
```bash
cd E:\AI\financial-mcp-server
conda env create -f environment.yml
```

### **3. 激活环境并验证**
```bash
conda activate financial-mcp
python --version  # 应该显示 Python 3.11.x
pip list | grep fastmcp  # 验证MCP包安装
```

### **4. 配置环境变量**
```bash
copy .env.example .env  # Windows
# 或
cp .env.example .env    # Linux
# 然后编辑.env文件，填入同花顺API配置
```

### **5. 启动服务器**
```bash
# Windows
start_server.bat

# Linux/WSL
./start_server.sh
```

---

## 🎯 **推荐的开发流程**

1. **环境准备**: 使用conda创建独立环境
2. **代码移植**: 从agent-zero_091项目移植金融工具代码
3. **测试验证**: 在独立环境中测试所有功能
4. **集成配置**: 配置agent-zero_091连接新的MCP服务器

**您希望我现在开始创建这些配置文件和项目结构吗？**
