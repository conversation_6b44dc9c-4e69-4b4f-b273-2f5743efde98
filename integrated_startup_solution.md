# 金融MCP服务器集成启动方案

## 🎯 **问题解答**

**是的，SSE方式需要启动MCP服务器作为常驻进程。** 但我们可以通过以下方案简化管理：

---

## 🚀 **推荐方案：集成启动**

### **方案1: 修改quick_start.sh（推荐）**

在现有的 `quick_start.sh` 中添加金融MCP服务器的启动和管理：

```bash
# 在cleanup函数中添加
cleanup() {
    echo ""
    echo "🛑 正在关闭所有服务..."
    
    # 停止金融MCP服务器
    pkill -f "financial_mcp_server_sse.py" 2>/dev/null && echo "✅ 金融MCP服务器已停止"
    
    # 停止SearXNG
    pkill -f "searx.webapp" 2>/dev/null && echo "✅ SearXNG已停止"
    
    # 停止Agent-Zero
    pkill -f "python run_ui.py" 2>/dev/null && echo "✅ Agent-Zero已停止"
    
    echo "👋 所有服务已停止，再见！"
    exit 0
}

# 在主启动流程中添加
echo "💰 启动金融MCP服务器..."
if ! pgrep -f "financial_mcp_server_sse.py" > /dev/null; then
    nohup python3 financial_mcp_server_sse.py > logs/financial_mcp.log 2>&1 &
    sleep 3
    
    # 检查启动状态
    if curl -s http://localhost:8080/health > /dev/null; then
        echo "✅ 金融MCP服务器启动成功 (端口8080)"
    else
        echo "❌ 金融MCP服务器启动失败"
    fi
else
    echo "✅ 金融MCP服务器已在运行"
fi
```

### **方案2: 独立启动脚本**

创建 `start_all_services.sh`：

```bash
#!/bin/bash

echo "🚀 === 启动所有服务 ==="

# 1. 启动金融MCP服务器
echo "💰 启动金融MCP服务器..."
if ! pgrep -f "financial_mcp_server_sse.py" > /dev/null; then
    cd financial-mcp-server
    nohup python3 server.py > ../logs/financial_mcp.log 2>&1 &
    cd ..
    sleep 3
    echo "✅ 金融MCP服务器已启动 (http://localhost:8080)"
else
    echo "✅ 金融MCP服务器已在运行"
fi

# 2. 启动SearXNG
echo "🔍 启动SearXNG..."
./start_searxng.sh &
sleep 5

# 3. 启动Agent-Zero
echo "🤖 启动Agent-Zero..."
python run_ui.py
```

---

## 🔧 **自动化管理方案**

### **systemd服务配置（Linux推荐）**

创建 `/etc/systemd/system/financial-mcp.service`：

```ini
[Unit]
Description=Financial MCP Server
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/financial-mcp-server
Environment=CONDA_DEFAULT_ENV=AZ091
ExecStart=/home/<USER>/miniconda3/envs/AZ091/bin/python server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl enable financial-mcp.service
sudo systemctl start financial-mcp.service
```

### **Windows服务配置**

使用 `nssm` (Non-Sucking Service Manager)：

```cmd
# 安装nssm
# 下载: https://nssm.cc/download

# 创建服务
nssm install FinancialMCP
nssm set FinancialMCP Application "C:\path\to\python.exe"
nssm set FinancialMCP AppParameters "C:\path\to\financial_mcp_server_sse.py"
nssm set FinancialMCP AppDirectory "C:\path\to\financial-mcp-server"

# 启动服务
nssm start FinancialMCP
```

---

## 📊 **启动状态检查**

### **健康检查脚本**

```bash
#!/bin/bash
# check_services.sh

echo "🔍 检查服务状态..."

# 检查金融MCP服务器
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✅ 金融MCP服务器: 运行中 (http://localhost:8080)"
else
    echo "❌ 金融MCP服务器: 未运行"
fi

# 检查SearXNG
if curl -s http://localhost:8888 > /dev/null; then
    echo "✅ SearXNG: 运行中 (http://localhost:8888)"
else
    echo "❌ SearXNG: 未运行"
fi

# 检查Agent-Zero
if curl -s http://localhost:50001 > /dev/null; then
    echo "✅ Agent-Zero: 运行中 (http://localhost:50001)"
else
    echo "❌ Agent-Zero: 未运行"
fi
```

---

## 🎯 **推荐的工作流程**

### **开发阶段**
```bash
# 1. 启动金融MCP服务器（后台）
nohup python3 financial_mcp_server_sse.py > logs/mcp.log 2>&1 &

# 2. 启动Agent-Zero
./quick_start.sh
```

### **生产阶段**
```bash
# 1. 配置systemd服务（一次性）
sudo systemctl enable financial-mcp.service

# 2. 正常启动Agent-Zero
./quick_start.sh
# MCP服务器会自动启动
```

---

## ⚡ **性能优化建议**

### **内存优化**
```python
# 在server.py中添加
import gc
import asyncio

# 定期清理内存
async def cleanup_memory():
    while True:
        await asyncio.sleep(300)  # 5分钟
        gc.collect()

# 启动清理任务
asyncio.create_task(cleanup_memory())
```

### **连接池优化**
```python
# 使用连接池减少HTTP连接开销
import aiohttp

connector = aiohttp.TCPConnector(
    limit=100,
    limit_per_host=30,
    keepalive_timeout=30
)
```

---

## 🔄 **故障恢复**

### **自动重启脚本**
```bash
#!/bin/bash
# monitor_mcp_server.sh

while true; do
    if ! curl -s http://localhost:8080/health > /dev/null; then
        echo "❌ MCP服务器异常，正在重启..."
        pkill -f "financial_mcp_server_sse.py"
        sleep 2
        nohup python3 financial_mcp_server_sse.py > logs/mcp.log 2>&1 &
        sleep 5
    fi
    sleep 30
done
```

---

## 📋 **总结**

**SSE方式的管理要点**：

1. **需要常驻进程**：是的，需要预先启动MCP服务器
2. **资源占用**：持续占用50-200MB内存
3. **性能优势**：响应速度快，支持并发
4. **管理方案**：可通过脚本、服务等方式自动化管理

**推荐做法**：
- 开发阶段：手动启动或集成到quick_start.sh
- 生产阶段：配置为系统服务自动启动
- 监控：添加健康检查和自动重启机制

您希望我实现哪种启动方案？我可以修改现有的quick_start.sh来集成MCP服务器的启动。
