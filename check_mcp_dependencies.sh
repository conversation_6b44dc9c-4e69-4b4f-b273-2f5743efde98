#!/bin/bash

# MCP服务器依赖检查脚本
# 检查conda环境是否包含所有必需的MCP服务器依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${BLUE}🔍 MCP服务器依赖检查工具${NC}"
echo -e "${BLUE}================================${NC}"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
MCP_DIR="$PROJECT_DIR/enhanced_search_mcp"

# 初始化conda
if command -v conda &> /dev/null; then
    source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
    echo -e "${GREEN}✅ conda环境初始化成功${NC}"
else
    echo -e "${RED}❌ conda未安装${NC}"
    exit 1
fi

# 检查并激活searxng环境
if conda env list | grep -q "searxng"; then
    conda activate searxng
    echo -e "${GREEN}✅ 已激活searxng环境${NC}"
else
    echo -e "${RED}❌ searxng环境不存在${NC}"
    exit 1
fi

# 定义依赖包列表
declare -A REQUIRED_PACKAGES=(
    # MCP框架
    ["fastmcp"]="MCP框架核心"
    ["fastapi"]="Web框架"
    ["uvicorn"]="ASGI服务器"
    
    # HTTP客户端
    ["aiohttp"]="异步HTTP客户端"
    ["httpx"]="现代HTTP客户端"
    
    # 配置和环境
    ["python-dotenv"]="环境变量管理"
    ["pydantic"]="数据验证"
    
    # SearXNG依赖
    ["flask"]="SearXNG Web框架"
    ["werkzeug"]="WSGI工具包"
    ["babel"]="国际化支持"
    ["lxml"]="XML/HTML解析"
    
    # 可选依赖
    ["redis"]="缓存支持(可选)"
    ["structlog"]="结构化日志(可选)"
)

# 检查函数
check_package() {
    local package=$1
    local description=$2
    
    if python -c "import $package" 2>/dev/null; then
        # 获取版本信息
        local version=$(python -c "import $package; print(getattr($package, '__version__', 'unknown'))" 2>/dev/null || echo "unknown")
        echo -e "${GREEN}✅ $package${NC} ($version) - $description"
        return 0
    else
        echo -e "${RED}❌ $package${NC} - $description"
        return 1
    fi
}

# 检查pip包管理器
echo -e "${YELLOW}📦 检查包管理器...${NC}"
if command -v pip &> /dev/null; then
    echo -e "${GREEN}✅ pip可用 ($(pip --version))${NC}"
else
    echo -e "${RED}❌ pip不可用${NC}"
    exit 1
fi

# 检查Python版本
echo -e "${YELLOW}🐍 检查Python版本...${NC}"
PYTHON_VERSION=$(python --version 2>&1)
echo -e "${GREEN}✅ $PYTHON_VERSION${NC}"

# 检查必需包
echo -e "${YELLOW}📋 检查必需依赖包...${NC}"
MISSING_PACKAGES=()
OPTIONAL_MISSING=()

for package in "${!REQUIRED_PACKAGES[@]}"; do
    description="${REQUIRED_PACKAGES[$package]}"
    
    if [[ "$description" == *"(可选)"* ]]; then
        if ! check_package "$package" "$description"; then
            OPTIONAL_MISSING+=("$package")
        fi
    else
        if ! check_package "$package" "$description"; then
            MISSING_PACKAGES+=("$package")
        fi
    fi
done

# 检查MCP服务器文件
echo -e "${YELLOW}📁 检查MCP服务器文件...${NC}"
REQUIRED_FILES=(
    "$MCP_DIR/server.py"
    "$MCP_DIR/enhanced_search_engine.py"
    "$MCP_DIR/searxng_adapter.py"
    "$MCP_DIR/config.py"
    "$MCP_DIR/requirements.txt"
)

MISSING_FILES=()
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $(basename "$file")${NC}"
    else
        echo -e "${RED}❌ $(basename "$file")${NC}"
        MISSING_FILES+=("$file")
    fi
done

# 检查端口可用性
echo -e "${YELLOW}🔌 检查端口可用性...${NC}"
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  端口 $port ($service) 已被占用${NC}"
        return 1
    else
        echo -e "${GREEN}✅ 端口 $port ($service) 可用${NC}"
        return 0
    fi
}

check_port 8888 "SearXNG"
check_port 8881 "MCP服务器"

# 生成报告
echo -e "\n${BLUE}📊 依赖检查报告${NC}"
echo -e "${BLUE}==================${NC}"

if [ ${#MISSING_PACKAGES[@]} -eq 0 ] && [ ${#MISSING_FILES[@]} -eq 0 ]; then
    echo -e "${GREEN}🎉 所有必需依赖都已满足！${NC}"
    
    if [ ${#OPTIONAL_MISSING[@]} -gt 0 ]; then
        echo -e "${YELLOW}📝 缺失的可选依赖:${NC}"
        for pkg in "${OPTIONAL_MISSING[@]}"; do
            echo -e "   • $pkg"
        done
        echo -e "${YELLOW}💡 可选依赖不影响基本功能，但建议安装以获得更好体验${NC}"
    fi
    
    echo -e "\n${GREEN}✅ MCP服务器可以正常启动${NC}"
    echo -e "${BLUE}🚀 使用以下命令启动服务:${NC}"
    echo -e "   cd /mnt/e/AI/searxng"
    echo -e "   ./scripts/start_searxng_with_mcp.sh"
    
else
    echo -e "${RED}❌ 发现缺失的依赖或文件${NC}"
    
    if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
        echo -e "\n${RED}缺失的必需包:${NC}"
        for pkg in "${MISSING_PACKAGES[@]}"; do
            echo -e "   • $pkg"
        done
        
        echo -e "\n${YELLOW}💡 安装缺失包的命令:${NC}"
        echo -e "   conda activate searxng"
        echo -e "   pip install ${MISSING_PACKAGES[*]}"
    fi
    
    if [ ${#MISSING_FILES[@]} -gt 0 ]; then
        echo -e "\n${RED}缺失的必需文件:${NC}"
        for file in "${MISSING_FILES[@]}"; do
            echo -e "   • $file"
        done
    fi
fi

# 生成安装脚本
if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
    INSTALL_SCRIPT="$PROJECT_DIR/install_missing_deps.sh"
    cat > "$INSTALL_SCRIPT" << EOF
#!/bin/bash
# 自动生成的依赖安装脚本

echo "🔧 安装缺失的MCP服务器依赖..."

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh || source ~/anaconda3/etc/profile.d/conda.sh
conda activate searxng

# 安装缺失的包
pip install ${MISSING_PACKAGES[*]}

echo "✅ 依赖安装完成"
EOF
    chmod +x "$INSTALL_SCRIPT"
    echo -e "\n${BLUE}📝 已生成自动安装脚本: $INSTALL_SCRIPT${NC}"
    echo -e "${BLUE}运行以下命令自动安装缺失依赖:${NC}"
    echo -e "   $INSTALL_SCRIPT"
fi

# 显示环境信息
echo -e "\n${BLUE}🔧 环境信息${NC}"
echo -e "${BLUE}============${NC}"
echo -e "Python版本: $PYTHON_VERSION"
echo -e "Conda环境: $(conda info --envs | grep '*' | awk '{print $1}')"
echo -e "项目目录: $PROJECT_DIR"
echo -e "MCP目录: $MCP_DIR"

# 返回状态码
if [ ${#MISSING_PACKAGES[@]} -eq 0 ] && [ ${#MISSING_FILES[@]} -eq 0 ]; then
    exit 0
else
    exit 1
fi
