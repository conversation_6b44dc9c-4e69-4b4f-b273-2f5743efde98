#!/usr/bin/env python3
"""
金融分析MCP服务器 - 兼容性修复版本
修复FastMCP版本兼容性问题，支持Docker跨环境访问
"""

# 过滤弃用警告
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="websockets")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="uvicorn")

import asyncio
import logging
import os
import sys
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import uvicorn
from fastapi import FastAPI
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入工具类
from clients.ifind_client import FinancialAPIClient
from tools.base_tool import FinancialBaseTool

# 金融关键词配置
FINANCIAL_KEYWORDS = {
    "stock_related": [
        "股票", "股价", "行情", "涨跌", "涨幅", "跌幅", "市值", "成交量", "成交额",
        "开盘价", "收盘价", "最高价", "最低价", "昨收价", "换手率", "振幅"
    ],
    "query_actions": [
        "查询股票", "查询股价", "查询行情", "查看股票", "查看股价", "查看行情",
        "获取股票", "获取股价", "获取行情", "股票查询", "股价查询", "行情查询"
    ],
    "technical_indicators": [
        "技术指标", "技术分析", "K线", "MACD", "KDJ", "RSI", "BOLL", "均线",
        "移动平均", "布林带", "相对强弱", "随机指标", "指数平滑"
    ],
    "fundamental_data": [
        "市盈率", "市净率", "PE", "PB", "ROE", "ROA", "净资产收益率",
        "每股收益", "每股净资产", "净利润", "营业收入", "毛利率", "净利率"
    ],
    "financial_reports": [
        "财务", "财报", "年报", "季报", "财务指标", "财务分析", "基本面", "估值"
    ]
}

# 全局API客户端实例
api_client = None

def get_api_client() -> FinancialAPIClient:
    """获取API客户端实例"""
    global api_client
    if api_client is None:
        api_client = FinancialAPIClient()
        logger.info("金融API客户端初始化成功")
    return api_client

# 创建工具实例
financial_tool = FinancialBaseTool("financial_data")

def calculate_financial_confidence(query: str) -> Dict[str, Any]:
    """计算金融查询置信度"""
    query_lower = query.lower()
    matched_keywords = []
    confidence = 0.0
    
    # 检查股票代码（高权重）
    stock_pattern = r'\d{6}\.(SZ|SH|sz|sh)'
    if re.search(stock_pattern, query):
        confidence += 0.5
        matched_keywords.append("股票代码")
    
    # 检查各类关键词
    for category, keywords in FINANCIAL_KEYWORDS.items():
        for keyword in keywords:
            if keyword.lower() in query_lower:
                confidence += 0.15
                matched_keywords.append(keyword)
                break
    
    # 检查股票名称
    stock_names = ["贵州茅台", "五粮液", "比亚迪", "平安银行", "招商银行", "中国平安", "宁德时代"]
    for name in stock_names:
        if name in query:
            confidence += 0.3
            matched_keywords.append(name)
    
    confidence = min(confidence, 1.0)
    
    return {
        "confidence": confidence,
        "matched_keywords": matched_keywords,
        "recommended": confidence >= 0.6,
        "query_type": financial_tool.detect_query_type(query) if confidence >= 0.6 else "unknown"
    }

# 创建FastAPI应用
app = FastAPI(
    title="Financial MCP Server", 
    version="1.0.0",
    description="专业金融数据分析MCP服务器"
)

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        client = get_api_client()
        health_status = client.health_check()
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "port": 8880,
            "api_status": health_status,
            "tools": [
                "get_stock_real_time",
                "get_stock_history", 
                "get_stock_basic_data",
                "get_financial_report",
                "analyze_technical_indicators",
                "financial_smart_query",
                "analyze_query_intent"
            ]
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@app.post("/mcp/tools/get_stock_real_time")
async def get_stock_real_time(request: dict):
    """获取股票实时行情数据"""
    try:
        codes = request.get("codes", "")
        indicators = request.get("indicators", "latest,preClose,change,changeRatio,volume,amount")
        
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return {"error": f"参数错误: {error_msg}"}
        
        financial_tool.log_execution("实时行情查询", {"codes": codes, "indicators": indicators})
        
        client = get_api_client()
        result = await client.get_real_time_quotation(codes, indicators)
        
        return {"result": financial_tool.format_financial_data(result, "real_time")}
        
    except Exception as e:
        return {"error": financial_tool.handle_error(e)}

@app.post("/mcp/tools/get_stock_history")
async def get_stock_history(request: dict):
    """获取股票历史行情数据"""
    try:
        codes = request.get("codes", "")
        start_date = request.get("start_date", "")
        end_date = request.get("end_date", "")
        indicators = request.get("indicators", "preClose,open,high,low,close,volume,changeRatio")
        
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return {"error": f"股票代码错误: {error_msg}"}
        
        is_valid, error_msg = financial_tool.validate_date_range(start_date, end_date)
        if not is_valid:
            return {"error": f"日期范围错误: {error_msg}"}
        
        financial_tool.log_execution("历史数据查询", {
            "codes": codes, "start_date": start_date, "end_date": end_date
        })
        
        client = get_api_client()
        result = await client.get_history_quotation(codes, start_date, end_date, indicators)
        
        return {"result": financial_tool.format_financial_data(result, "history")}
        
    except Exception as e:
        return {"error": financial_tool.handle_error(e)}

@app.post("/mcp/tools/get_stock_basic_data")
async def get_stock_basic_data(request: dict):
    """获取股票基础数据"""
    try:
        codes = request.get("codes", "")
        indicators = request.get("indicators", "totalShares,totalCapital,mv,pb,pe_ttm")
        
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return {"error": f"参数错误: {error_msg}"}
        
        financial_tool.log_execution("基础数据查询", {"codes": codes, "indicators": indicators})
        
        client = get_api_client()
        result = await client.get_basic_data(codes, indicators)
        
        return {"result": financial_tool.format_financial_data(result, "basic")}
        
    except Exception as e:
        return {"error": financial_tool.handle_error(e)}

@app.post("/mcp/tools/analyze_technical_indicators")
async def analyze_technical_indicators(request: dict):
    """技术指标分析"""
    try:
        query = request.get("query", "")
        codes = request.get("codes", "")
        indicators = request.get("indicators", "MACD,RSI,KDJ")
        period = request.get("period", "1M")
        
        if query and not codes:
            codes = financial_tool.extract_stock_codes(query)
        
        if not codes:
            return {"error": "未识别到有效的股票代码，请提供股票代码（如600519.SH）或股票名称"}
        
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return {"error": f"股票代码错误: {error_msg}"}
        
        # 计算时间范围
        end_date = datetime.now()
        if period == "1M":
            start_date = end_date - timedelta(days=30)
        elif period == "3M":
            start_date = end_date - timedelta(days=90)
        elif period == "6M":
            start_date = end_date - timedelta(days=180)
        elif period == "1Y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)
        
        financial_tool.log_execution("技术指标分析", {
            "query": query, "codes": codes, "indicators": indicators, "period": period
        })
        
        client = get_api_client()
        result = await client.get_technical_indicators(
            codes=codes,
            starttime=start_date.strftime("%Y-%m-%d"),
            endtime=end_date.strftime("%Y-%m-%d"),
            indicators=indicators
        )
        
        return {"result": financial_tool.format_financial_data(result, "technical_indicators")}
        
    except Exception as e:
        return {"error": financial_tool.handle_error(e)}

@app.post("/mcp/tools/financial_smart_query")
async def financial_smart_query(request: dict):
    """智能金融查询"""
    try:
        query = request.get("query", "")
        query_type = request.get("query_type", "auto")
        
        analysis = calculate_financial_confidence(query)
        
        if not analysis["recommended"]:
            confidence_pct = analysis["confidence"] * 100
            return {
                "result": f"""
🔍 查询意图分析结果：
   置信度: {confidence_pct:.1f}% (低于推荐阈值60%)
   
   💡 如需金融数据查询，请尝试包含：
   • 股票名称或代码（如：贵州茅台、600519.SH）
   • 查询动作（如：查询、分析、获取）
   • 数据类型（如：行情、技术指标、财务数据）
"""
            }
        
        codes = financial_tool.extract_stock_codes(query)
        if not codes:
            return {"error": "未识别到有效的股票代码，请提供股票代码或股票名称"}
        
        if query_type == "auto":
            query_type = analysis["query_type"]
        
        financial_tool.log_execution("智能查询", {
            "query": query, "detected_type": query_type, "codes": codes
        })
        
        # 根据查询类型调用相应功能
        if query_type == "technical_indicators":
            return await analyze_technical_indicators({
                "query": query, "codes": codes
            })
        elif query_type == "history":
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            return await get_stock_history({
                "codes": codes, "start_date": start_date, "end_date": end_date
            })
        elif query_type == "financial_report":
            return await get_stock_basic_data({"codes": codes})
        else:
            return await get_stock_real_time({"codes": codes})
            
    except Exception as e:
        return {"error": financial_tool.handle_error(e)}

@app.get("/mcp/tools")
async def list_tools():
    """列出所有可用工具"""
    return {
        "tools": [
            {
                "name": "get_stock_real_time",
                "description": "获取股票实时行情数据",
                "parameters": ["codes", "indicators"]
            },
            {
                "name": "get_stock_history", 
                "description": "获取股票历史行情数据",
                "parameters": ["codes", "start_date", "end_date", "indicators"]
            },
            {
                "name": "get_stock_basic_data",
                "description": "获取股票基础数据",
                "parameters": ["codes", "indicators"]
            },
            {
                "name": "analyze_technical_indicators",
                "description": "技术指标分析",
                "parameters": ["query", "codes", "indicators", "period"]
            },
            {
                "name": "financial_smart_query",
                "description": "智能金融查询",
                "parameters": ["query", "query_type"]
            }
        ]
    }

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("IFIND_REFRESH_TOKEN"):
        logger.warning("未设置IFIND_REFRESH_TOKEN环境变量，请在.env文件中配置")
    
    logger.info("🚀 启动金融分析MCP服务器（兼容性修复版）...")
    logger.info("📡 HTTP API端点: http://0.0.0.0:8880/mcp/tools/")
    logger.info("🏥 健康检查: http://0.0.0.0:8880/health")
    logger.info("🔧 支持Docker跨环境访问")
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8880,
        log_level="info"
    )
