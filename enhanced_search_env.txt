# Enhanced Search Engine MCP Server Environment Configuration

# 服务器配置
MCP_HOST=0.0.0.0
MCP_PORT=8881

# SearXNG配置
SEARXNG_URL=http://localhost:8888
SEARXNG_TIMEOUT=30
SEARXNG_MAX_RETRIES=3

# 缓存配置 (可选)
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
ENABLE_CACHE=true
CACHE_PREFIX=enhanced_search

# 搜索配置
MAX_CONCURRENT_SEARCHES=5
DEFAULT_ENGINES=google,bing,brave
MAX_RESULTS_PER_QUERY=10
MAX_TOTAL_RESULTS=20

# 搜索策略配置
ENABLE_EXPANDED_SEARCH=true
ENABLE_RELATED_SEARCH=true
EXPANDED_QUERIES_LIMIT=3
RELATED_QUERIES_LIMIT=2

# 质量评分权重配置
TITLE_RELEVANCE_WEIGHT=0.3
CONTENT_QUALITY_WEIGHT=0.25
SOURCE_AUTHORITY_WEIGHT=0.2
FRESHNESS_WEIGHT=0.15
ENGAGEMENT_WEIGHT=0.1

# 关键词配置
DEPTH_KEYWORDS=详细介绍,深入分析,全面解析,研究报告
TIME_KEYWORDS=最新,2024,2025,趋势
TYPE_KEYWORDS=原理,方法,技术,应用
COMPARE_KEYWORDS=对比,比较,优缺点,差异
PRACTICE_KEYWORDS=实践,案例,示例,教程

# 质量关键词
QUALITY_KEYWORDS=详细,全面,深入,研究,分析,完整,系统,专业,权威,介绍,指南,教程,方法,技术,原理,应用,发展,趋势,detailed,comprehensive,complete,guide,tutorial,analysis,research,study,review,overview,introduction,technical,professional,official,latest,update

# 权威域名
AUTHORITY_DOMAINS=wikipedia.org,github.com,stackoverflow.com,medium.com,arxiv.org,ieee.org,acm.org,nature.com,science.org,edu,gov,org

# 低质量指标
LOW_QUALITY_INDICATORS=广告,推广,购买,下载,免费,优惠,促销,ad,ads,advertisement,promotion,buy,sale,download,free,discount,offer

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/enhanced_search_mcp.log
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=8882
