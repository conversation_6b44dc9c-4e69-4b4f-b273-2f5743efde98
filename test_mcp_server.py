#!/usr/bin/env python3
"""
金融MCP服务器测试脚本
验证服务器基本功能和依赖是否正常
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, '/mnt/e/AI/financial-mcp-server')

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        import fastmcp
        print("✅ FastMCP导入成功")
    except ImportError as e:
        print(f"❌ FastMCP导入失败: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn导入成功")
    except ImportError as e:
        print(f"❌ Uvicorn导入失败: {e}")
        return False
    
    try:
        from fastapi import FastAPI
        print("✅ FastAPI导入成功")
    except ImportError as e:
        print(f"❌ FastAPI导入失败: {e}")
        return False
    
    try:
        import requests
        print("✅ Requests导入成功")
    except ImportError as e:
        print(f"❌ Requests导入失败: {e}")
        return False
    
    return True

def test_project_structure():
    """测试项目结构"""
    print("\n📁 测试项目结构...")
    
    base_path = "/mnt/e/AI/financial-mcp-server"
    required_files = [
        "server.py",
        "clients/ifind_client.py",
        "tools/base_tool.py",
        "environment.yml",
        "requirements.txt",
        ".env.example"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def test_api_client():
    """测试API客户端"""
    print("\n🔌 测试API客户端...")
    
    try:
        from clients.ifind_client import FinancialAPIClient
        client = FinancialAPIClient()
        print("✅ FinancialAPIClient创建成功")
        
        # 测试健康检查（不需要真实token）
        health = client.health_check()
        print(f"✅ 健康检查返回: {health['status']}")
        
        return True
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        return False

def test_base_tool():
    """测试工具基类"""
    print("\n🔧 测试工具基类...")
    
    try:
        from tools.base_tool import FinancialBaseTool
        tool = FinancialBaseTool("test_tool")
        print("✅ FinancialBaseTool创建成功")
        
        # 测试股票代码验证
        is_valid, msg = tool.validate_stock_codes("600519.SH")
        if is_valid:
            print("✅ 股票代码验证功能正常")
        else:
            print(f"❌ 股票代码验证失败: {msg}")
            return False
        
        # 测试股票代码提取
        codes = tool.extract_stock_codes("查询贵州茅台的行情")
        if codes:
            print(f"✅ 股票代码提取功能正常: {codes}")
        else:
            print("⚠️  股票代码提取未找到结果（正常，因为映射有限）")
        
        return True
    except Exception as e:
        print(f"❌ 工具基类测试失败: {e}")
        return False

async def test_mcp_server():
    """测试MCP服务器创建"""
    print("\n🚀 测试MCP服务器...")
    
    try:
        from fastmcp import FastMCP
        
        # 创建简单的MCP服务器
        mcp_server = FastMCP(
            name="Test Financial MCP Server",
            instructions="测试服务器"
        )
        
        @mcp_server.tool()
        async def test_tool(message: str = "Hello") -> str:
            """测试工具"""
            return f"测试响应: {message}"
        
        print("✅ MCP服务器创建成功")
        print("✅ 测试工具注册成功")
        
        return True
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 金融MCP服务器测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("导入测试", test_imports),
        ("项目结构测试", test_project_structure),
        ("API客户端测试", test_api_client),
        ("工具基类测试", test_base_tool),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 异步测试
    print(f"\n🔬 运行 MCP服务器测试...")
    try:
        if asyncio.run(test_mcp_server()):
            passed += 1
            total += 1
            print(f"✅ MCP服务器测试 通过")
        else:
            total += 1
            print(f"❌ MCP服务器测试 失败")
    except Exception as e:
        total += 1
        print(f"❌ MCP服务器测试 异常: {e}")
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务器准备就绪")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
