#!/usr/bin/env python3
"""
金融分析MCP服务器 - 工作版本
确保所有功能正常工作的稳定版本
"""

import asyncio
import logging
import os
import sys
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import uvicorn
from fastapi import FastAPI
from fastmcp import FastMCP
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入工具类
from clients.ifind_client import FinancialAPIClient
from tools.base_tool import FinancialBaseTool

# 创建FastMCP服务器实例
mcp_server = FastMCP(
    name="Financial Analysis MCP Server",
    instructions="""
    专业金融数据分析MCP服务器，提供以下功能：
    
    🏦 核心功能：
    - 实时股票行情查询
    - 历史数据分析
    - 技术指标计算（MACD、RSI、KDJ等50+指标）
    - VWAP（成交量加权平均价）分析
    - 多周期移动平均线（支持任意周期）
    - 财务报表数据
    - 基础数据查询
    
    📊 支持的技术指标：
    - 趋势类：MACD、MA(任意周期)、EXPMA、DMA、TRIX
    - 震荡类：RSI、KDJ、CCI、WR、ROC
    - 成交量：OBV、VR、VRSI、VMACD、VMA、VOSC、VSTD
    - 支撑阻力：BOLL、CDP、MIKE
    - 波动率：ATR、STD、BIAS
    
    🎯 智能识别：
    - 自动识别股票代码和名称
    - 支持自然语言查询
    - 智能查询类型检测
    
    数据源：同花顺iFinD专业金融数据
    """
)

# 全局API客户端实例
api_client = None

def get_api_client() -> FinancialAPIClient:
    """获取API客户端实例"""
    global api_client
    if api_client is None:
        api_client = FinancialAPIClient()
        logger.info("金融API客户端初始化成功")
    return api_client

# 创建工具实例
financial_tool = FinancialBaseTool("financial_data")

def extract_ma_periods(query: str) -> List[str]:
    """从查询中提取MA周期"""
    periods = []
    
    # 匹配数字+日/天的模式
    patterns = [
        r'(\d+)日[均移动平均线]',
        r'(\d+)天[均移动平均线]',
        r'MA(\d+)',
        r'ma(\d+)',
        r'(\d+)日MA',
        r'(\d+)天MA'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        periods.extend(matches)
    
    # 去重并排序
    periods = list(set(periods))
    periods.sort(key=int)
    
    return periods

@mcp_server.tool()
async def get_stock_real_time(
    codes: str,
    indicators: str = "latest,preClose,change,changeRatio,volume,amount"
) -> str:
    """
    获取股票实时行情数据
    
    Args:
        codes: 股票代码，支持多个代码用逗号分隔，如 "600519.SH,000858.SZ"
        indicators: 查询指标，如 "latest,preClose,change,changeRatio,volume"
    
    Returns:
        JSON格式的实时行情数据
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("实时行情查询", {"codes": codes, "indicators": indicators})
        
        # 调用API
        client = get_api_client()
        result = await client.get_real_time_quotation(codes, indicators)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "real_time")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_stock_history(
    codes: str,
    start_date: str,
    end_date: str,
    indicators: str = "preClose,open,high,low,close,volume,changeRatio"
) -> str:
    """
    获取股票历史行情数据
    
    Args:
        codes: 股票代码，如 "600519.SH"
        start_date: 开始日期，格式 YYYY-MM-DD
        end_date: 结束日期，格式 YYYY-MM-DD
        indicators: 查询指标
    
    Returns:
        历史行情数据
    """
    try:
        # 验证参数
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"
        
        is_valid, error_msg = financial_tool.validate_date_range(start_date, end_date)
        if not is_valid:
            return f"❌ 日期范围错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("历史数据查询", {
            "codes": codes, 
            "start_date": start_date, 
            "end_date": end_date
        })
        
        # 调用API
        client = get_api_client()
        result = await client.get_history_quotation(codes, start_date, end_date, indicators)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "history")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_stock_basic_data(
    codes: str,
    indicators: str = "totalShares,totalCapital,mv,pb,pe_ttm"
) -> str:
    """
    获取股票基础数据
    
    Args:
        codes: 股票代码，如 "600519.SH"
        indicators: 查询指标
    
    Returns:
        基础数据信息
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("基础数据查询", {"codes": codes, "indicators": indicators})
        
        # 调用API
        client = get_api_client()
        result = await client.get_basic_data(codes, indicators)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "basic")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_financial_report(
    codes: str,
    report_type: str = "",
    period: str = ""
) -> str:
    """
    获取财务报表数据
    
    Args:
        codes: 股票代码，如 "600519.SH"
        report_type: 报表类型
        period: 报告期间
    
    Returns:
        财务报表数据
    """
    try:
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        # 记录执行日志
        financial_tool.log_execution("财务报表查询", {
            "codes": codes, 
            "report_type": report_type, 
            "period": period
        })
        
        # 调用API
        client = get_api_client()
        result = await client.get_financial_report(codes, report_type, period)
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "financial_report")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def analyze_technical_indicators(
    query: str = "",
    codes: str = "",
    indicators: str = "MACD,RSI,KDJ",
    period: str = "1M"
) -> str:
    """
    技术指标分析
    
    Args:
        query: 自然语言查询，如 "分析贵州茅台的MACD指标"
        codes: 股票代码，如 "600519.SH"
        indicators: 技术指标，如 "MACD,RSI,KDJ"
        period: 分析周期，如 "1M"（1个月）
    
    Returns:
        技术指标分析结果
    """
    try:
        # 如果提供了query，从中提取股票代码
        if query and not codes:
            codes = financial_tool.extract_stock_codes(query)
        
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码（如600519.SH）或股票名称"
        
        # 验证股票代码
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"
        
        # 检查多周期MA
        if query:
            ma_periods = extract_ma_periods(query)
            if ma_periods:
                ma_indicators = [f"MA{period}" for period in ma_periods]
                other_indicators = [ind for ind in indicators.split(',') if not ind.strip().upper().startswith('MA')]
                indicators = ','.join(other_indicators + ma_indicators)
        
        # 计算时间范围
        end_date = datetime.now()
        if period == "1M":
            start_date = end_date - timedelta(days=30)
        elif period == "3M":
            start_date = end_date - timedelta(days=90)
        elif period == "6M":
            start_date = end_date - timedelta(days=180)
        elif period == "1Y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)
        
        # 记录执行日志
        financial_tool.log_execution("技术指标分析", {
            "query": query,
            "codes": codes, 
            "indicators": indicators, 
            "period": period
        })
        
        # 调用API
        client = get_api_client()
        result = await client.get_technical_indicators(
            codes=codes,
            starttime=start_date.strftime("%Y-%m-%d"),
            endtime=end_date.strftime("%Y-%m-%d"),
            indicators=indicators
        )
        
        # 格式化返回结果
        return financial_tool.format_financial_data(result, "technical_indicators")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def financial_smart_query(
    query: str,
    query_type: str = "auto"
) -> str:
    """
    智能金融查询
    
    Args:
        query: 自然语言查询，如 "查询贵州茅台的实时行情"
        query_type: 查询类型，auto为自动检测
    
    Returns:
        查询结果
    """
    try:
        # 提取股票代码
        codes = financial_tool.extract_stock_codes(query)
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码或股票名称"
        
        # 检查VWAP查询
        if 'VWAP' in query.upper() or '成交量加权平均价' in query:
            return await get_stock_real_time(codes=codes, indicators="latest,volume,amount,vwap")
        
        # 自动检测查询类型
        if query_type == "auto":
            query_type = financial_tool.detect_query_type(query)
        
        # 记录执行日志
        financial_tool.log_execution("智能查询", {
            "query": query,
            "detected_type": query_type,
            "codes": codes
        })
        
        # 根据查询类型调用相应功能
        if query_type == "technical_indicators":
            return await analyze_technical_indicators(query=query, codes=codes)
        elif query_type == "history":
            # 默认查询最近1个月的历史数据
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            return await get_stock_history(codes=codes, start_date=start_date, end_date=end_date)
        elif query_type == "financial_report":
            return await get_financial_report(codes=codes)
        else:
            # 默认返回实时行情
            return await get_stock_real_time(codes=codes)
            
    except Exception as e:
        return financial_tool.handle_error(e)

# 创建FastAPI应用
app = FastAPI(title="Financial MCP Server", version="1.0.0")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        client = get_api_client()
        health_status = client.health_check()
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "api_status": health_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

# 集成MCP服务器到FastAPI
app.mount("/mcp", mcp_server.sse_app)

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("IFIND_REFRESH_TOKEN"):
        logger.warning("未设置IFIND_REFRESH_TOKEN环境变量，请在.env文件中配置")

    logger.info("🚀 启动金融分析MCP服务器...")
    logger.info("📡 SSE端点: http://localhost:8080/mcp/sse")
    logger.info("🏥 健康检查: http://localhost:8080/health")

    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
