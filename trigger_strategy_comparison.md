# 金融工具触发策略和使用方式对比分析

## 📊 **原项目 vs MCP服务器对比**

### **🎯 触发策略对比**

| 方面 | 原项目 (Agent-Zero) | MCP服务器 | 一致性 |
|------|---------------------|-----------|--------|
| **触发方式** | tool_selector.py自动推荐 | MCP客户端调用 | ❌ 不同 |
| **关键词识别** | 44个金融关键词 | 依赖MCP客户端 | ⚠️ 需配置 |
| **股票代码识别** | 自动识别6位+.SZ/SH | 完全一致 | ✅ 一致 |
| **查询类型检测** | 自动检测4种类型 | 完全一致 | ✅ 一致 |
| **工具推荐** | 智能推荐+置信度 | 无自动推荐 | ❌ 缺失 |

### **🔑 原项目触发关键词清单**

#### **股票相关关键词**
```
"股票", "股价", "行情", "涨跌", "涨幅", "跌幅", "市值", "成交量", "成交额",
"开盘价", "收盘价", "最高价", "最低价", "昨收价", "换手率", "振幅"
```

#### **查询动作词**
```
"查询股票", "查询股价", "查询行情", "查看股票", "查看股价", "查看行情",
"获取股票", "获取股价", "获取行情", "股票查询", "股价查询", "行情查询",
"查看证券", "查询证券", "获取证券", "分析股票", "分析证券", "分析行情"
```

#### **技术指标关键词**
```
"技术指标", "技术分析", "K线", "MACD", "KDJ", "RSI", "BOLL", "均线",
"移动平均", "布林带", "相对强弱", "随机指标", "指数平滑"
```

#### **基本面指标**
```
"市盈率", "市净率", "PE", "PB", "ROE", "ROA", "净资产收益率",
"每股收益", "每股净资产", "净利润", "营业收入", "毛利率", "净利率"
```

#### **财务数据关键词**
```
"财务", "财报", "年报", "季报", "财务指标", "财务分析", "基本面", "估值",
"总股本", "流通股", "总市值", "流通市值", "股东权益", "负债率"
```

### **🤖 原项目智能推荐机制**

#### **推荐触发条件**
- **置信度阈值**: 0.6 (60%)
- **关键词匹配**: 直接匹配 +0.3分，词根匹配 +0.2分
- **多关键词加权**: 每增加一个关键词 +0.2分
- **股票代码检测**: 包含股票代码时置信度提升至0.9

#### **推荐消息格式**
```
💡 检测到您可能需要专业金融数据（置信度: 85.0%）
   触发关键词: 股票, 行情, MACD

   建议使用 financial_data_tool 工具，它可以提供：
   • 实时股票行情数据
   • 历史价格和技术指标
   • 财务指标和基本面数据
   • 专业投资分析支持
```

### **🔧 MCP服务器当前状态**

#### **优势保持**
- ✅ **核心功能完整**: 所有原有查询功能100%移植
- ✅ **股票代码识别**: 完全一致的识别逻辑
- ✅ **查询类型检测**: 保持原有的智能检测
- ✅ **数据格式化**: 保持专业的数据展示格式
- ✅ **错误处理**: 完整的错误处理机制

#### **缺失的触发机制**
- ❌ **自动工具推荐**: 无tool_selector.py等价机制
- ❌ **关键词触发**: 依赖MCP客户端配置
- ❌ **置信度评估**: 无智能推荐置信度
- ❌ **用户引导**: 无主动的工具使用建议

---

## 🔧 **解决方案：保持触发策略一致性**

### **方案1: MCP客户端配置关键词**

在Agent-Zero的MCP配置中添加完整的触发关键词：

```json
{
  "mcpServers": {
    "financial-analysis": {
      "type": "sse",
      "url": "http://localhost:8080/mcp/sse",
      "description": "专业金融数据分析MCP服务器",
      "enabled": true,
      "keywords": [
        "股票", "股价", "行情", "涨跌", "涨幅", "跌幅", "市值", "成交量", "成交额",
        "开盘价", "收盘价", "最高价", "最低价", "昨收价", "换手率", "振幅",
        "查询股票", "查询股价", "查询行情", "查看股票", "查看股价", "查看行情",
        "获取股票", "获取股价", "获取行情", "股票查询", "股价查询", "行情查询",
        "查看证券", "查询证券", "获取证券", "分析股票", "分析证券", "分析行情",
        "监控股票", "监控行情", "跟踪股票", "跟踪行情", "了解股票", "关注股票",
        "查看财务", "查询财务", "获取财务", "分析财务", "股票分析", "证券分析",
        "技术指标", "技术分析", "K线", "MACD", "KDJ", "RSI", "BOLL", "均线",
        "移动平均", "布林带", "相对强弱", "随机指标", "指数平滑",
        "市盈率", "市净率", "PE", "PB", "ROE", "ROA", "净资产收益率", "资产收益率",
        "每股收益", "每股净资产", "净利润", "营业收入", "毛利率", "净利率",
        "财务", "财报", "年报", "季报", "财务指标", "财务分析", "基本面", "估值",
        "总股本", "流通股", "总市值", "流通市值", "股东权益", "负债率",
        "资产负债表", "利润表", "现金流量表", "所有者权益", "营业利润",
        "VWAP", "成交量加权平均价", "OBV", "VR", "VRSI", "VMACD", "VMA", "VOSC", "VSTD",
        "贵州茅台", "五粮液", "比亚迪", "平安银行", "山西汾酒", "招商银行",
        "中国平安", "宁德时代", "腾讯控股", "阿里巴巴", "茅台", "平安", "招行"
      ],
      "trigger_patterns": [
        "\\d{6}\\.(SZ|SH|sz|sh)",
        "\\d{5}\\.(HK|hk)"
      ]
    }
  }
}
```

### **方案2: 增强MCP服务器智能路由**

在MCP服务器中添加智能工具推荐功能：

```python
@mcp_server.tool()
async def analyze_query_intent(query: str) -> str:
    """
    分析查询意图并推荐最佳工具
    
    Args:
        query: 用户查询文本
    
    Returns:
        查询意图分析和工具推荐
    """
    # 实现与原项目tool_selector.py相同的逻辑
    financial_keywords = [
        "股票", "股价", "行情", "涨跌", "涨幅", "跌幅", "市值", "成交量",
        "技术指标", "MACD", "KDJ", "RSI", "财务", "财报"
    ]
    
    score = calculate_keyword_score(query, financial_keywords)
    
    if score >= 0.6:
        return f"""
💡 检测到金融数据查询需求（置信度: {score:.1%}）

建议使用以下MCP工具：
• financial_smart_query - 智能金融查询
• get_stock_real_time - 实时行情
• analyze_technical_indicators - 技术指标分析

您可以直接说："查询贵州茅台的实时行情"
"""
    else:
        return "未检测到明确的金融查询意图"
```

### **方案3: 保持原有tool_selector.py**

在Agent-Zero中保留原有的tool_selector.py，但修改推荐逻辑：

```python
# 在tool_selector.py中添加MCP服务器推荐
if analysis['financial_data_tool']['recommended']:
    message = f"""
💡 检测到您可能需要专业金融数据（置信度: {confidence:.1%}）
   触发关键词: {', '.join(keywords)}

   建议使用 MCP金融分析服务器，它可以提供：
   • 实时股票行情数据
   • 历史价格和技术指标  
   • 财务指标和基本面数据
   • 专业投资分析支持
   
   🔗 MCP服务器已自动连接，直接询问即可使用
"""
```

---

## 🎯 **推荐实施策略**

### **立即实施 (高优先级)**

1. **配置MCP关键词**: 在Agent-Zero中配置完整的金融关键词列表
2. **保持stock_mapping**: 确保股票名称映射与原项目一致
3. **测试触发响应**: 验证关键词触发是否正常工作

### **后续优化 (中优先级)**

1. **添加智能推荐工具**: 在MCP服务器中实现query_intent_analyzer
2. **增强用户引导**: 提供更好的工具使用指导
3. **统计使用情况**: 监控工具调用频率和成功率

### **长期改进 (低优先级)**

1. **机器学习优化**: 基于使用数据优化关键词权重
2. **个性化推荐**: 根据用户历史偏好调整推荐策略
3. **多语言支持**: 支持英文金融术语触发

---

## ✅ **结论**

**触发策略一致性评估**: ⚠️ **部分一致，需要配置优化**

- **核心功能**: ✅ 100%一致
- **识别逻辑**: ✅ 完全一致  
- **触发机制**: ❌ 需要MCP客户端配置
- **用户体验**: ⚠️ 需要手动配置关键词

**建议**: 立即实施方案1（MCP客户端关键词配置），确保用户体验与原项目保持一致。
