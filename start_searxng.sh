#!/bin/bash

# SearXNG 独立启动脚本
# 用于在WSL环境中启动SearXNG服务，包括conda虚拟环境激活

echo "🔍 === SearXNG 独立启动脚本 ==="
echo ""

# 配置变量
SEARXNG_PATH="/mnt/e/AI/searxng"
SEARXNG_WEBAPP="$SEARXNG_PATH/searx/webapp.py"
CONDA_ENV_NAME="searxng"
SEARXNG_PORT="8888"  # 匹配Agent-Zero配置
SEARXNG_HOST="127.0.0.1"
LOG_FILE="/tmp/searxng_standalone.log"

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在关闭SearXNG服务..."
    
    # 停止SearXNG进程
    pkill -f "searx.webapp" 2>/dev/null && echo "✅ SearXNG已停止"
    
    # 清理后台任务
    jobs -p | xargs -r kill 2>/dev/null
    
    echo "👋 SearXNG服务已停止，再见！"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 第一步：环境检查
echo "🔍 第一步：检查环境..."

# 检查conda
source ~/.bashrc 2>/dev/null || true
if ! command -v conda &> /dev/null; then
    echo "❌ 错误：未找到conda，请先安装conda"
    exit 1
fi

# 检查searxng conda环境
if ! conda env list | grep -q "$CONDA_ENV_NAME"; then
    echo "❌ 错误：未找到 $CONDA_ENV_NAME conda环境"
    echo "请先创建：conda create -n $CONDA_ENV_NAME python=3.11 -y"
    echo "然后安装依赖：conda activate $CONDA_ENV_NAME && pip install -r $SEARXNG_PATH/requirements.txt"
    exit 1
fi

# 检查SearXNG源码
if [ ! -d "$SEARXNG_PATH" ]; then
    echo "❌ 错误：未找到SearXNG源码目录：$SEARXNG_PATH"
    exit 1
fi

if [ ! -f "$SEARXNG_WEBAPP" ]; then
    echo "❌ 错误：未找到SearXNG启动文件：$SEARXNG_WEBAPP"
    exit 1
fi

echo "✅ 环境检查完成"

# 第二步：清理现有进程
echo ""
echo "🧹 第二步：清理现有SearXNG进程..."
pkill -f "searx.webapp" 2>/dev/null && echo "✅ 已清理现有SearXNG进程"

# 第三步：启动SearXNG
echo ""
echo "🚀 第三步：启动SearXNG服务..."

# 检查端口是否被占用
if netstat -tuln 2>/dev/null | grep -q ":$SEARXNG_PORT "; then
    echo "⚠️  警告：端口 $SEARXNG_PORT 已被占用"
    echo "正在尝试清理..."
    lsof -ti:$SEARXNG_PORT | xargs -r kill -9 2>/dev/null
    sleep 2
fi

echo "🔄 正在启动SearXNG..."
echo "📝 日志文件: $LOG_FILE"

# 启动SearXNG（默认后台运行，可选择前台运行）
if [ "$1" = "--foreground" ] || [ "$1" = "-f" ]; then
    echo "🔄 前台模式启动..."
    echo "📝 按 Ctrl+C 停止服务"
    echo ""

    # 前台启动SearXNG
    bash -c "
        source ~/.bashrc
        conda activate $CONDA_ENV_NAME
        cd '$SEARXNG_PATH'
        export SEARXNG_SECRET=\"my-super-secret-key-\$(date +%s)\"
        export SEARXNG_BIND_ADDRESS=\"$SEARXNG_HOST\"
        export SEARXNG_PORT=\"$SEARXNG_PORT\"
        echo '🌐 SearXNG服务启动中...'
        echo '   🔍 访问地址: http://$SEARXNG_HOST:$SEARXNG_PORT'
        echo '   📝 按 Ctrl+C 停止服务'
        echo ''
        python -m searx.webapp
    " 2>&1 | tee "$LOG_FILE"

    # 如果到达这里，说明SearXNG被停止了
    cleanup

else
    echo "🔄 后台模式启动（默认）..."

    # 后台启动SearXNG
    nohup bash -c "
        source ~/.bashrc
        conda activate $CONDA_ENV_NAME
        cd '$SEARXNG_PATH'
        export SEARXNG_SECRET=\"my-super-secret-key-\$(date +%s)\"
        export SEARXNG_BIND_ADDRESS=\"$SEARXNG_HOST\"
        export SEARXNG_PORT=\"$SEARXNG_PORT\"
        python -m searx.webapp
    " > "$LOG_FILE" 2>&1 &

    SEARXNG_PID=$!
    echo "✅ SearXNG已在后台启动，PID: $SEARXNG_PID"

    # 等待SearXNG启动
    echo "⏳ 等待SearXNG初始化..."
    for i in {1..30}; do
        if curl -s "http://$SEARXNG_HOST:$SEARXNG_PORT" > /dev/null 2>&1; then
            echo "✅ SearXNG运行正常：http://$SEARXNG_HOST:$SEARXNG_PORT"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ SearXNG启动超时"
            echo "📝 检查日志：tail -f $LOG_FILE"
            exit 1
        fi
        sleep 1
    done

    echo ""
    echo "🌐 SearXNG服务信息："
    echo "   🔍 访问地址: http://$SEARXNG_HOST:$SEARXNG_PORT"
    echo "   📝 日志文件: $LOG_FILE"
    echo "   🔧 进程ID: $SEARXNG_PID"
    echo ""
    echo "📝 查看日志: tail -f $LOG_FILE"
    echo "📝 停止服务: pkill -f 'searx.webapp' 或者 kill $SEARXNG_PID"
    echo "📝 前台运行: $0 --foreground"
    echo ""
fi
