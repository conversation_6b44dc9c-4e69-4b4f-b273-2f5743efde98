#!/usr/bin/env python3
"""
金融数据源健康检查脚本
检查同花顺iFinD API的连接状态和Token有效性
"""

import sys
import os
import asyncio
import argparse
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '.')

try:
    from python.helpers.financial_api_client import FinancialAPIClient
    from python.helpers import dotenv
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class FinancialDataSourceChecker:
    """金融数据源检查器"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.client = None
    
    def log(self, message, level="info"):
        """日志输出"""
        if self.verbose or level == "error":
            timestamp = datetime.now().strftime("%H:%M:%S")
            if level == "error":
                print(f"[{timestamp}] ❌ {message}")
            elif level == "warning":
                print(f"[{timestamp}] ⚠️  {message}")
            elif level == "success":
                print(f"[{timestamp}] ✅ {message}")
            else:
                print(f"[{timestamp}] ℹ️  {message}")
    
    def check_environment(self):
        """检查环境配置"""
        self.log("检查环境配置...")
        
        # 检查REFRESH_TOKEN
        refresh_token = dotenv.get_dotenv_value("IFIND_REFRESH_TOKEN")
        if not refresh_token:
            self.log("REFRESH_TOKEN未配置", "error")
            self.log("请在.env文件中设置IFIND_REFRESH_TOKEN", "warning")
            return False
        
        self.log(f"REFRESH_TOKEN已配置: {refresh_token[:10]}...", "success")
        return True
    
    def check_client_initialization(self):
        """检查API客户端初始化"""
        self.log("初始化API客户端...")
        
        try:
            self.client = FinancialAPIClient()
            self.log("API客户端初始化成功", "success")
            return True
        except Exception as e:
            self.log(f"API客户端初始化失败: {e}", "error")
            return False
    
    def check_token_management(self):
        """检查Token管理"""
        self.log("检查Token管理...")
        
        try:
            access_token = self.client._get_access_token()
            if access_token:
                self.log(f"ACCESS_TOKEN获取成功: {access_token[:10]}...", "success")
                return True
            else:
                self.log("ACCESS_TOKEN获取失败", "error")
                return False
        except Exception as e:
            self.log(f"Token管理异常: {e}", "error")
            return False
    
    async def check_api_connection(self):
        """检查API连接"""
        self.log("测试API连接...")
        
        try:
            # 测试实时行情API
            result = await self.client.get_real_time_quotation('000001.SZ', 'latest')
            
            if result.get('errorcode') == 0:
                self.log("API连接测试成功", "success")
                data = result.get('data', {})
                tables = data.get('tables', [])
                if tables:
                    latest_price = tables[0].get('table', {}).get('latest')
                    if latest_price:
                        self.log(f"测试数据: 000001.SZ 最新价 = {latest_price}")
                return True
            else:
                error_msg = result.get('reason', '未知错误')
                self.log(f"API连接失败: {error_msg}", "error")
                return False
                
        except Exception as e:
            self.log(f"API连接异常: {e}", "error")
            return False
    
    def check_health_status(self):
        """检查整体健康状态"""
        self.log("检查整体健康状态...")
        
        try:
            health_result = self.client.health_check()
            status = health_result.get('status')
            
            if status == 'healthy':
                self.log("金融数据源健康状态: 正常", "success")
                return True
            else:
                error = health_result.get('error', '未知错误')
                self.log(f"金融数据源健康状态: 异常 - {error}", "error")
                return False
                
        except Exception as e:
            self.log(f"健康检查异常: {e}", "error")
            return False
    
    async def run_full_check(self):
        """运行完整检查"""
        if self.verbose:
            print("🔍 === 金融数据源健康检查 ===")
            print()
        
        checks = [
            ("环境配置", self.check_environment),
            ("客户端初始化", self.check_client_initialization),
            ("Token管理", self.check_token_management),
            ("API连接", self.check_api_connection),
            ("健康状态", self.check_health_status)
        ]
        
        passed = 0
        total = len(checks)
        
        for name, check_func in checks:
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                if result:
                    passed += 1
                else:
                    if self.verbose:
                        self.log(f"{name}检查失败", "warning")
                    
            except Exception as e:
                self.log(f"{name}检查异常: {e}", "error")
        
        if self.verbose:
            print()
            print(f"📊 检查结果: {passed}/{total} 通过")
            print()
        
        if passed == total:
            if self.verbose:
                print("🎉 === 金融数据源状态正常 ===")
                print("✅ 所有检查项目通过")
                print("💡 金融数据工具可以正常使用")
            else:
                print("✅ 金融数据源: 正常")
            return True
        else:
            if self.verbose:
                print("⚠️  === 金融数据源存在问题 ===")
                print(f"🔧 {total - passed} 个检查项目失败")
                print("📝 请根据上述错误信息进行修复")
            else:
                print("❌ 金融数据源: 存在问题")
            return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='金融数据源健康检查')
    parser.add_argument('--quiet', '-q', action='store_true', 
                       help='简化模式，只显示最终结果')
    
    args = parser.parse_args()
    verbose = not args.quiet
    
    checker = FinancialDataSourceChecker(verbose=verbose)
    success = await checker.run_full_check()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 检查脚本异常: {e}")
        sys.exit(1)
