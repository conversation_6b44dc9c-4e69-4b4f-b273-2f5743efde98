# Agent-Zero 应用扩展开发指南

## 📋 概述

Agent-Zero 提供了强大的扩展系统，允许开发者在不修改核心代码的情况下，通过扩展机制添加新功能、工具和行为。本文档详细介绍了如何开发和集成各种类型的扩展。

## 🏗️ 扩展系统架构

### **扩展目录结构**
```
python/extensions/
├── system_prompt/              # 系统提示扩展
│   ├── _10_system_prompt.py
│   ├── _20_agent_name.py
│   └── _30_agent_personality.py
├── message_loop_prompts_before/ # 消息循环前扩展
│   ├── _10_context_variables.py
│   └── _20_user_context.py
├── message_loop_prompts_after/  # 消息循环后扩展
│   ├── _50_recall_memories.py
│   ├── _60_include_current_datetime.py
│   ├── _70_tool_recommendations.py  # 工具推荐扩展
│   └── _91_recall_wait.py
└── other_hooks/                # 其他钩子扩展
    └── custom_extensions.py
```

### **扩展执行时机**
```
Agent 消息处理流程:
├── 1. 接收用户输入
├── 2. call_extensions("message_loop_prompts_before")
├── 3. 构建系统提示
│   └── call_extensions("system_prompt")
├── 4. 构建消息历史
├── 5. call_extensions("message_loop_prompts_after")
├── 6. 发送到 LLM
├── 7. 处理 LLM 响应
└── 8. 返回结果
```

## 🔧 扩展基类和接口

### **Extension 基类**
```python
from python.helpers.extension import Extension

class MyExtension(Extension):
    """自定义扩展示例"""
    
    async def execute(self, **kwargs):
        """
        扩展的核心执行方法
        
        Args:
            **kwargs: 扩展参数，根据扩展类型不同而不同
                - system_prompt: 系统提示列表
                - loop_data: 循环数据对象
                - agent: Agent 实例
        """
        # 扩展逻辑实现
        pass
```

### **常用扩展参数**

#### **system_prompt 扩展参数**
```python
async def execute(self, system_prompt, loop_data, **kwargs):
    """
    Args:
        system_prompt (list): 系统提示列表
        loop_data (LoopData): 循环数据对象
    """
    system_prompt.append("自定义系统提示内容")
```

#### **message_loop_prompts_after 扩展参数**
```python
async def execute(self, loop_data, **kwargs):
    """
    Args:
        loop_data (LoopData): 循环数据对象
            - loop_data.system: 系统提示列表
            - loop_data.user_message: 用户消息
            - loop_data.history_output: 历史消息
    """
    loop_data.system.append("自定义内容")
```

## 🛠️ 工具扩展开发

### **1. 创建工具类**
```python
# python/tools/my_custom_tool.py
from python.helpers.tool import Tool, Response

class MyCustomTool(Tool):
    """自定义工具示例"""
    
    def __init__(self, agent, name="my_custom_tool", method=None, args=None, message="", **kwargs):
        super().__init__(agent, name, method, args or {}, message, **kwargs)
    
    async def execute(self, **kwargs):
        """工具执行逻辑"""
        try:
            # 获取参数
            param1 = kwargs.get('param1', '')
            param2 = kwargs.get('param2', '')
            
            # 执行工具逻辑
            result = self._process_data(param1, param2)
            
            # 返回结果
            return Response(
                message=f"处理完成: {result}",
                data=result
            )
            
        except Exception as e:
            return Response(
                message=f"工具执行失败: {str(e)}",
                data=""
            )
    
    def _process_data(self, param1, param2):
        """工具的具体处理逻辑"""
        return f"处理结果: {param1} + {param2}"
```

### **2. 创建工具描述文档**
```markdown
<!-- prompts/default/agent.system.tool.my_custom_tool.md -->
# 自定义工具 (my_custom_tool)

## 工具描述
这是一个自定义工具的示例，用于演示工具开发流程。

## 使用场景
- 数据处理
- 格式转换
- 自定义计算

## 参数说明
- `param1`: 第一个参数 (必需)
- `param2`: 第二个参数 (可选)

## 使用示例
```python
my_custom_tool(
    param1="输入数据",
    param2="配置参数"
)
```

## 触发关键词
- 自定义处理
- 数据转换
- 特殊计算
```

### **3. 注册工具**
```markdown
<!-- prompts/default/agent.system.tools.md -->
{{ include './agent.system.tool.my_custom_tool.md' }}
```

## 🧠 智能推荐扩展开发

### **工具推荐扩展示例**
```python
# python/extensions/message_loop_prompts_after/_70_tool_recommendations.py
from python.helpers.extension import Extension
from python.helpers.tool_selector import tool_selector

class ToolRecommendations(Extension):
    """工具推荐扩展"""
    
    async def execute(self, loop_data, **kwargs):
        """执行工具推荐逻辑"""
        try:
            # 获取用户输入
            user_input = self._get_user_input(loop_data)
            if not user_input:
                return
            
            # 分析用户输入，获取工具推荐
            analysis = tool_selector.analyze_user_input(user_input)
            
            # 生成推荐内容
            recommended_tools = []
            for tool_name, tool_info in analysis.items():
                if tool_info.get('recommended', False):
                    recommended_tools.append({
                        'name': tool_name,
                        'confidence': tool_info.get('confidence', 0),
                        'keywords': tool_info.get('trigger_keywords', [])
                    })
            
            # 添加推荐到系统提示
            if recommended_tools:
                recommendation_text = self._build_recommendation_text(
                    recommended_tools, user_input
                )
                loop_data.system.append(recommendation_text)
                
        except Exception as e:
            # 扩展失败不应该影响主流程
            self.agent.context.log.log(
                type="warning",
                content=f"工具推荐扩展执行失败: {e}"
            )
    
    def _get_user_input(self, loop_data):
        """健壮的用户输入获取"""
        # 方法1：从 loop_data.user_message 获取
        if loop_data.user_message and loop_data.user_message.message:
            return loop_data.user_message.message.strip()
        
        # 方法2：从 agent.last_user_message 获取
        if hasattr(self.agent, 'last_user_message') and self.agent.last_user_message:
            return self.agent.last_user_message.strip()
        
        # 方法3：从历史消息获取
        if hasattr(self.agent, 'history') and self.agent.history:
            try:
                for message in reversed(self.agent.history.messages):
                    if not message.ai and message.content:
                        return message.content.strip()
            except Exception:
                pass
        
        return None
    
    def _build_recommendation_text(self, recommended_tools, user_input):
        """构建推荐文本"""
        lines = [
            "🔧 **智能工具推荐系统**",
            "",
            f"基于用户输入分析: \"{user_input[:100]}{'...' if len(user_input) > 100 else ''}\"",
            "",
            "**推荐工具:**"
        ]
        
        for tool in recommended_tools:
            lines.extend([
                f"- **{tool['name']}** (置信度: {tool['confidence']:.1%})",
                f"  触发关键词: {', '.join(tool['keywords'][:3])}",
                ""
            ])
        
        return "\n".join(lines)
```

## 📊 数据处理扩展开发

### **内存回忆扩展示例**
```python
# python/extensions/message_loop_prompts_after/_50_recall_memories.py
from python.helpers.extension import Extension

class RecallMemories(Extension):
    """内存回忆扩展"""
    
    async def execute(self, loop_data, **kwargs):
        """回忆相关记忆"""
        try:
            # 获取用户消息
            user_message = loop_data.user_message.message if loop_data.user_message else ""
            
            # 搜索相关记忆
            memories = await self._search_memories(user_message)
            
            # 添加到系统提示
            if memories:
                memory_text = self._format_memories(memories)
                loop_data.system.append(memory_text)
                
        except Exception as e:
            self.agent.context.log.log(
                type="warning",
                content=f"内存回忆扩展失败: {e}"
            )
    
    async def _search_memories(self, query):
        """搜索相关记忆"""
        # 实现记忆搜索逻辑
        return []
    
    def _format_memories(self, memories):
        """格式化记忆内容"""
        lines = ["📚 **相关记忆**", ""]
        for memory in memories:
            lines.append(f"- {memory}")
        return "\n".join(lines)
```

## 🎯 扩展开发最佳实践

### **1. 命名规范**
- **文件命名**: `_XX_extension_name.py` (XX为两位数字，控制执行顺序)
- **类命名**: 使用 PascalCase，如 `ToolRecommendations`
- **方法命名**: 使用 snake_case，如 `_get_user_input`

### **2. 执行顺序控制**
```
_10_xxx.py  # 最先执行
_20_xxx.py  # 第二执行
...
_90_xxx.py  # 最后执行
```

### **3. 错误处理**
```python
async def execute(self, **kwargs):
    try:
        # 扩展逻辑
        pass
    except Exception as e:
        # 记录错误但不中断主流程
        self.agent.context.log.log(
            type="warning",
            content=f"扩展执行失败: {e}"
        )
```

### **4. 性能优化**
- 避免阻塞操作
- 使用异步方法
- 缓存重复计算结果
- 限制处理时间

### **5. 兼容性考虑**
- 检查属性存在性: `hasattr(obj, 'attr')`
- 提供默认值: `kwargs.get('key', default)`
- 优雅降级: 功能不可用时提供基础功能

## 🔍 调试和测试

### **扩展调试**
```python
# 添加调试日志
self.agent.context.log.log(
    type="info",
    content=f"扩展执行: {self.__class__.__name__}"
)

# 调试用户输入
print(f"Debug: user_input = {user_input}")
```

### **扩展测试**
```python
# test_my_extension.py
import asyncio
from python.extensions.my_path.my_extension import MyExtension

async def test_extension():
    # 创建模拟环境
    mock_agent = MockAgent()
    mock_loop_data = MockLoopData()
    
    # 测试扩展
    extension = MyExtension(mock_agent)
    await extension.execute(loop_data=mock_loop_data)
    
    # 验证结果
    assert len(mock_loop_data.system) > 0

if __name__ == "__main__":
    asyncio.run(test_extension())
```

## 📚 扩展示例库

### **已实现的扩展**
1. **工具推荐扩展** (`_70_tool_recommendations.py`)
   - 智能分析用户输入
   - 推荐合适的工具
   - 提供使用指导

2. **内存回忆扩展** (`_50_recall_memories.py`)
   - 搜索相关历史记忆
   - 提供上下文信息

3. **时间信息扩展** (`_60_include_current_datetime.py`)
   - 添加当前时间信息
   - 支持时区处理

### **扩展开发模板**
```python
# python/extensions/category/_XX_my_extension.py
from python.helpers.extension import Extension

class MyExtension(Extension):
    """扩展描述"""
    
    async def execute(self, **kwargs):
        """扩展执行逻辑"""
        try:
            # 1. 获取参数
            # 2. 处理逻辑
            # 3. 修改系统状态
            # 4. 记录日志
            pass
        except Exception as e:
            self.agent.context.log.log(
                type="warning",
                content=f"{self.__class__.__name__} 执行失败: {e}"
            )
```

## 🚀 部署和发布

### **扩展打包**
1. 确保所有依赖文件完整
2. 编写详细的文档说明
3. 提供使用示例和测试用例
4. 遵循项目的代码规范

### **扩展分享**
1. 创建独立的扩展包
2. 提供安装和配置说明
3. 包含完整的测试覆盖
4. 维护版本兼容性

## 🔧 高级扩展技巧

### **条件执行扩展**
```python
class ConditionalExtension(Extension):
    """条件执行扩展示例"""

    async def execute(self, loop_data, **kwargs):
        # 只在特定条件下执行
        if not self._should_execute(loop_data):
            return

        # 执行扩展逻辑
        await self._do_extension_work(loop_data)

    def _should_execute(self, loop_data):
        """判断是否应该执行扩展"""
        user_input = loop_data.user_message.message if loop_data.user_message else ""

        # 示例条件：只在用户输入包含特定关键词时执行
        keywords = ["分析", "研究", "调查"]
        return any(keyword in user_input for keyword in keywords)
```

### **状态管理扩展**
```python
class StatefulExtension(Extension):
    """有状态的扩展示例"""

    def __init__(self, agent):
        super().__init__(agent)
        self.state = {}
        self.execution_count = 0

    async def execute(self, loop_data, **kwargs):
        self.execution_count += 1

        # 基于历史状态做决策
        if self.execution_count > 5:
            # 执行特殊逻辑
            await self._handle_frequent_execution(loop_data)

        # 更新状态
        self.state['last_execution'] = datetime.now()
        self.state['last_user_input'] = loop_data.user_message.message
```

### **异步数据获取扩展**
```python
class AsyncDataExtension(Extension):
    """异步数据获取扩展"""

    async def execute(self, loop_data, **kwargs):
        try:
            # 并发获取多个数据源
            tasks = [
                self._fetch_data_source_1(),
                self._fetch_data_source_2(),
                self._fetch_data_source_3()
            ]

            # 等待所有任务完成，设置超时
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=5.0
            )

            # 处理结果
            valid_results = [r for r in results if not isinstance(r, Exception)]
            if valid_results:
                summary = self._combine_results(valid_results)
                loop_data.system.append(f"📊 **数据摘要**: {summary}")

        except asyncio.TimeoutError:
            self.agent.context.log.log(
                type="warning",
                content="数据获取超时，使用缓存数据"
            )
```

## 🎨 扩展配置系统

### **配置文件支持**
```python
# config/extensions.json
{
    "tool_recommendations": {
        "enabled": true,
        "confidence_threshold": 0.7,
        "max_recommendations": 3
    },
    "memory_recall": {
        "enabled": true,
        "max_memories": 5,
        "similarity_threshold": 0.8
    }
}
```

### **配置读取扩展**
```python
import json
from pathlib import Path

class ConfigurableExtension(Extension):
    """支持配置的扩展"""

    def __init__(self, agent):
        super().__init__(agent)
        self.config = self._load_config()

    def _load_config(self):
        """加载扩展配置"""
        config_path = Path("config/extensions.json")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get(self.__class__.__name__.lower(), {})
        return {}

    async def execute(self, **kwargs):
        # 检查是否启用
        if not self.config.get('enabled', True):
            return

        # 使用配置参数
        threshold = self.config.get('confidence_threshold', 0.7)
        max_items = self.config.get('max_recommendations', 3)
```

## 🧪 扩展测试框架

### **测试基类**
```python
# tests/test_extensions_base.py
import unittest
from unittest.mock import Mock, AsyncMock

class ExtensionTestBase(unittest.TestCase):
    """扩展测试基类"""

    def setUp(self):
        """设置测试环境"""
        self.mock_agent = Mock()
        self.mock_agent.context = Mock()
        self.mock_agent.context.log = Mock()
        self.mock_agent.context.log.log = Mock()

        self.mock_loop_data = Mock()
        self.mock_loop_data.system = []
        self.mock_loop_data.user_message = Mock()
        self.mock_loop_data.user_message.message = "测试用户输入"

    def create_extension(self, extension_class):
        """创建扩展实例"""
        return extension_class(self.mock_agent)

    async def run_extension(self, extension, **kwargs):
        """运行扩展"""
        await extension.execute(loop_data=self.mock_loop_data, **kwargs)
```

### **具体测试示例**
```python
# tests/test_tool_recommendations.py
from tests.test_extensions_base import ExtensionTestBase
from python.extensions.message_loop_prompts_after._70_tool_recommendations import ToolRecommendations

class TestToolRecommendations(ExtensionTestBase):
    """工具推荐扩展测试"""

    async def test_basic_recommendation(self):
        """测试基本推荐功能"""
        # 设置测试输入
        self.mock_loop_data.user_message.message = "深入研究人工智能"

        # 创建并运行扩展
        extension = self.create_extension(ToolRecommendations)
        await self.run_extension(extension)

        # 验证结果
        self.assertTrue(len(self.mock_loop_data.system) > 0)
        recommendation_text = self.mock_loop_data.system[0]
        self.assertIn("enhanced_search_engine", recommendation_text)

    async def test_no_input_handling(self):
        """测试无输入情况处理"""
        # 设置空输入
        self.mock_loop_data.user_message = None

        # 运行扩展
        extension = self.create_extension(ToolRecommendations)
        await self.run_extension(extension)

        # 验证没有添加推荐
        self.assertEqual(len(self.mock_loop_data.system), 0)
```

## 📈 性能监控扩展

### **性能统计扩展**
```python
import time
from collections import defaultdict

class PerformanceMonitor(Extension):
    """性能监控扩展"""

    def __init__(self, agent):
        super().__init__(agent)
        self.stats = defaultdict(list)
        self.start_time = None

    async def execute(self, loop_data, **kwargs):
        """记录性能数据"""
        if not hasattr(self, '_monitoring_enabled'):
            self._monitoring_enabled = True

        if self._monitoring_enabled:
            # 记录执行时间
            execution_time = time.time() - (self.start_time or time.time())
            self.stats['execution_times'].append(execution_time)

            # 记录系统提示长度
            system_length = sum(len(s) for s in loop_data.system)
            self.stats['system_prompt_lengths'].append(system_length)

            # 定期报告统计信息
            if len(self.stats['execution_times']) % 10 == 0:
                await self._report_stats()

    async def _report_stats(self):
        """报告性能统计"""
        avg_time = sum(self.stats['execution_times']) / len(self.stats['execution_times'])
        avg_length = sum(self.stats['system_prompt_lengths']) / len(self.stats['system_prompt_lengths'])

        self.agent.context.log.log(
            type="info",
            content=f"性能统计 - 平均执行时间: {avg_time:.3f}s, 平均提示长度: {avg_length:.0f}字符"
        )
```

## 🔒 安全性考虑

### **输入验证扩展**
```python
import re
from html import escape

class SecurityExtension(Extension):
    """安全性扩展"""

    def __init__(self, agent):
        super().__init__(agent)
        self.dangerous_patterns = [
            r'<script.*?>.*?</script>',  # XSS
            r'javascript:',              # JavaScript URLs
            r'data:.*base64',           # Base64 data URLs
        ]

    async def execute(self, loop_data, **kwargs):
        """安全检查"""
        if loop_data.user_message and loop_data.user_message.message:
            user_input = loop_data.user_message.message

            # 检查危险模式
            if self._contains_dangerous_content(user_input):
                self.agent.context.log.log(
                    type="warning",
                    content="检测到潜在的安全风险输入"
                )

                # 清理输入
                cleaned_input = self._sanitize_input(user_input)
                loop_data.user_message.message = cleaned_input

    def _contains_dangerous_content(self, text):
        """检查是否包含危险内容"""
        for pattern in self.dangerous_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def _sanitize_input(self, text):
        """清理输入内容"""
        # HTML转义
        text = escape(text)

        # 移除危险模式
        for pattern in self.dangerous_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        return text.strip()
```

## 📋 扩展开发检查清单

### **开发前检查**
- [ ] 确定扩展的目标和功能
- [ ] 选择合适的扩展类型和执行时机
- [ ] 设计扩展的接口和参数
- [ ] 考虑性能和安全性影响

### **开发中检查**
- [ ] 继承正确的基类
- [ ] 实现必需的方法
- [ ] 添加适当的错误处理
- [ ] 编写清晰的文档注释
- [ ] 遵循命名规范

### **开发后检查**
- [ ] 编写单元测试
- [ ] 进行集成测试
- [ ] 检查性能影响
- [ ] 验证安全性
- [ ] 更新相关文档

### **部署前检查**
- [ ] 代码审查通过
- [ ] 所有测试通过
- [ ] 文档完整准确
- [ ] 版本兼容性确认
- [ ] 备份和回滚计划

---

**文档版本**: 1.0
**最后更新**: 2025-01-13
**适用版本**: Agent-Zero v0.91+
**维护者**: Agent-Zero 开发团队
