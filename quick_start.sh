#!/bin/bash

# Agent-Zero 快速启动脚本
# 一键启动 SearXNG 和 Agent-Zero 服务

echo "🚀 === Agent-Zero 快速启动 ==="
echo ""

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在关闭所有服务..."
    
    # 停止SearXNG
    pkill -f "searx.webapp" 2>/dev/null && echo "✅ SearXNG已停止"
    
    # 停止Agent-Zero
    pkill -f "python run_ui.py" 2>/dev/null && echo "✅ Agent-Zero已停止"
    
    echo "👋 所有服务已停止，再见！"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 显示帮助信息
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -s, --stop     停止所有服务"
    echo "  -c, --check    检查服务状态（包括金融数据源）"
    echo ""
    echo "默认行为: 启动 SearXNG (后台) + 检查金融数据源 + Agent-Zero (前台)"
    echo ""
    echo "功能说明:"
    echo "  🔍 SearXNG: 搜索引擎服务 (端口8888)"
    echo "  🤖 Agent-Zero: AI助手服务 (端口50001)"
    echo "  💰 金融数据源: 同花顺iFinD API检查"
    echo ""
    exit 0
fi

# 停止服务
if [ "$1" = "-s" ] || [ "$1" = "--stop" ]; then
    echo "🛑 停止所有服务..."
    pkill -f "searx.webapp" 2>/dev/null && echo "✅ SearXNG已停止" || echo "ℹ️  SearXNG未运行"
    pkill -f "python run_ui.py" 2>/dev/null && echo "✅ Agent-Zero已停止" || echo "ℹ️  Agent-Zero未运行"
    echo "👋 完成！"
    exit 0
fi

# 检查服务状态
if [ "$1" = "-c" ] || [ "$1" = "--check" ]; then
    echo "🔍 检查服务状态..."
    echo ""

    # 检查SearXNG
    if curl -s http://127.0.0.1:8888 > /dev/null 2>&1; then
        echo "✅ SearXNG: 运行正常 (http://127.0.0.1:8888)"
    else
        echo "❌ SearXNG: 未运行"
    fi

    # 检查Agent-Zero
    if curl -s http://localhost:50001 > /dev/null 2>&1; then
        echo "✅ Agent-Zero: 运行正常 (http://localhost:50001)"
    else
        echo "❌ Agent-Zero: 未运行"
    fi

    # 检查金融数据源
    echo ""
    if [ -f "check_financial_datasource.py" ]; then
        echo "💰 检查金融数据源..."

        # 激活conda环境并运行检查
        if command -v conda >/dev/null 2>&1; then
            source ~/.bashrc 2>/dev/null || true
            conda activate AZ091 2>/dev/null || true
            python check_financial_datasource.py
        else
            python3 check_financial_datasource.py 2>/dev/null || python check_financial_datasource.py
        fi
    else
        echo "ℹ️  金融数据源: 检查模块未找到"
    fi

    echo ""
    echo "📊 进程信息:"
    ps aux | grep -E "(searx|run_ui)" | grep -v grep || echo "   无相关进程运行"

    exit 0
fi

# 第一步：启动 SearXNG（后台）
echo "🔍 第一步：启动 SearXNG 服务（后台）..."

# 检查 SearXNG 是否已经运行
if curl -s http://127.0.0.1:8888 > /dev/null 2>&1; then
    echo "ℹ️  SearXNG 已经在运行，跳过启动"
else
    echo "🔄 启动 SearXNG..."
    ./start_searxng.sh > /dev/null 2>&1 &
    
    # 等待启动
    echo "⏳ 等待 SearXNG 启动..."
    for i in {1..30}; do
        if curl -s http://127.0.0.1:8888 > /dev/null 2>&1; then
            echo "✅ SearXNG 启动成功"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "❌ SearXNG 启动超时"
            echo "📝 请手动检查: ./start_searxng.sh"
            exit 1
        fi
        sleep 1
    done
fi

# 第二步：检查金融数据源
echo ""
echo "💰 第二步：检查金融数据源状态..."

# 检查金融数据源健康状态
if [ -f "check_financial_datasource.py" ]; then
    echo "🔍 正在检查同花顺iFinD数据源..."

    # 激活conda环境并运行检查（简化模式）
    if command -v conda >/dev/null 2>&1; then
        # 使用conda环境
        source ~/.bashrc 2>/dev/null || true
        conda activate AZ090 2>/dev/null || true
        python check_financial_datasource.py --quiet
        datasource_status=$?
    else
        # 直接使用python
        python3 check_financial_datasource.py --quiet 2>/dev/null || python check_financial_datasource.py --quiet
        datasource_status=$?
    fi

    if [ $datasource_status -eq 0 ]; then
        echo "✅ 金融数据源检查完成"
    else
        echo "⚠️  金融数据源存在问题，但不影响系统启动"
        echo "📝 金融功能可能受限，请查看上述提示进行修复"
    fi
else
    echo "ℹ️  未找到金融数据源检查模块，跳过检查"
fi

# 第三步：启动 Agent-Zero（前台）
echo ""
echo "🤖 第三步：启动 Agent-Zero 服务（前台）..."

# 检查 Agent-Zero 是否已经运行
if curl -s http://localhost:50001 > /dev/null 2>&1; then
    echo "⚠️  Agent-Zero 已经在运行"
    echo "📝 如需重启，请先运行: $0 --stop"
    exit 1
fi

echo "🔄 启动 Agent-Zero..."
echo "📝 按 Ctrl+C 停止所有服务"
echo ""

# 显示服务信息
echo "🌐 服务地址："
echo "   🔍 SearXNG: http://127.0.0.1:8888"
echo "   📱 Agent-Zero: http://localhost:50001"
echo ""

# 启动 Agent-Zero（前台运行）
./start_agent_zero.sh -H 0.0.0.0 -p 50001

# 如果到达这里，说明 Agent-Zero 被停止了
cleanup
