#!/usr/bin/env python3
"""
金融分析MCP服务器 - 修复版本
端口8880，修复websockets弃用警告
"""

# 首先过滤弃用警告
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="websockets")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="uvicorn")

import asyncio
import logging
import os
import sys
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import uvicorn
from fastapi import FastAPI
from fastmcp import FastMCP
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入工具类
from clients.ifind_client import FinancialAPIClient
from tools.base_tool import FinancialBaseTool

# 金融关键词配置
FINANCIAL_KEYWORDS = {
    "stock_related": [
        "股票", "股价", "行情", "涨跌", "涨幅", "跌幅", "市值", "成交量", "成交额",
        "开盘价", "收盘价", "最高价", "最低价", "昨收价", "换手率", "振幅"
    ],
    "query_actions": [
        "查询股票", "查询股价", "查询行情", "查看股票", "查看股价", "查看行情",
        "获取股票", "获取股价", "获取行情", "股票查询", "股价查询", "行情查询",
        "查看证券", "查询证券", "获取证券", "分析股票", "分析证券", "分析行情"
    ],
    "technical_indicators": [
        "技术指标", "技术分析", "K线", "MACD", "KDJ", "RSI", "BOLL", "均线",
        "移动平均", "布林带", "相对强弱", "随机指标", "指数平滑"
    ],
    "fundamental_data": [
        "市盈率", "市净率", "PE", "PB", "ROE", "ROA", "净资产收益率",
        "每股收益", "每股净资产", "净利润", "营业收入", "毛利率", "净利率"
    ],
    "financial_reports": [
        "财务", "财报", "年报", "季报", "财务指标", "财务分析", "基本面", "估值",
        "总股本", "流通股", "总市值", "流通市值", "股东权益", "负债率"
    ]
}

# 创建FastMCP服务器实例
mcp_server = FastMCP(
    name="Financial Analysis MCP Server",
    instructions="""
    专业金融数据分析MCP服务器，提供以下功能：
    
    🏦 核心功能：
    - 实时股票行情查询
    - 历史数据分析
    - 技术指标计算（MACD、RSI、KDJ等50+指标）
    - 财务报表数据
    - 基础数据查询
    - 智能查询推荐
    
    🎯 智能识别：
    - 自动识别股票代码和名称
    - 支持自然语言查询
    - 智能查询类型检测
    
    数据源：同花顺iFinD专业金融数据
    """
)

# 全局API客户端实例
api_client = None

def get_api_client() -> FinancialAPIClient:
    """获取API客户端实例"""
    global api_client
    if api_client is None:
        api_client = FinancialAPIClient()
        logger.info("金融API客户端初始化成功")
    return api_client

# 创建工具实例
financial_tool = FinancialBaseTool("financial_data")

def calculate_financial_confidence(query: str) -> Dict[str, Any]:
    """计算金融查询置信度"""
    query_lower = query.lower()
    matched_keywords = []
    confidence = 0.0
    
    # 检查股票代码（高权重）
    stock_pattern = r'\d{6}\.(SZ|SH|sz|sh)'
    if re.search(stock_pattern, query):
        confidence += 0.5
        matched_keywords.append("股票代码")
    
    # 检查各类关键词
    for category, keywords in FINANCIAL_KEYWORDS.items():
        for keyword in keywords:
            if keyword.lower() in query_lower:
                confidence += 0.15
                matched_keywords.append(keyword)
                break  # 每个类别只计算一次
    
    # 检查股票名称
    stock_names = ["贵州茅台", "五粮液", "比亚迪", "平安银行", "招商银行", "中国平安", "宁德时代"]
    for name in stock_names:
        if name in query:
            confidence += 0.3
            matched_keywords.append(name)
    
    # 限制最大置信度
    confidence = min(confidence, 1.0)
    
    return {
        "confidence": confidence,
        "matched_keywords": matched_keywords,
        "recommended": confidence >= 0.6,
        "query_type": financial_tool.detect_query_type(query) if confidence >= 0.6 else "unknown"
    }

@mcp_server.tool()
async def analyze_query_intent(query: str) -> str:
    """分析查询意图并提供工具推荐"""
    try:
        analysis = calculate_financial_confidence(query)
        
        if analysis["recommended"]:
            confidence_pct = analysis["confidence"] * 100
            keywords_str = ", ".join(analysis["matched_keywords"][:5])
            
            return f"""
💡 检测到您可能需要专业金融数据（置信度: {confidence_pct:.1f}%）
   触发关键词: {keywords_str}

   建议使用 MCP金融分析服务器，它可以提供：
   • 实时股票行情数据
   • 历史价格和技术指标
   • 财务指标和基本面数据
   • 专业投资分析支持
   
   🔗 MCP服务器已连接，您可以直接询问：
   - "查询贵州茅台的实时行情"
   - "分析五粮液的MACD指标"
   - "获取比亚迪的财务报表"
"""
        else:
            return f"""
🔍 查询意图分析结果：
   置信度: {analysis['confidence']*100:.1f}% (低于推荐阈值60%)
   
   💡 如需金融数据查询，请尝试包含：
   • 股票名称或代码（如：贵州茅台、600519.SH）
   • 查询动作（如：查询、分析、获取）
   • 数据类型（如：行情、技术指标、财务数据）
"""
    
    except Exception as e:
        return f"❌ 查询意图分析失败: {str(e)}"

@mcp_server.tool()
async def get_stock_real_time(
    codes: str,
    indicators: str = "latest,preClose,change,changeRatio,volume,amount"
) -> str:
    """获取股票实时行情数据"""
    try:
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        financial_tool.log_execution("实时行情查询", {"codes": codes, "indicators": indicators})
        
        client = get_api_client()
        result = await client.get_real_time_quotation(codes, indicators)
        
        return financial_tool.format_financial_data(result, "real_time")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_stock_history(
    codes: str,
    start_date: str,
    end_date: str,
    indicators: str = "preClose,open,high,low,close,volume,changeRatio"
) -> str:
    """获取股票历史行情数据"""
    try:
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"
        
        is_valid, error_msg = financial_tool.validate_date_range(start_date, end_date)
        if not is_valid:
            return f"❌ 日期范围错误: {error_msg}"
        
        financial_tool.log_execution("历史数据查询", {
            "codes": codes, "start_date": start_date, "end_date": end_date
        })
        
        client = get_api_client()
        result = await client.get_history_quotation(codes, start_date, end_date, indicators)
        
        return financial_tool.format_financial_data(result, "history")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_stock_basic_data(
    codes: str,
    indicators: str = "totalShares,totalCapital,mv,pb,pe_ttm"
) -> str:
    """获取股票基础数据"""
    try:
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        financial_tool.log_execution("基础数据查询", {"codes": codes, "indicators": indicators})
        
        client = get_api_client()
        result = await client.get_basic_data(codes, indicators)
        
        return financial_tool.format_financial_data(result, "basic")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def get_financial_report(
    codes: str,
    report_type: str = "",
    period: str = ""
) -> str:
    """获取财务报表数据"""
    try:
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 参数错误: {error_msg}"
        
        financial_tool.log_execution("财务报表查询", {
            "codes": codes, "report_type": report_type, "period": period
        })
        
        client = get_api_client()
        result = await client.get_financial_report(codes, report_type, period)
        
        return financial_tool.format_financial_data(result, "financial_report")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def analyze_technical_indicators(
    query: str = "",
    codes: str = "",
    indicators: str = "MACD,RSI,KDJ",
    period: str = "1M"
) -> str:
    """技术指标分析"""
    try:
        if query and not codes:
            codes = financial_tool.extract_stock_codes(query)
        
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码（如600519.SH）或股票名称"
        
        is_valid, error_msg = financial_tool.validate_stock_codes(codes)
        if not is_valid:
            return f"❌ 股票代码错误: {error_msg}"
        
        # 计算时间范围
        end_date = datetime.now()
        if period == "1M":
            start_date = end_date - timedelta(days=30)
        elif period == "3M":
            start_date = end_date - timedelta(days=90)
        elif period == "6M":
            start_date = end_date - timedelta(days=180)
        elif period == "1Y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)
        
        financial_tool.log_execution("技术指标分析", {
            "query": query, "codes": codes, "indicators": indicators, "period": period
        })
        
        client = get_api_client()
        result = await client.get_technical_indicators(
            codes=codes,
            starttime=start_date.strftime("%Y-%m-%d"),
            endtime=end_date.strftime("%Y-%m-%d"),
            indicators=indicators
        )
        
        return financial_tool.format_financial_data(result, "technical_indicators")
        
    except Exception as e:
        return financial_tool.handle_error(e)

@mcp_server.tool()
async def financial_smart_query(
    query: str,
    query_type: str = "auto"
) -> str:
    """智能金融查询"""
    try:
        analysis = calculate_financial_confidence(query)
        
        if not analysis["recommended"]:
            return await analyze_query_intent(query)
        
        codes = financial_tool.extract_stock_codes(query)
        if not codes:
            return "❌ 未识别到有效的股票代码，请提供股票代码或股票名称"
        
        if query_type == "auto":
            query_type = analysis["query_type"]
        
        financial_tool.log_execution("智能查询", {
            "query": query, "detected_type": query_type, "codes": codes
        })
        
        if query_type == "technical_indicators":
            return await analyze_technical_indicators(query=query, codes=codes)
        elif query_type == "history":
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            return await get_stock_history(codes=codes, start_date=start_date, end_date=end_date)
        elif query_type == "financial_report":
            return await get_financial_report(codes=codes)
        else:
            return await get_stock_real_time(codes=codes)
            
    except Exception as e:
        return financial_tool.handle_error(e)

# 创建FastAPI应用
app = FastAPI(title="Financial MCP Server", version="1.0.0")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        client = get_api_client()
        health_status = client.health_check()
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "port": 8880,
            "api_status": health_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

# 集成MCP服务器到FastAPI
app.mount("/mcp", mcp_server.sse_app)

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("IFIND_REFRESH_TOKEN"):
        logger.warning("未设置IFIND_REFRESH_TOKEN环境变量，请在.env文件中配置")
    
    logger.info("🚀 启动金融分析MCP服务器...")
    logger.info("📡 SSE端点: http://localhost:8880/mcp/sse")
    logger.info("🏥 健康检查: http://localhost:8880/health")
    logger.info("🔧 已修复websockets弃用警告")
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8880,
        log_level="info"
    )
