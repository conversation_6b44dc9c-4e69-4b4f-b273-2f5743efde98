# 智能API选择功能 - 快速参考

## 功能概述
解决非交易时间股票数据获取为空的问题，实现7×24小时可用的股票数据查询。

## 核心逻辑
```
用户查询 → 判断交易时间 → 选择API → 获取数据 → 格式化返回
              ↓              ↓
          交易时间        非交易时间
              ↓              ↓
        实时行情API      历史行情API
```

## 主要修改

### 1. 文件：`python/helpers/financial_api_client.py`

#### 新增方法：
```python
def _is_trading_time(self) -> bool
def _get_latest_trading_date(self) -> str  
def _map_historical_to_realtime_format(self, historical_result: Dict[str, Any]) -> Dict[str, Any]
```

#### 修改方法：
```python
async def get_real_time_quotation(self, codes: str, indicators: str = "") -> Dict[str, Any]
```

#### 关键修改点：
- 添加时间判断逻辑
- 实现智能API选择
- 数据格式自动映射（close → latest）

### 2. 文件：`python/tools/financial_data_tool.py`

#### 修改方法：
```python
def _format_real_time_result(self, result: Dict[str, Any], codes: str) -> str
def _format_single_stock_data(self, code: str, data: Dict[str, Any]) -> str
```

#### 关键修改点：
- 数据源识别和显示
- 价格字段智能标签（最新价 vs 收盘价）
- 用户友好的提示信息

## 交易时间定义
- **上午**：09:30 - 11:30
- **下午**：13:00 - 15:00
- **交易日**：周一至周五（不含节假日）

## API映射关系

| 场景 | API端点 | 数据特点 | 价格字段 |
|------|---------|----------|----------|
| 交易时间 | `/api/v1/real_time_quotation` | 实时数据 | `latest` |
| 非交易时间 | `/api/v1/cmd_history_quotation` | 历史数据 | `close` → `latest` |

## 用户体验对比

### 修改前（非交易时间）
```
用户：查询平安银行股票
系统：❌ 未获取到数据
```

### 修改后（非交易时间）
```
用户：查询平安银行股票
系统：📊 股票行情数据 (最近交易日: 2025-07-14)
      **000001.SZ**
      - 收盘价: 16.62 (+0.05, +0.30%)
      - 开盘价: 16.58
      - 成交量: 45,678,900股
      
      *数据来源: 同花顺iFinD历史行情API*
      *说明: 当前为非交易时间，显示最近交易日数据*
```

## 关键技术点

### 1. 时间处理
```python
# 避免time模块冲突
from datetime import datetime, timedelta, time as dt_time

# 交易时间判断
current_time = now.time()
morning_start = dt_time(9, 30)
```

### 2. 智能降级
```python
if self._is_trading_time():
    # 尝试实时API
    result = await self._make_request('/api/v1/real_time_quotation', params)
    if not self._is_valid_data(result):
        # 降级到历史API
        result = await self._get_historical_data(codes, indicators)
else:
    # 直接使用历史API
    result = await self._get_historical_data(codes, indicators)
```

### 3. 数据映射
```python
# 字段映射：close → latest
if 'close' in table_data and 'latest' not in table_data:
    table_data['latest'] = table_data['close']

# 添加元数据
table['data_source'] = 'historical'
table['trading_date'] = self._get_latest_trading_date()
```

## 测试验证

### 运行测试
```bash
# 基础功能测试
python test_smart_api_selection.py

# 完整功能测试
python test_complete_smart_api.py
```

### 验证要点
- ✅ 交易时间判断正确
- ✅ API自动切换正常
- ✅ 数据格式映射正确
- ✅ 用户界面显示友好
- ✅ 错误处理优雅

## 配置参数

### 交易时间（可调整）
```python
MORNING_START = dt_time(9, 30)    # 上午开盘
MORNING_END = dt_time(11, 30)     # 上午收盘  
AFTERNOON_START = dt_time(13, 0)  # 下午开盘
AFTERNOON_END = dt_time(15, 0)    # 下午收盘
```

### 缓存设置（可选）
```python
CACHE_TTL_TRADING = 300      # 交易时间缓存5分钟
CACHE_TTL_NON_TRADING = 3600 # 非交易时间缓存1小时
```

## 故障排除

### 常见问题

#### 1. 时间判断错误
**症状**：交易时间内使用历史API
**解决**：检查系统时间和时区设置

#### 2. 数据映射失败  
**症状**：历史数据中没有latest字段
**解决**：检查`_map_historical_to_realtime_format`方法

#### 3. API调用失败
**症状**：两个API都返回错误
**解决**：检查网络连接和API密钥

### 调试方法
```python
# 启用调试日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查交易时间判断
client = FinancialAPIClient()
print(f"当前是否交易时间: {client._is_trading_time()}")
print(f"最近交易日: {client._get_latest_trading_date()}")
```

## 性能优化

### 1. 缓存策略
- 交易时间：缓存5分钟
- 非交易时间：缓存1小时

### 2. 并发处理
- 支持多股票并发查询
- 使用连接池减少开销

### 3. 智能预取
- 预取热门股票数据
- 后台更新缓存

## 扩展建议

### 短期
- [ ] 添加港股、美股支持
- [ ] 集成交易日历API
- [ ] 实现数据质量检查

### 中期  
- [ ] 机器学习优化API选择
- [ ] 多数据源融合
- [ ] 实时数据质量评估

### 长期
- [ ] 全球市场时区支持
- [ ] 智能数据预测
- [ ] 个性化数据推荐

## 总结

智能API选择功能通过以下核心改进解决了非交易时间数据获取问题：

1. **智能时间判断** - 精确识别交易时间
2. **自动API切换** - 无缝选择最佳数据源  
3. **数据格式统一** - 保持接口一致性
4. **用户体验优化** - 友好的信息展示

该功能现已稳定运行，为用户提供7×24小时可用的股票数据查询服务。
