# Agent-Zero 工具开发者指南

## 📋 **文档概述**

本文档为Agent-Zero工具系统的开发者提供详细的工具开发、集成和维护指南，包括最佳实践、代码示例和故障排除方法。

**文档版本**: v1.0  
**更新日期**: 2025-07-07  
**目标读者**: 工具开发者、系统集成者、技术维护人员

---

## 🏗️ **工具开发基础**

### **工具类结构**

#### **基础工具类**:
```python
from python.helpers.tool import Tool, Response
from python.helpers import files
import asyncio

class MyTool(Tool):
    """
    自定义工具类
    继承自Tool基类，实现execute方法
    """
    
    async def execute(self, **kwargs):
        """
        工具执行方法
        
        Args:
            **kwargs: 工具参数
            
        Returns:
            Response: 工具执行结果
        """
        try:
            # 1. 参数验证
            self._validate_parameters(kwargs)
            
            # 2. 核心逻辑实现
            result = await self._core_logic(**kwargs)
            
            # 3. 结果处理
            formatted_result = self._format_result(result)
            
            # 4. 返回响应
            return Response(
                message=formatted_result,
                break_loop=False  # 是否中断对话循环
            )
            
        except Exception as e:
            # 错误处理
            error_msg = f"工具执行失败: {str(e)}"
            self._print_error(error_msg)
            return Response(
                message=error_msg,
                break_loop=False
            )
    
    def _validate_parameters(self, kwargs):
        """参数验证"""
        required_params = ['param1', 'param2']
        for param in required_params:
            if param not in kwargs:
                raise ValueError(f"缺少必需参数: {param}")
    
    async def _core_logic(self, **kwargs):
        """核心业务逻辑"""
        # 实现具体功能
        pass
    
    def _format_result(self, result):
        """结果格式化"""
        return f"处理结果: {result}"
    
    def _print_error(self, message):
        """错误日志输出"""
        self.agent.context.log.error(message)
```

### **工具参数设计**

#### **参数类型定义**:
```python
class ToolParameters:
    """工具参数定义"""
    
    def __init__(self):
        self.required_params = {
            'input_text': str,      # 必需的字符串参数
            'max_results': int,     # 必需的整数参数
        }
        
        self.optional_params = {
            'timeout': (int, 30),           # 可选参数，默认值30
            'format': (str, 'json'),        # 可选参数，默认值'json'
            'verbose': (bool, False),       # 可选参数，默认值False
        }
    
    def validate(self, kwargs):
        """参数验证方法"""
        # 检查必需参数
        for param, param_type in self.required_params.items():
            if param not in kwargs:
                raise ValueError(f"缺少必需参数: {param}")
            if not isinstance(kwargs[param], param_type):
                raise TypeError(f"参数 {param} 类型错误，期望 {param_type.__name__}")
        
        # 设置默认值
        for param, (param_type, default_value) in self.optional_params.items():
            if param not in kwargs:
                kwargs[param] = default_value
            elif not isinstance(kwargs[param], param_type):
                raise TypeError(f"参数 {param} 类型错误，期望 {param_type.__name__}")
        
        return kwargs
```

### **异步处理模式**

#### **基础异步模式**:
```python
class AsyncTool(Tool):
    async def execute(self, **kwargs):
        # 异步操作示例
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                data = await response.json()
        
        return Response(message=str(data))
```

#### **并发处理模式**:
```python
class ConcurrentTool(Tool):
    async def execute(self, urls, **kwargs):
        # 并发处理多个URL
        tasks = [self._process_url(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常
        success_results = []
        errors = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                errors.append(f"URL {urls[i]} 处理失败: {result}")
            else:
                success_results.append(result)
        
        return Response(
            message=f"成功处理 {len(success_results)} 个URL，{len(errors)} 个失败"
        )
    
    async def _process_url(self, url):
        # 单个URL处理逻辑
        pass
```

---

## 📝 **工具描述文件**

### **描述文件模板**

#### **基础模板** (`prompts/default/agent.system.tool.my_tool.md`):
```markdown
## My Tool

🔧 **工具名称**: My Tool  
📝 **功能描述**: 详细描述工具的功能和用途  
🎯 **适用场景**: 说明工具的适用场景和使用时机  

### **核心特性**:
- ✨ 特性1: 详细说明
- 🚀 特性2: 详细说明
- 🔍 特性3: 详细说明

### **使用方法**:

```json
{
  "tool_name": "my_tool",
  "tool_args": {
    "param1": "参数1说明",
    "param2": "参数2说明",
    "optional_param": "可选参数说明"
  }
}
```

### **参数说明**:

#### **必需参数**:
- `param1` (string): 参数1的详细说明
- `param2` (integer): 参数2的详细说明

#### **可选参数**:
- `optional_param` (string, 默认: "default"): 可选参数说明
- `timeout` (integer, 默认: 30): 超时时间（秒）

### **使用示例**:

#### **示例1: 基础用法**
```json
{
  "tool_name": "my_tool",
  "tool_args": {
    "param1": "示例值1",
    "param2": 100
  }
}
```

#### **示例2: 高级用法**
```json
{
  "tool_name": "my_tool",
  "tool_args": {
    "param1": "示例值1",
    "param2": 100,
    "optional_param": "自定义值",
    "timeout": 60
  }
}
```

### **返回结果**:
工具执行成功后返回格式化的结果信息，包括：
- 处理状态
- 结果数据
- 执行统计

### **注意事项**:
- ⚠️ 注意事项1
- ⚠️ 注意事项2
- 💡 使用建议

### **错误处理**:
- 参数验证失败时返回详细错误信息
- 网络超时时自动重试
- 异常情况下提供友好的错误提示
```

### **高级描述特性**

#### **条件使用说明**:
```markdown
### **何时使用此工具**:
- ✅ 当用户请求包含关键词: "关键词1", "关键词2"
- ✅ 当需要处理特定类型的数据时
- ✅ 当其他工具无法满足需求时

### **何时不使用此工具**:
- ❌ 简单查询可以用基础工具解决
- ❌ 数据量过大可能导致超时
- ❌ 需要实时交互的场景
```

#### **性能说明**:
```markdown
### **性能特征**:
- ⏱️ **执行时间**: 通常 5-30 秒
- 💾 **内存使用**: 中等 (50-200MB)
- 🌐 **网络依赖**: 需要稳定网络连接
- 🔄 **并发支持**: 支持最多 5 个并发任务
```

---

## 🔧 **工具注册与集成**

### **工具注册流程**

#### **步骤1: 创建工具文件**
```bash
# 创建工具实现文件
touch python/tools/my_tool.py

# 创建工具描述文件
touch prompts/default/agent.system.tool.my_tool.md
```

#### **步骤2: 实现工具类**
```python
# python/tools/my_tool.py
from python.helpers.tool import Tool, Response

class MyTool(Tool):
    async def execute(self, **kwargs):
        # 工具实现
        return Response(message="工具执行结果")
```

#### **步骤3: 编写工具描述**
```markdown
<!-- prompts/default/agent.system.tool.my_tool.md -->
## My Tool
工具描述内容...
```

#### **步骤4: 注册到主工具文件**
```markdown
<!-- prompts/default/agent.system.tools.md -->
{{ include './agent.system.tool.my_tool.md' }}
```

#### **步骤5: 更新工具选择器**
```python
# python/helpers/tool_selector.py
class ToolSelector:
    def __init__(self):
        # 添加新工具的关键词
        self.my_tool_keywords = [
            "关键词1", "关键词2", "keyword1", "keyword2"
        ]
    
    def analyze_user_input(self, user_input):
        # 添加新工具的分析逻辑
        my_tool_score = self._calculate_keyword_score(
            user_input.lower(), self.my_tool_keywords
        )
        
        analysis['my_tool'] = {
            'score': my_tool_score,
            'recommended': my_tool_score >= self.confidence_threshold,
            'confidence': min(my_tool_score, 1.0),
            'trigger_keywords': self._find_matching_keywords(
                user_input.lower(), self.my_tool_keywords
            )
        }
```

### **依赖管理**

#### **Python依赖**:
```python
# 在工具文件顶部导入依赖
try:
    import required_library
except ImportError:
    raise ImportError("请安装required_library: pip install required_library")

class MyTool(Tool):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 检查依赖可用性
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查工具依赖"""
        try:
            import required_library
            self.library_available = True
        except ImportError:
            self.library_available = False
            self.agent.context.log.warning("MyTool依赖库未安装")
    
    async def execute(self, **kwargs):
        if not self.library_available:
            return Response(
                message="工具依赖库未安装，请运行: pip install required_library"
            )
        # 正常执行逻辑
```

#### **系统依赖**:
```python
import shutil
import subprocess

class SystemTool(Tool):
    def _check_system_dependencies(self):
        """检查系统依赖"""
        required_commands = ['git', 'curl', 'node']
        missing_commands = []
        
        for cmd in required_commands:
            if not shutil.which(cmd):
                missing_commands.append(cmd)
        
        if missing_commands:
            raise RuntimeError(f"缺少系统命令: {', '.join(missing_commands)}")
```

---

## 🧪 **测试与调试**

### **单元测试**

#### **测试框架**:
```python
import unittest
import asyncio
from unittest.mock import Mock, patch
from python.tools.my_tool import MyTool

class TestMyTool(unittest.TestCase):
    def setUp(self):
        """测试初始化"""
        self.mock_agent = Mock()
        self.tool = MyTool(
            agent=self.mock_agent,
            name="my_tool",
            method=None,
            args={},
            message="test"
        )
    
    def test_parameter_validation(self):
        """测试参数验证"""
        # 测试缺少必需参数
        with self.assertRaises(ValueError):
            asyncio.run(self.tool.execute())
        
        # 测试参数类型错误
        with self.assertRaises(TypeError):
            asyncio.run(self.tool.execute(param1=123))  # 应该是字符串
    
    def test_successful_execution(self):
        """测试成功执行"""
        result = asyncio.run(self.tool.execute(
            param1="test_value",
            param2=100
        ))
        
        self.assertIsNotNone(result)
        self.assertIn("成功", result.message)
    
    @patch('aiohttp.ClientSession.get')
    def test_network_request(self, mock_get):
        """测试网络请求"""
        # 模拟网络响应
        mock_response = Mock()
        mock_response.json.return_value = {"status": "success"}
        mock_get.return_value.__aenter__.return_value = mock_response
        
        result = asyncio.run(self.tool.execute(
            param1="test_value",
            param2=100
        ))
        
        self.assertIn("success", result.message)

if __name__ == '__main__':
    unittest.main()
```

### **集成测试**

#### **工具加载测试**:
```python
def test_tool_loading():
    """测试工具加载"""
    from python.helpers.extract_tools import load_classes_from_folder
    from python.helpers.tool import Tool
    
    # 测试工具类加载
    classes = load_classes_from_folder("python/tools", "my_tool.py", Tool)
    assert len(classes) == 1
    assert classes[0].__name__ == "MyTool"
    
    # 测试工具实例化
    tool_instance = classes[0](
        agent=None, name="my_tool", method=None, args={}, message="test"
    )
    assert tool_instance is not None
```

#### **工具选择器测试**:
```python
def test_tool_selector():
    """测试工具选择器"""
    from python.helpers.tool_selector import tool_selector
    
    # 测试关键词触发
    analysis = tool_selector.analyze_user_input("使用my_tool处理数据")
    assert analysis['my_tool']['recommended'] == True
    assert analysis['my_tool']['confidence'] > 0.7
    
    # 测试非相关输入
    analysis = tool_selector.analyze_user_input("今天天气怎么样")
    assert analysis['my_tool']['recommended'] == False
```

### **调试技巧**

#### **日志调试**:
```python
class MyTool(Tool):
    async def execute(self, **kwargs):
        # 添加详细日志
        self.agent.context.log.info(f"MyTool开始执行，参数: {kwargs}")
        
        try:
            result = await self._core_logic(**kwargs)
            self.agent.context.log.info(f"MyTool执行成功，结果: {result}")
            return Response(message=str(result))
        except Exception as e:
            self.agent.context.log.error(f"MyTool执行失败: {str(e)}")
            raise
```

#### **性能监控**:
```python
import time
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(self, **kwargs):
        start_time = time.time()
        try:
            result = await func(self, **kwargs)
            execution_time = time.time() - start_time
            self.agent.context.log.info(f"工具执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            self.agent.context.log.error(f"工具执行失败，耗时: {execution_time:.2f}秒")
            raise
    return wrapper

class MyTool(Tool):
    @performance_monitor
    async def execute(self, **kwargs):
        # 工具实现
        pass
```

---

## 📊 **性能优化**

### **缓存策略**

#### **结果缓存**:
```python
import hashlib
import json
from functools import lru_cache

class CachedTool(Tool):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache = {}
    
    def _get_cache_key(self, **kwargs):
        """生成缓存键"""
        # 创建参数的哈希值作为缓存键
        params_str = json.dumps(kwargs, sort_keys=True)
        return hashlib.md5(params_str.encode()).hexdigest()
    
    async def execute(self, **kwargs):
        # 检查缓存
        cache_key = self._get_cache_key(**kwargs)
        if cache_key in self.cache:
            self.agent.context.log.info("使用缓存结果")
            return self.cache[cache_key]
        
        # 执行工具逻辑
        result = await self._core_logic(**kwargs)
        
        # 存储到缓存
        response = Response(message=str(result))
        self.cache[cache_key] = response
        
        return response
```

#### **LRU缓存**:
```python
from functools import lru_cache

class OptimizedTool(Tool):
    @lru_cache(maxsize=128)
    def _expensive_computation(self, param):
        """昂贵的计算操作，使用LRU缓存"""
        # 复杂计算逻辑
        return complex_calculation(param)
    
    async def execute(self, **kwargs):
        # 使用缓存的计算结果
        result = self._expensive_computation(kwargs['param'])
        return Response(message=str(result))
```

### **资源管理**

#### **连接池管理**:
```python
import aiohttp
from aiohttp import TCPConnector

class NetworkTool(Tool):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 创建连接池
        self.connector = TCPConnector(
            limit=100,          # 总连接数限制
            limit_per_host=30,  # 每个主机连接数限制
            ttl_dns_cache=300,  # DNS缓存时间
            use_dns_cache=True,
        )
        self.session = None
    
    async def _get_session(self):
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return self.session
    
    async def execute(self, **kwargs):
        session = await self._get_session()
        # 使用会话进行网络请求
        async with session.get(url) as response:
            data = await response.json()
        return Response(message=str(data))
    
    async def cleanup(self):
        """清理资源"""
        if self.session and not self.session.closed:
            await self.session.close()
        if self.connector:
            await self.connector.close()
```

### **并发控制**

#### **信号量限制**:
```python
import asyncio

class ConcurrentTool(Tool):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 限制并发数量
        self.semaphore = asyncio.Semaphore(5)
    
    async def execute(self, urls, **kwargs):
        """并发处理多个URL"""
        async def process_with_limit(url):
            async with self.semaphore:
                return await self._process_single_url(url)
        
        # 创建并发任务
        tasks = [process_with_limit(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return Response(message=f"处理完成，共 {len(results)} 个结果")
```

---

## 🔒 **安全考虑**

### **输入验证**

#### **参数清理**:
```python
import re
from urllib.parse import urlparse

class SecureTool(Tool):
    def _validate_url(self, url):
        """URL验证"""
        try:
            parsed = urlparse(url)
            if parsed.scheme not in ['http', 'https']:
                raise ValueError("只支持HTTP/HTTPS协议")
            if parsed.hostname in ['localhost', '127.0.0.1']:
                raise ValueError("不允许访问本地地址")
            return True
        except Exception as e:
            raise ValueError(f"无效的URL: {e}")
    
    def _sanitize_input(self, text):
        """输入清理"""
        # 移除潜在的恶意字符
        sanitized = re.sub(r'[<>"\']', '', text)
        # 限制长度
        if len(sanitized) > 1000:
            sanitized = sanitized[:1000]
        return sanitized
    
    async def execute(self, url, text, **kwargs):
        # 验证和清理输入
        self._validate_url(url)
        clean_text = self._sanitize_input(text)
        
        # 安全执行逻辑
        return Response(message="安全执行完成")
```

### **权限控制**

#### **操作权限检查**:
```python
class PrivilegedTool(Tool):
    REQUIRED_PERMISSIONS = ['file_access', 'network_access']
    
    def _check_permissions(self):
        """检查权限"""
        user_permissions = self.agent.get_user_permissions()
        for permission in self.REQUIRED_PERMISSIONS:
            if permission not in user_permissions:
                raise PermissionError(f"缺少权限: {permission}")
    
    async def execute(self, **kwargs):
        # 权限检查
        self._check_permissions()
        
        # 执行特权操作
        return Response(message="特权操作完成")
```

---

## 📚 **最佳实践**

### **代码质量**

#### **代码规范**:
```python
"""
工具开发代码规范示例
"""

class WellDesignedTool(Tool):
    """
    良好设计的工具类示例
    
    这个工具演示了以下最佳实践:
    - 清晰的文档字符串
    - 完整的错误处理
    - 参数验证
    - 日志记录
    - 资源管理
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = self.agent.context.log
        self._initialize_resources()
    
    def _initialize_resources(self):
        """初始化资源"""
        try:
            # 资源初始化逻辑
            self.logger.info("工具资源初始化完成")
        except Exception as e:
            self.logger.error(f"资源初始化失败: {e}")
            raise
    
    async def execute(self, **kwargs):
        """
        执行工具主要功能
        
        Args:
            **kwargs: 工具参数
            
        Returns:
            Response: 执行结果
            
        Raises:
            ValueError: 参数验证失败
            RuntimeError: 执行过程中的运行时错误
        """
        self.logger.info(f"开始执行工具，参数: {kwargs}")
        
        try:
            # 1. 参数验证
            validated_params = self._validate_and_prepare_params(kwargs)
            
            # 2. 执行核心逻辑
            result = await self._execute_core_logic(validated_params)
            
            # 3. 结果处理
            formatted_result = self._format_result(result)
            
            self.logger.info("工具执行成功")
            return Response(message=formatted_result)
            
        except Exception as e:
            self.logger.error(f"工具执行失败: {e}")
            return Response(
                message=f"执行失败: {str(e)}",
                break_loop=False
            )
    
    def _validate_and_prepare_params(self, kwargs):
        """验证和准备参数"""
        # 参数验证逻辑
        return kwargs
    
    async def _execute_core_logic(self, params):
        """执行核心逻辑"""
        # 核心功能实现
        pass
    
    def _format_result(self, result):
        """格式化结果"""
        return str(result)
```

### **错误处理策略**

#### **分层错误处理**:
```python
class RobustTool(Tool):
    async def execute(self, **kwargs):
        try:
            return await self._safe_execute(**kwargs)
        except ValidationError as e:
            return self._handle_validation_error(e)
        except NetworkError as e:
            return self._handle_network_error(e)
        except Exception as e:
            return self._handle_unexpected_error(e)
    
    async def _safe_execute(self, **kwargs):
        """安全执行逻辑"""
        # 主要执行逻辑
        pass
    
    def _handle_validation_error(self, error):
        """处理验证错误"""
        return Response(
            message=f"参数验证失败: {error}",
            break_loop=False
        )
    
    def _handle_network_error(self, error):
        """处理网络错误"""
        return Response(
            message=f"网络请求失败: {error}，请检查网络连接",
            break_loop=False
        )
    
    def _handle_unexpected_error(self, error):
        """处理意外错误"""
        self.logger.exception("工具执行出现意外错误")
        return Response(
            message="工具执行出现意外错误，请联系技术支持",
            break_loop=False
        )
```

---

## 📝 **文档维护**

### **版本管理**

#### **变更日志**:
```markdown
# 工具变更日志

## v1.2.0 (2025-07-07)
### 新增
- 添加批量处理功能
- 支持自定义输出格式

### 改进
- 优化性能，提升50%执行速度
- 改进错误提示信息

### 修复
- 修复并发处理时的内存泄漏问题
- 修复特殊字符处理错误

## v1.1.0 (2025-06-01)
### 新增
- 添加缓存机制
- 支持异步处理

### 改进
- 优化参数验证逻辑
```

### **API文档**

#### **自动文档生成**:
```python
def generate_tool_docs(tool_class):
    """自动生成工具文档"""
    doc = f"""
# {tool_class.__name__}

## 描述
{tool_class.__doc__ or "暂无描述"}

## 方法

### execute(**kwargs)
{tool_class.execute.__doc__ or "暂无方法描述"}

## 示例
```python
tool = {tool_class.__name__}(agent, "tool_name", None, {{}}, "test")
result = await tool.execute(param1="value1")
```
"""
    return doc
```

---

## 🔮 **未来发展**

### **工具生态系统**
- **插件化架构**: 支持动态加载和卸载工具
- **工具市场**: 社区贡献的工具分享平台
- **版本管理**: 工具版本控制和依赖管理

### **智能化增强**
- **自适应参数**: 根据使用情况自动调整参数
- **性能预测**: 基于历史数据预测工具执行时间
- **智能推荐**: 基于上下文推荐最适合的工具

---

**文档维护**: Agent-Zero开发团队  
**技术支持**: 请参考项目GitHub仓库  
**最后更新**: 2025-07-07
