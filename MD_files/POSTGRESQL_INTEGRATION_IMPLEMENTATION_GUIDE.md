# PostgreSQL集成实施指南

## 📋 **实施概述**

**目标**: 为Agent-Zero集成PostgreSQL数据库  
**架构**: 混合存储 (PostgreSQL + FAISS)  
**实施周期**: 6-8周  
**风险等级**: 中等 (可控)  

---

## 🔧 **环境要求**

### **1. 系统环境要求**

#### **操作系统**
- ✅ **当前WSL环境**: 完全兼容
- ✅ **Ubuntu 20.04+**: 推荐
- ✅ **Windows 10/11**: 通过WSL2支持
- ✅ **macOS**: 原生支持
- ✅ **Docker**: 容器化部署

#### **硬件要求**
```
最低配置:
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB可用空间
- 网络: 稳定网络连接

推荐配置:
- CPU: 8核心+
- 内存: 16GB+ RAM
- 存储: 100GB+ SSD
- 网络: 高速网络连接
```

### **2. PostgreSQL环境要求**

#### **PostgreSQL版本**
- ✅ **PostgreSQL 14+**: 推荐 (支持所有特性)
- ✅ **PostgreSQL 13**: 兼容 (部分特性受限)
- ⚠️ **PostgreSQL 12**: 最低版本 (功能有限)

#### **必需扩展**
```sql
-- 向量搜索扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- UUID支持
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 全文搜索
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- JSON支持增强
CREATE EXTENSION IF NOT EXISTS btree_gin;
```

### **3. Python环境要求**

#### **Python版本**
- ✅ **Python 3.12+**: 当前使用 (完全兼容)
- ✅ **Python 3.11**: 兼容
- ⚠️ **Python 3.10**: 最低版本

#### **新增依赖包**
```txt
# 数据库连接
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1

# 向量数据库
pgvector==0.2.4
langchain-postgres==0.0.6

# 连接池
psycopg2-pool==1.1
asyncpg==0.29.0

# 数据迁移
pandas==2.1.4
numpy==1.24.3
```

---

## 📋 **详细实施步骤**

### **Phase 1: 环境准备 (第1周)**

#### **步骤1.1: PostgreSQL安装**

**WSL环境安装**:
```bash
# 更新包管理器
sudo apt update && sudo apt upgrade -y

# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib postgresql-client -y

# 启动PostgreSQL服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 验证安装
sudo -u postgres psql -c "SELECT version();"
```

**Docker安装** (推荐):
```bash
# 创建PostgreSQL容器
docker run --name agent-zero-postgres \
  -e POSTGRES_DB=agent_zero \
  -e POSTGRES_USER=agent_zero \
  -e POSTGRES_PASSWORD=your_secure_password \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  -d postgres:15

# 验证容器运行
docker ps | grep agent-zero-postgres
```

#### **步骤1.2: pgvector扩展安装**
```bash
# 方法1: 从源码编译
git clone --branch v0.5.1 https://github.com/pgvector/pgvector.git
cd pgvector
make
sudo make install

# 方法2: 使用包管理器 (Ubuntu)
sudo apt install postgresql-15-pgvector

# 方法3: Docker镜像 (推荐)
docker run --name agent-zero-postgres \
  -e POSTGRES_DB=agent_zero \
  -e POSTGRES_USER=agent_zero \
  -e POSTGRES_PASSWORD=your_secure_password \
  -p 5432:5432 \
  -d pgvector/pgvector:pg15
```

#### **步骤1.3: 数据库初始化**
```sql
-- 连接到数据库
psql -h localhost -U agent_zero -d agent_zero

-- 创建必需扩展
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- 验证扩展
\dx
```

### **Phase 2: 代码基础架构 (第2周)**

#### **步骤2.1: 添加依赖**
```bash
# 激活conda环境
conda activate AZ090

# 安装PostgreSQL相关包
pip install psycopg2-binary==2.9.9
pip install sqlalchemy==2.0.23
pip install alembic==1.13.1
pip install pgvector==0.2.4
pip install asyncpg==0.29.0

# 更新requirements.txt
echo "psycopg2-binary==2.9.9" >> requirements.txt
echo "sqlalchemy==2.0.23" >> requirements.txt
echo "alembic==1.13.1" >> requirements.txt
echo "pgvector==0.2.4" >> requirements.txt
echo "asyncpg==0.29.0" >> requirements.txt
```

#### **步骤2.2: 创建数据库配置**
```python
# python/helpers/database_config.py
import os
from typing import Optional
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession

class DatabaseConfig:
    def __init__(self):
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = int(os.getenv('DB_PORT', '5432'))
        self.database = os.getenv('DB_NAME', 'agent_zero')
        self.username = os.getenv('DB_USER', 'agent_zero')
        self.password = os.getenv('DB_PASSWORD', '')
        self.pool_size = int(os.getenv('DB_POOL_SIZE', '10'))
        self.max_overflow = int(os.getenv('DB_MAX_OVERFLOW', '20'))
    
    @property
    def sync_url(self) -> str:
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    @property
    def async_url(self) -> str:
        return f"postgresql+asyncpg://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    def create_sync_engine(self):
        return create_engine(
            self.sync_url,
            pool_size=self.pool_size,
            max_overflow=self.max_overflow,
            echo=os.getenv('DB_ECHO', 'false').lower() == 'true'
        )
    
    def create_async_engine(self):
        return create_async_engine(
            self.async_url,
            pool_size=self.pool_size,
            max_overflow=self.max_overflow,
            echo=os.getenv('DB_ECHO', 'false').lower() == 'true'
        )
```

#### **步骤2.3: 创建数据模型**
```python
# python/helpers/database_models.py
from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, Float, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
from pgvector.sqlalchemy import Vector
import uuid
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(255), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    settings = Column(JSON, default={})

class Conversation(Base):
    __tablename__ = 'conversations'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=True)
    title = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    metadata = Column(JSON, default={})

class Message(Base):
    __tablename__ = 'messages'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), nullable=False)
    role = Column(String(50), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON, default={})
    
    # 向量字段 (4096维，匹配Qwen3-Embedding-8B)
    embedding = Column(Vector(4096), nullable=True)

class Document(Base):
    __tablename__ = 'documents'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    source = Column(String(500), nullable=True)
    document_type = Column(String(100), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON, default={})
    
    # 向量字段
    embedding = Column(Vector(4096), nullable=True)

class SystemLog(Base):
    __tablename__ = 'system_logs'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    level = Column(String(20), nullable=False)  # INFO, WARNING, ERROR
    message = Column(Text, nullable=False)
    source = Column(String(100), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON, default={})
```

### **Phase 3: 数据访问层 (第3周)**

#### **步骤3.1: 创建数据访问层**
```python
# python/helpers/database_manager.py
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, update, delete
from .database_config import DatabaseConfig
from .database_models import Base, User, Conversation, Message, Document, SystemLog

class DatabaseManager:
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.async_engine = config.create_async_engine()
        self.sync_engine = config.create_sync_engine()
        self.async_session_factory = sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )
    
    async def create_tables(self):
        """创建所有表"""
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    async def save_conversation(self, conversation_data: Dict[str, Any]) -> str:
        """保存对话"""
        async with self.async_session_factory() as session:
            conversation = Conversation(**conversation_data)
            session.add(conversation)
            await session.commit()
            return str(conversation.id)
    
    async def save_message(self, message_data: Dict[str, Any]) -> str:
        """保存消息"""
        async with self.async_session_factory() as session:
            message = Message(**message_data)
            session.add(message)
            await session.commit()
            return str(message.id)
    
    async def search_messages_by_vector(self, query_vector: List[float], limit: int = 10) -> List[Message]:
        """基于向量搜索消息"""
        async with self.async_session_factory() as session:
            # 使用pgvector的余弦相似度搜索
            stmt = select(Message).order_by(
                Message.embedding.cosine_distance(query_vector)
            ).limit(limit)
            result = await session.execute(stmt)
            return result.scalars().all()
```

### **Phase 4: 集成现有系统 (第4-5周)**

#### **步骤4.1: 扩展Memory类**
```python
# python/helpers/hybrid_memory.py
from typing import List, Dict, Any, Optional
from .memory import Memory
from .database_manager import DatabaseManager
from .database_config import DatabaseConfig

class HybridMemory(Memory):
    def __init__(self, agent, db, memory_subdir: str = "default"):
        super().__init__(agent, db, memory_subdir)
        
        # 初始化PostgreSQL连接
        self.db_config = DatabaseConfig()
        self.db_manager = DatabaseManager(self.db_config)
    
    async def save_conversation_to_postgres(self, conversation_data: Dict[str, Any]):
        """保存对话到PostgreSQL"""
        return await self.db_manager.save_conversation(conversation_data)
    
    async def hybrid_search(self, query: str, use_postgres: bool = False, **kwargs):
        """混合搜索：FAISS + PostgreSQL"""
        
        # FAISS搜索 (保持原有高性能)
        faiss_results = await self.search_similarity_threshold(query, **kwargs)
        
        if use_postgres:
            # PostgreSQL向量搜索
            query_vector = await self._get_query_embedding(query)
            postgres_results = await self.db_manager.search_messages_by_vector(query_vector)
            
            # 合并结果
            return self._merge_search_results(faiss_results, postgres_results)
        
        return faiss_results
    
    async def _get_query_embedding(self, query: str) -> List[float]:
        """获取查询的向量表示"""
        embeddings_model = models.get_model(
            models.ModelType.EMBEDDING,
            self.agent.config.embeddings_model.provider,
            self.agent.config.embeddings_model.name,
            **self.agent.config.embeddings_model.kwargs,
        )
        return embeddings_model.embed_query(query)
```

### **Phase 5: 配置和部署 (第6周)**

#### **步骤5.1: 环境配置**
```bash
# .env文件添加数据库配置
echo "# PostgreSQL配置" >> .env
echo "DB_HOST=localhost" >> .env
echo "DB_PORT=5432" >> .env
echo "DB_NAME=agent_zero" >> .env
echo "DB_USER=agent_zero" >> .env
echo "DB_PASSWORD=your_secure_password" >> .env
echo "DB_POOL_SIZE=10" >> .env
echo "DB_MAX_OVERFLOW=20" >> .env
echo "DB_ECHO=false" >> .env
```

#### **步骤5.2: 数据库迁移脚本**
```python
# migrate_to_postgres.py
import asyncio
from python.helpers.database_config import DatabaseConfig
from python.helpers.database_manager import DatabaseManager

async def migrate_existing_data():
    """迁移现有数据到PostgreSQL"""
    
    config = DatabaseConfig()
    db_manager = DatabaseManager(config)
    
    # 创建表
    await db_manager.create_tables()
    
    # 迁移现有记忆数据
    # TODO: 实现具体的迁移逻辑
    
    print("✅ 数据迁移完成")

if __name__ == "__main__":
    asyncio.run(migrate_existing_data())
```

---

## 🧪 **测试和验证**

### **单元测试**
```python
# tests/test_postgres_integration.py
import pytest
import asyncio
from python.helpers.database_config import DatabaseConfig
from python.helpers.database_manager import DatabaseManager

@pytest.mark.asyncio
async def test_database_connection():
    config = DatabaseConfig()
    db_manager = DatabaseManager(config)
    
    # 测试连接
    async with db_manager.async_session_factory() as session:
        result = await session.execute("SELECT 1")
        assert result.scalar() == 1

@pytest.mark.asyncio
async def test_save_conversation():
    config = DatabaseConfig()
    db_manager = DatabaseManager(config)
    
    conversation_data = {
        "title": "测试对话",
        "metadata": {"test": True}
    }
    
    conv_id = await db_manager.save_conversation(conversation_data)
    assert conv_id is not None
```

### **性能测试**
```bash
# 创建性能测试脚本
python performance_test.py --test-type=postgres --duration=60
```

---

## 📊 **监控和维护**

### **数据库监控**
```sql
-- 监控查询
SELECT * FROM pg_stat_activity WHERE datname = 'agent_zero';

-- 监控表大小
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public';

-- 监控向量查询性能
EXPLAIN ANALYZE SELECT * FROM messages ORDER BY embedding <-> '[0,1,2,...]' LIMIT 10;
```

### **备份策略**
```bash
# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backup/agent_zero"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份
pg_dump -h localhost -U agent_zero agent_zero > "$BACKUP_DIR/agent_zero_$DATE.sql"

# 压缩备份
gzip "$BACKUP_DIR/agent_zero_$DATE.sql"

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

---

## ⚠️ **注意事项和最佳实践**

### **安全考虑**
- 🔒 使用强密码
- 🛡️ 配置防火墙规则
- 🔐 启用SSL连接
- 👤 最小权限原则

### **性能优化**
- 📊 创建适当的索引
- 🔄 使用连接池
- 📈 监控查询性能
- 💾 定期维护数据库

### **数据一致性**
- 🔄 实现事务管理
- 📝 记录操作日志
- 🔍 定期数据校验
- 🔙 制定回滚策略

这个实施方案提供了完整的PostgreSQL集成路径，可以根据您的具体需求进行调整！🚀
