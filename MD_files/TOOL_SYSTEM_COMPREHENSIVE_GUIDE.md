# Agent-Zero 工具系统完整指南

## 📋 **文档概述**

本文档详细说明了Agent-Zero项目的工具调用流程、工具使用策略、工具选择机制以及最佳实践。

**文档版本**: v1.0  
**更新日期**: 2025-07-07  
**适用版本**: Agent-Zero 0.8.7+  

---

## 🏗️ **工具系统架构**

### **核心组件**

```
Agent-Zero 工具系统
├── 工具发现与加载 (Tool Discovery & Loading)
├── 工具选择器 (Tool Selector)
├── 系统提示扩展 (System Prompt Extensions)
├── 工具执行引擎 (Tool Execution Engine)
└── MCP工具集成 (MCP Tool Integration)
```

### **工具分类**

#### **1. 核心工具 (Core Tools)**
- `response` - 直接回答用户
- `search_engine` - 基础搜索
- `browser` - 网页浏览
- `code_exe` - 代码执行
- `memory` - 记忆管理
- `scheduler` - 任务调度

#### **2. 增强工具 (Enhanced Tools)**
- `enhanced_search_engine` - 深度搜索引擎
- `sequential_thinking` - 序列化思维工具
- `web_crawler` - 智能网页爬虫
- `code_generator` - 智能代码生成工具

#### **3. MCP工具 (MCP Tools)**
- `excel-stdio` - Excel文件处理
- `context7` - 代码文档工具

---

## 🔄 **工具调用流程**

### **完整调用链路**

```mermaid
graph TD
    A[用户输入] --> B[系统提示生成]
    B --> C[工具选择器分析]
    C --> D[LLM决策]
    D --> E[工具调用]
    E --> F[MCP工具检查]
    F --> G[本地工具加载]
    G --> H[工具执行]
    H --> I[结果返回]

    subgraph "系统提示生成"
        B1[主要系统提示]
        B2[工具描述加载]
        B3[增强工具指导]
        B4[动态工具推荐]
    end

    subgraph "工具选择器分析"
        C1[关键词提取]
        C2[置信度计算]
        C3[工具推荐生成]
    end

    subgraph "工具执行"
        H1[参数验证]
        H2[依赖检查]
        H3[实际执行]
        H4[结果处理]
    end
```

### **详细流程说明**

#### **阶段1: 输入处理与分析**
1. **用户输入接收**
   - 接收用户消息
   - 提取文本内容
   - 初始化上下文

2. **系统提示生成**
   ```python
   # 系统提示组成
   system_prompt = [
       main_prompt,           # 主要系统提示
       tools_prompt,          # 工具描述
       mcp_tools_prompt,      # MCP工具描述
       enhanced_tools_guide,  # 增强工具指导
       tool_recommendations  # 工具推荐
   ]
   ```

3. **工具选择器分析**
   ```python
   # 关键词分析
   analysis = tool_selector.analyze_user_input(user_input)
   
   # 生成推荐
   recommendations = tool_selector.generate_recommendation_message(analysis)
   ```

#### **阶段2: LLM决策**
1. **工具选择决策**
   - LLM基于系统提示选择工具
   - 考虑用户意图和上下文
   - 生成工具调用JSON

2. **工具调用格式**
   ```json
   {
     "thoughts": ["分析用户需求", "选择合适工具"],
     "tool_name": "tool_name",
     "tool_args": {
       "param1": "value1",
       "param2": "value2"
     }
   }
   ```

#### **阶段3: 工具执行**
1. **工具查找优先级**
   ```python
   # 1. 优先检查MCP工具
   mcp_tool = mcp_config.get_tool(tool_name)
   
   # 2. 回退到本地工具
   if not mcp_tool:
       tool = agent.get_tool(tool_name)
   ```

2. **本地工具加载**
   ```python
   # 文件名映射
   file_path = f"python/tools/{tool_name}.py"
   
   # 类加载
   classes = load_classes_from_folder("python/tools", f"{tool_name}.py", Tool)
   tool_class = classes[0] if classes else Unknown
   ```

3. **工具实例化与执行**
   ```python
   # 实例化
   tool_instance = tool_class(agent=agent, name=tool_name, ...)
   
   # 执行
   result = await tool_instance.execute(**tool_args)
   ```

---

## 🎯 **工具选择策略**

### **智能工具选择器 (Tool Selector)**

#### **核心机制**
- **关键词匹配**: 基于用户输入的关键词分析
- **置信度评分**: 计算工具推荐的置信度
- **多维度分析**: 考虑语义、上下文和用户意图

#### **工具触发条件**

##### **1. Enhanced Search Engine**
**触发关键词**:
- 中文: 深入、详细、全面、研究、深度、彻底
- 英文: enhance, enhanced, deep, detailed, comprehensive

**触发条件**:
```python
confidence_threshold = 0.7  # 70%置信度
if deep_search_score >= confidence_threshold:
    recommend_enhanced_search_engine()
```

**使用场景**:
- 需要深度研究的复杂问题
- 要求全面信息收集
- 学术或专业资料查找

##### **2. Sequential Thinking**
**触发关键词**:
- 中文: 系统、结构、分步、逻辑、分析、框架
- 英文: systematic, structured, logical, analysis, framework

**使用场景**:
- 复杂问题的系统性分析
- 需要结构化思考的场景
- 多步骤逻辑推理

##### **3. Web Crawler**
**触发关键词**:
- 中文: 爬取、抓取、采集、收集、获取、提取
- 英文: crawl, scrape, extract, collect, gather

**使用场景**:
- 网页内容批量提取
- 结构化数据采集
- 网站信息收集

##### **4. Code Generator**
**触发关键词**:
- 中文: 代码生成、编程、算法、实现、LeetCode
- 英文: code generation, programming, algorithm, implement

**使用场景**:
- 算法问题求解
- 代码实现需求
- 测试驱动开发
- 编程挑战解决

### **工具选择优先级**

#### **明确工具指定**
```
用户明确提及工具名 → 直接使用指定工具
例: "使用enhanced_search_engine搜索..."
```

#### **关键词触发**
```
高置信度关键词匹配 → 推荐对应专用工具
例: "深入研究" → enhanced_search_engine
```

#### **默认工具选择**
```
无明确指向 → 使用标准工具
例: 普通搜索 → search_engine
```

---

## 📚 **工具使用指南**

### **核心工具详解**

#### **1. Response Tool**
**用途**: 直接回答用户问题
**适用场景**:
- 简单问答
- 知识解释
- 直接信息提供

**调用示例**:
```json
{
  "tool_name": "response",
  "tool_args": {
    "text": "这是直接回答的内容"
  }
}
```

#### **2. Search Engine**
**用途**: 基础网络搜索
**适用场景**:
- 一般信息查询
- 新闻资讯
- 基础事实查找

**调用示例**:
```json
{
  "tool_name": "search_engine",
  "tool_args": {
    "query": "搜索关键词"
  }
}
```

#### **3. Browser Tool**
**用途**: 网页浏览和交互
**适用场景**:
- 访问特定网页
- 网页截图
- 简单网页交互

**调用示例**:
```json
{
  "tool_name": "browser",
  "tool_args": {
    "url": "https://example.com",
    "action": "screenshot"
  }
}
```

#### **4. Code Execution**
**用途**: 代码执行和验证
**适用场景**:
- 代码测试
- 计算验证
- 脚本运行

**调用示例**:
```json
{
  "tool_name": "code_exe",
  "tool_args": {
    "language": "python",
    "code": "print('Hello World')"
  }
}
```

### **增强工具详解**

#### **1. Enhanced Search Engine**
**核心特性**:
- 多轮搜索策略
- 结果质量评估
- 智能摘要生成

**使用方法**:
```json
{
  "tool_name": "enhanced_search_engine",
  "tool_args": {
    "query": "深度研究主题",
    "max_results": 10,
    "quality_threshold": 0.8
  }
}
```

**最佳实践**:
- 用于复杂研究问题
- 需要高质量信息源
- 要求综合性分析

#### **2. Sequential Thinking**
**核心特性**:
- 5步结构化分析
- 问题系统性分解
- 逻辑推理链条

**使用方法**:
```json
{
  "tool_name": "sequential_thinking",
  "tool_args": {
    "problem": "复杂问题描述",
    "analysis_depth": "deep"
  }
}
```

**分析框架**:
1. 问题理解与分解
2. 信息收集与整理
3. 多角度分析
4. 逻辑推理
5. 结论整合

#### **3. Web Crawler**
**核心特性**:
- LLM自主生成爬取策略
- 智能网站类型识别
- 多格式内容提取

**使用方法**:
```json
{
  "tool_name": "web_crawler",
  "tool_args": {
    "url": "目标网站URL",
    "extraction_type": "content",
    "max_pages": 10
  }
}
```

**爬取策略**:
- 自动识别网站结构
- 智能选择提取方法
- 处理动态内容

#### **4. Code Generator**
**核心特性**:
- 自动测试用例生成
- 智能代码实现
- 迭代改进机制

**使用方法**:
```json
{
  "tool_name": "code_generator",
  "tool_args": {
    "problem": "编程问题描述",
    "max_iterations": 3,
    "num_examples": 1
  }
}
```

**工作流程**:
1. 问题分析
2. 测试用例生成
3. 代码实现
4. 测试验证
5. 迭代改进

---

## ⚙️ **系统配置**

### **工具注册机制**

#### **文件结构**
```
prompts/default/
├── agent.system.tools.md              # 主工具注册文件
├── agent.system.tool.response.md      # 各工具描述文件
├── agent.system.tool.search_engine.md
├── agent.system.tool.enhanced_search_engine.md
├── agent.system.tool.sequential_thinking.md
├── agent.system.tool.web_crawler.md
├── agent.system.tool.code_generator.md
└── ...
```

#### **工具注册**
```markdown
<!-- agent.system.tools.md -->
## Tools available:

{{ include './agent.system.tool.response.md' }}
{{ include './agent.system.tool.search_engine.md' }}
{{ include './agent.system.tool.enhanced_search_engine.md' }}
{{ include './agent.system.tool.sequential_thinking.md' }}
{{ include './agent.system.tool.web_crawler.md' }}
{{ include './agent.system.tool.code_generator.md' }}
```

### **系统提示扩展**

#### **扩展文件**
```
python/extensions/system_prompt/
├── _10_system_prompt.py              # 基础系统提示
├── _15_enhanced_tools_guide.py       # 增强工具指导
└── _16_new_tool_recommendations.py   # 工具推荐扩展
```

#### **扩展执行顺序**
1. 基础系统提示加载
2. 工具描述集成
3. 增强工具指导添加
4. 动态工具推荐生成

### **工具选择器配置**

#### **配置文件**: `python/helpers/tool_selector.py`

#### **关键参数**:
```python
confidence_threshold = 0.7  # 推荐置信度阈值
keyword_weights = {
    'direct_match': 0.3,    # 直接匹配权重
    'partial_match': 0.2,   # 部分匹配权重
    'phrase_match': 0.4     # 短语匹配权重
}
```

---

## 🔧 **开发与扩展**

### **添加新工具**

#### **步骤1: 创建工具类**
```python
# python/tools/new_tool.py
from python.helpers.tool import Tool, Response

class NewTool(Tool):
    async def execute(self, **kwargs):
        # 工具实现逻辑
        return Response(
            message="工具执行结果",
            break_loop=False
        )
```

#### **步骤2: 创建工具描述**
```markdown
<!-- prompts/default/agent.system.tool.new_tool.md -->
## New Tool

工具描述和使用方法...

**使用方法**：
```json
{
  "tool_name": "new_tool",
  "parameters": {
    "param1": "value1"
  }
}
```

#### **步骤3: 注册工具**
```markdown
<!-- 在 agent.system.tools.md 中添加 -->
{{ include './agent.system.tool.new_tool.md' }}
```

#### **步骤4: 更新工具选择器**
```python
# 在 tool_selector.py 中添加
self.new_tool_keywords = [
    "关键词1", "关键词2", ...
]

# 添加分析逻辑
new_tool_score = self._calculate_keyword_score(
    user_input_lower, self.new_tool_keywords
)
```

### **工具调试**

#### **调试工具**
```python
# 测试工具加载
from python.helpers.extract_tools import load_classes_from_folder
from python.helpers.tool import Tool

classes = load_classes_from_folder("python/tools", "tool_name.py", Tool)
print(f"加载的工具类: {classes}")
```

#### **测试工具选择器**
```python
from python.helpers.tool_selector import tool_selector

analysis = tool_selector.analyze_user_input("测试输入")
print(f"分析结果: {analysis}")
```

---

## 🔍 **MCP工具集成**

### **MCP (Model Context Protocol) 概述**
MCP是一个标准化协议，用于连接AI助手与外部工具和数据源。

#### **MCP工具特点**:
- **标准化接口**: 统一的工具调用协议
- **外部集成**: 连接第三方服务和工具
- **动态发现**: 运行时发现可用工具
- **安全隔离**: 独立的执行环境

### **当前集成的MCP工具**

#### **1. Excel-STDIO**
**功能**: Excel文件处理
**用途**:
- 读取Excel文件
- 数据分析和处理
- 表格操作

**调用示例**:
```json
{
  "tool_name": "excel-stdio",
  "tool_args": {
    "action": "read",
    "file_path": "data.xlsx"
  }
}
```

#### **2. Context7**
**功能**: 代码文档工具
**用途**:
- 代码分析
- 文档生成
- 代码理解

### **MCP工具调用流程**
```python
# 1. 检查MCP工具可用性
mcp_tool = mcp_config.get_tool(tool_name)

# 2. 如果MCP工具存在，优先使用
if mcp_tool:
    result = await mcp_tool.execute(**tool_args)
else:
    # 3. 回退到本地工具
    local_tool = agent.get_tool(tool_name)
    result = await local_tool.execute(**tool_args)
```

---

## 📊 **性能优化**

### **工具加载优化**
- **延迟加载**: 只在需要时加载工具类
- **缓存机制**: 缓存已加载的工具类
- **并行处理**: 支持工具并行执行
- **内存管理**: 及时释放未使用的工具实例

### **选择器优化**
- **关键词索引**: 建立关键词快速查找索引
- **置信度缓存**: 缓存常用输入的分析结果
- **算法优化**: 优化关键词匹配算法
- **预计算**: 预计算常用关键词组合

### **系统提示优化**
- **模板缓存**: 缓存系统提示模板
- **增量更新**: 只更新变化的部分
- **压缩传输**: 压缩大型提示内容
- **分块加载**: 按需加载提示片段

---

## 🚨 **故障排除**

### **常见问题**

#### **工具未找到**
```
Tool 'tool_name' not found
```
**解决方案**:
1. 检查工具文件是否存在
2. 验证工具类名是否正确
3. 确认工具已注册到系统提示

#### **工具调用失败**
```
Tool execution failed
```
**解决方案**:
1. 检查工具参数是否正确
2. 验证工具依赖是否安装
3. 查看详细错误日志

#### **工具选择不准确**
**解决方案**:
1. 调整关键词列表
2. 修改置信度阈值
3. 优化工具描述

### **调试命令**
```bash
# 查看工具加载状态
python -c "from python.helpers.extract_tools import load_classes_from_folder; print(load_classes_from_folder('python/tools', '*.py', object))"

# 测试工具选择器
python -c "from python.helpers.tool_selector import tool_selector; print(tool_selector.analyze_user_input('测试输入'))"

# 检查系统提示
python -c "from agent import Agent; from initialize import initialize_agent; agent = Agent(0, initialize_agent()); print(len(agent.read_prompt('agent.system.tools.md')))"

# 测试特定工具
python -c "from agent import Agent; from initialize import initialize_agent; agent = Agent(0, initialize_agent()); tool = agent.get_tool('tool_name', None, {}, 'test'); print(tool.__class__.__name__)"

# 检查MCP工具
python -c "from python.helpers.mcp_config import mcp_config; print(mcp_config.get_available_tools())"
```

### **日志分析**
```bash
# 查看最新日志
tail -f logs/log_$(date +%Y%m%d)_*.html

# 搜索工具调用
grep -A 5 -B 5 "tool_name" logs/log_*.html

# 分析工具选择
grep "Tool.*not found\|Available tools" logs/log_*.html

# 检查错误信息
grep -i "error\|exception\|failed" logs/log_*.html
```

---

## 📈 **最佳实践**

### **工具使用原则**
1. **明确性优于智能性**: 明确指定工具名比依赖自动选择更可靠
2. **专用工具优于通用工具**: 针对特定场景使用专用工具
3. **简单工具优于复杂工具**: 能用简单工具解决的不用复杂工具

### **开发建议**
1. **工具单一职责**: 每个工具专注解决特定问题
2. **参数验证**: 严格验证输入参数
3. **错误处理**: 提供清晰的错误信息
4. **文档完整**: 提供详细的使用文档

### **性能建议**
1. **避免重复工具调用**: 缓存工具执行结果
2. **合理设置超时**: 防止工具执行时间过长
3. **资源管理**: 及时释放工具占用的资源

---

## 📝 **更新日志**

### **v1.0 (2025-07-07)**
- 初始版本发布
- 包含完整的工具系统说明
- 添加4个增强工具的详细文档
- 提供开发和故障排除指南

---

## 🔗 **相关文档**

- [工具注册分析报告](./TOOL_REGISTRATION_ANALYSIS_REPORT.md)
- [Code Generator工具修复报告](./CODE_GENERATOR_TOOL_FIX_REPORT.md)
- [调度器问题分析](./SCHEDULER_ISSUE_ANALYSIS.md)

---

**文档维护**: Agent-Zero开发团队  
**技术支持**: 请参考项目GitHub仓库  
**最后更新**: 2025-07-07
