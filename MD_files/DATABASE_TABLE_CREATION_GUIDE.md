# 数据库表格创建指导手册

## 🎯 目标

通过优化prompt让LLM正确使用MCP服务器建立数据库表格，避免常见的依赖关系错误。

## 📋 已实施的解决方案

### **1. 创建专门的数据库操作指南**

**文件位置**: `prompts/default/agent.system.database_operations.md`

**核心内容**:
- 表创建顺序原则
- 操作前检查原则  
- 分步执行原则
- 标准操作流程
- 常见错误避免
- MCP工具使用指南

### **2. 集成到系统提示**

**修改文件**: `prompts/default/agent.system.main.md`
- 添加了数据库操作指南的引用
- 确保LLM在处理数据库操作时能看到这些指导

## 🔧 具体改进内容

### **核心原则强化**

1. **表创建顺序原则**
   - 先创建基础表，再创建依赖表
   - 检查表是否存在，避免重复创建
   - 外键引用的表必须先存在

2. **操作前检查原则**
   - 使用`list-tables`检查现有表结构
   - 使用`describe-table`了解表结构
   - 确认依赖关系后再执行CREATE操作

3. **分步执行原则**
   - 一次只创建一个表
   - 按依赖顺序逐步创建
   - 每步验证成功后再继续

### **标准操作流程模板**

```json
// 步骤1: 环境检查
{
    "thoughts": ["检查当前数据库中已有的表"],
    "tool_name": "postgres_basic.list-tables",
    "tool_args": {"schema_name": "public"}
}

// 步骤2: 创建基础表（无外键依赖）
{
    "thoughts": ["创建上市公司基础信息表，无外键依赖"],
    "tool_name": "postgres_expert.execute_sql",
    "tool_args": {
        "sql": "CREATE TABLE IF NOT EXISTS listed_companies (stock_code VARCHAR(10) PRIMARY KEY, company_name VARCHAR(100) NOT NULL, industry VARCHAR(50));"
    }
}

// 步骤3: 验证基础表创建
{
    "thoughts": ["验证基础表是否创建成功"],
    "tool_name": "postgres_basic.describe-table",
    "tool_args": {"table_name": "listed_companies"}
}

// 步骤4: 创建依赖表
{
    "thoughts": ["现在可以安全创建引用listed_companies的财务数据表"],
    "tool_name": "postgres_expert.execute_sql",
    "tool_args": {
        "sql": "CREATE TABLE IF NOT EXISTS financial_data (id SERIAL PRIMARY KEY, stock_code VARCHAR(10) REFERENCES listed_companies(stock_code), report_date DATE NOT NULL);"
    }
}
```

## 🚀 使用方法

### **方法1: 自动生效**
- 重启Agent-Zero后，新的数据库操作指南会自动加载到系统提示中
- LLM在处理数据库相关请求时会自动遵循这些原则

### **方法2: 明确指导**
用户可以在请求中明确提及：
```
"请按照数据库操作最佳实践，分步骤创建A股财务数据表"
```

### **方法3: 具体化请求**
```
"首先检查现有表结构，然后按依赖顺序创建：1)上市公司基础信息表 2)财务数据表"
```

## 📊 预期效果

### **改进前的问题**
```sql
-- ❌ 错误：直接引用不存在的表
CREATE TABLE financial_data (
    stock_code VARCHAR(10) REFERENCES listed_companies(stock_code)  -- 表不存在
);
-- Error: 关系 "listed_companies" 不存在
```

### **改进后的正确流程**
```sql
-- ✅ 步骤1：检查现有表
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- ✅ 步骤2：创建基础表
CREATE TABLE IF NOT EXISTS listed_companies (
    stock_code VARCHAR(10) PRIMARY KEY,
    company_name VARCHAR(100) NOT NULL
);

-- ✅ 步骤3：验证创建成功
\d listed_companies

-- ✅ 步骤4：创建依赖表
CREATE TABLE IF NOT EXISTS financial_data (
    stock_code VARCHAR(10) REFERENCES listed_companies(stock_code),
    report_date DATE NOT NULL
);
```

## 🔍 验证方法

### **测试场景1: 创建A股财务数据库**
用户请求：
```
"在PostgreSQL中创建A股财务数据库，包含公司信息表和财务数据表"
```

**预期行为**:
1. LLM首先检查现有表结构
2. 创建`listed_companies`基础表
3. 验证基础表创建成功
4. 创建`financial_data`依赖表
5. 添加索引和约束

### **测试场景2: 复杂表结构创建**
用户请求：
```
"创建包含用户、订单、订单详情的电商数据库"
```

**预期行为**:
1. 按依赖顺序：users → orders → order_items
2. 每步验证成功后继续
3. 正确处理外键关系

## 💡 进一步优化建议

### **1. 添加错误恢复机制**
```markdown
如果遇到表创建错误：
1. 分析错误原因
2. 检查依赖关系
3. 调整创建顺序
4. 重新执行
```

### **2. 增强验证步骤**
```markdown
每次创建后验证：
1. 表是否存在
2. 字段是否正确
3. 约束是否生效
4. 索引是否创建
```

### **3. 提供回滚机制**
```markdown
如果创建失败：
1. 记录已创建的对象
2. 提供清理脚本
3. 支持重新开始
```

## ✅ 成功标准

实施成功的标志：
1. ✅ LLM能正确识别表依赖关系
2. ✅ 按正确顺序创建表
3. ✅ 每步都进行验证
4. ✅ 避免"关系不存在"错误
5. ✅ 生成完整可用的数据库结构

## 🎯 总结

通过添加专门的数据库操作指南到系统提示中，LLM现在具备了：

1. **系统性思维**：理解表创建的依赖关系
2. **分步执行能力**：不再尝试一次性创建复杂结构
3. **验证意识**：每步都会检查执行结果
4. **错误预防**：主动避免常见的数据库操作错误

这将显著提高LLM使用MCP服务器进行数据库操作的成功率和可靠性。
