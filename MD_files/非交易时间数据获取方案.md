# 非交易时间数据获取方案

## 问题分析

### 当前项目的数据获取方式
项目目前主要使用**实时行情API**来获取股票数据：

```python
# 当前使用的API端点
endpoint = '/api/v1/real_time_quotation'

# 获取的指标
indicators = "open,high,low,latest,preClose,volume,amount,turnoverRatio,pb,pe_ttm"
```

### 非交易时间的问题
1. **实时行情API在非交易时间可能返回空数据**
2. **`latest`（最新价）在非交易时间无法更新**
3. **成交量、成交额等实时指标为0或空值**
4. **用户在非交易时间查询股票会得到不完整的信息**

## 解决方案

根据同花顺iFinD API用户手册，有以下几种API可以在非交易时间获取数据：

### 1. 历史行情API（推荐用于非交易时间）

**API端点**: `/api/v1/cmd_history_quotation`

**优势**:
- ✅ 可以获取任意历史日期的完整行情数据
- ✅ 包含开盘价、收盘价、最高价、最低价等完整信息
- ✅ 在非交易时间仍能返回最近交易日的数据
- ✅ 数据稳定可靠

**可获取的指标**:
```
preClose     - 前收盘价
open         - 开盘价  
high         - 最高价
low          - 最低价
close        - 收盘价 (非交易时间的"最新价")
volume       - 成交量
amount       - 成交额
turnoverRatio - 换手率
pe_ttm       - 市盈率(TTM)
pb           - 市净率
```

**示例请求**:
```python
params = {
    "codes": "000001.SZ,600519.SH",
    "indicators": "preClose,open,high,low,close,volume,amount,turnoverRatio,pe_ttm,pb",
    "startdate": "2024-12-13",  # 最近交易日
    "enddate": "2024-12-13"     # 同一天
}
```

### 2. 基础数据API（用于基本面数据）

**API端点**: `/api/v1/basic_data_service`

**优势**:
- ✅ 提供基本面数据，不受交易时间影响
- ✅ 包含总股本、总市值、ROE等重要指标
- ✅ 数据相对稳定，更新频率较低

**可获取的指标**:
```
totalShares   - 总股本
totalCapital  - 总市值
mv           - 流通市值
roe          - 净资产收益率
roa          - 总资产收益率
eps          - 每股收益
bps          - 每股净资产
```

### 3. 数据序列API（用于时间序列分析）

**API端点**: `/api/v1/data_sequence`

**优势**:
- ✅ 可以获取指定时间范围的历史数据序列
- ✅ 适合技术分析和趋势分析
- ✅ 支持多种时间周期（日、周、月等）

## 实施方案

### 方案一：智能API选择（推荐）

根据当前时间和交易状态，自动选择最合适的API：

```python
async def get_stock_data_smart(self, codes: str, indicators: str = "") -> Dict[str, Any]:
    """智能获取股票数据 - 根据交易时间自动选择API"""
    
    # 1. 检查当前是否为交易时间
    if self._is_trading_time():
        # 交易时间：使用实时行情API
        return await self.get_real_time_quotation(codes, indicators)
    else:
        # 非交易时间：使用历史行情API获取最近交易日数据
        latest_trading_date = self._get_latest_trading_date()
        return await self.get_history_quotation(
            codes, 
            latest_trading_date, 
            latest_trading_date, 
            indicators
        )

def _is_trading_time(self) -> bool:
    """判断当前是否为交易时间"""
    now = datetime.now()
    
    # 检查是否为工作日（周一到周五）
    if now.weekday() >= 5:  # 周六、周日
        return False
    
    # 检查是否在交易时间段内
    current_time = now.time()
    morning_start = time(9, 30)   # 上午9:30
    morning_end = time(11, 30)    # 上午11:30
    afternoon_start = time(13, 0) # 下午13:00
    afternoon_end = time(15, 0)   # 下午15:00
    
    return (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end)

def _get_latest_trading_date(self) -> str:
    """获取最近的交易日"""
    today = datetime.now()
    
    # 如果是周末，回退到周五
    if today.weekday() == 5:  # 周六
        latest_date = today - timedelta(days=1)  # 周五
    elif today.weekday() == 6:  # 周日
        latest_date = today - timedelta(days=2)  # 周五
    else:
        # 工作日
        if self._is_trading_time():
            latest_date = today  # 当前交易日
        else:
            # 非交易时间，使用前一个交易日
            if today.time() < time(9, 30):
                # 早于开盘时间，使用前一个交易日
                latest_date = today - timedelta(days=1)
                if latest_date.weekday() >= 5:  # 如果是周末
                    latest_date = today - timedelta(days=3)  # 回到周五
            else:
                # 盘后时间，使用当天
                latest_date = today
    
    return latest_date.strftime('%Y-%m-%d')
```

### 方案二：数据融合策略

结合多个API的数据，提供最完整的信息：

```python
async def get_comprehensive_stock_data(self, codes: str) -> Dict[str, Any]:
    """获取综合股票数据 - 融合实时和历史数据"""
    
    results = {}
    
    try:
        # 1. 尝试获取实时数据
        real_time_data = await self.get_real_time_quotation(codes, "latest,volume,amount")
        
        # 2. 获取历史数据作为备份
        latest_date = self._get_latest_trading_date()
        history_data = await self.get_history_quotation(
            codes, latest_date, latest_date, 
            "open,high,low,close,volume,amount,turnoverRatio"
        )
        
        # 3. 获取基础数据
        basic_data = await self.get_basic_data(codes, "totalShares,totalCapital,pe_ttm,pb")
        
        # 4. 数据融合
        results = self._merge_data_sources(real_time_data, history_data, basic_data)
        
    except Exception as e:
        # 如果实时数据失败，至少返回历史数据
        results = history_data
    
    return results
```

### 方案三：缓存策略

对于非交易时间，使用缓存的最近交易日数据：

```python
class FinancialDataCache:
    """金融数据缓存管理"""
    
    def __init__(self):
        self.cache = {}
        self.cache_expiry = {}
    
    async def get_cached_or_fetch(self, codes: str, indicators: str) -> Dict[str, Any]:
        """获取缓存数据或重新获取"""
        cache_key = f"{codes}_{indicators}"
        
        # 检查缓存是否有效
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        # 缓存失效，重新获取数据
        if self._is_trading_time():
            data = await self.get_real_time_quotation(codes, indicators)
        else:
            # 非交易时间，获取最近交易日数据
            latest_date = self._get_latest_trading_date()
            data = await self.get_history_quotation(codes, latest_date, latest_date, indicators)
        
        # 更新缓存
        self.cache[cache_key] = data
        self.cache_expiry[cache_key] = self._get_cache_expiry_time()
        
        return data
    
    def _get_cache_expiry_time(self) -> datetime:
        """获取缓存过期时间"""
        if self._is_trading_time():
            # 交易时间：缓存5分钟
            return datetime.now() + timedelta(minutes=5)
        else:
            # 非交易时间：缓存到下一个交易日开盘
            next_trading_day = self._get_next_trading_day()
            return datetime.combine(next_trading_day, time(9, 30))
```

## 实施建议

### 1. 立即实施（高优先级）
- ✅ 在`financial_data_tool.py`中添加交易时间判断逻辑
- ✅ 修改`get_real_time_quotation`方法，非交易时间自动切换到历史行情API
- ✅ 更新数据格式化逻辑，正确处理历史数据的字段映射

### 2. 中期优化（中优先级）
- 🔄 实施数据融合策略，提供更完整的股票信息
- 🔄 添加缓存机制，提高非交易时间的响应速度
- 🔄 优化错误处理，提供更友好的用户提示

### 3. 长期改进（低优先级）
- 📅 集成交易日历API，准确判断节假日
- 📅 添加盘前盘后数据支持
- 📅 实现多市场（A股、港股、美股）的时区处理

## 代码修改示例

### 修改`financial_api_client.py`

```python
async def get_real_time_quotation(self, codes: str, indicators: str = "") -> Dict[str, Any]:
    """获取实时行情数据（智能切换）"""
    if not indicators:
        indicators = "open,high,low,latest,preClose,volume,amount,turnoverRatio,pb,pe_ttm"
    
    # 智能选择API
    if self._is_trading_time():
        # 交易时间：使用实时行情API
        params = {
            "codes": codes,
            "indicators": indicators
        }
        return await self._make_request('/api/v1/real_time_quotation', params)
    else:
        # 非交易时间：使用历史行情API
        latest_date = self._get_latest_trading_date()
        # 将latest替换为close，因为历史API没有latest字段
        historical_indicators = indicators.replace('latest', 'close')
        
        return await self.get_history_quotation(codes, latest_date, latest_date, historical_indicators)
```

### 修改`financial_data_tool.py`

```python
def _format_real_time_result(self, result: Dict[str, Any], codes: str) -> str:
    """格式化实时行情结果（兼容历史数据）"""
    if result.get('errorcode') != 0:
        return f"❌ 未获取到数据: {result.get('reason', '未知错误')}"
    
    # 检测数据来源
    is_historical = 'startdate' in str(result) or 'close' in str(result)
    data_source = "历史行情" if is_historical else "实时行情"
    
    # 字段映射（历史数据使用close，实时数据使用latest）
    price_field = 'close' if is_historical else 'latest'
    
    # ... 其余格式化逻辑保持不变，但使用动态字段名
```

## 总结

通过实施智能API选择策略，项目可以：

1. **解决非交易时间数据获取问题** - 自动切换到历史行情API
2. **提供一致的用户体验** - 无论何时查询都能获得有效数据
3. **保持数据的准确性** - 使用最近交易日的收盘数据作为"当前价格"
4. **提高系统可靠性** - 减少因交易时间限制导致的查询失败

这个方案既解决了当前的问题，又为未来的功能扩展奠定了基础。
