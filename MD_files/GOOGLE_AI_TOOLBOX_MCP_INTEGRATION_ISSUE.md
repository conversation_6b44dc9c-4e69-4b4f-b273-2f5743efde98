# Google AI Toolbox MCP集成问题分析

## 🎯 问题描述

Google AI Toolbox与Agent-Zero的MCP集成失败，出现"405 Method Not Allowed"错误。

## 🔍 问题分析

### **协议不兼容**

**Agent-Zero MCP客户端支持的协议**：
1. **stdio** - 本地命令行程序通信
2. **SSE** - Server-Sent Events协议

**Google AI Toolbox使用的协议**：
- **HTTP POST** - 标准HTTP POST请求到`/mcp`端点

### **测试结果**

```
📡 测试: http://************:5000/mcp
  GET: 405 - Method Not Allowed
  POST: 200 - OK

📡 测试SSE: http://************:5000/mcp
  状态码: 405
  Content-Type: application/json
  ❌ SSE连接失败: 405
```

**结论**: Google AI Toolbox的`/mcp`端点只接受POST请求，不支持SSE协议。

## 🚀 解决方案

### **方案1: 检查Google AI Toolbox的stdio支持**

Google AI Toolbox可能支持stdio模式，需要检查：

```bash
# 检查是否支持stdio模式
./toolbox --help

# 查找stdio相关选项
./toolbox --mode stdio
# 或
./toolbox stdio
```

**如果支持stdio模式，配置如下**：
```json
{
    "name": "postgres-basic",
    "description": "Google AI Toolbox for Database Operations",
    "command": "toolbox",
    "args": ["--tools-file", "tools.yaml", "--mode", "stdio"],
    "timeout": 30
}
```

### **方案2: 使用MCP代理/桥接器**

创建一个中间代理，将Agent-Zero的SSE请求转换为HTTP POST请求：

**代理服务器功能**：
- 接收Agent-Zero的SSE连接
- 将MCP请求转换为HTTP POST发送给Google AI Toolbox
- 将响应转换回SSE格式返回给Agent-Zero

### **方案3: 扩展Agent-Zero的MCP客户端**

修改Agent-Zero的MCP处理器，添加HTTP POST协议支持：

**需要修改的文件**：
- `python/helpers/mcp_handler.py`
- 添加新的`MCPClientHTTP`类

### **方案4: 使用现有的postgres-expert**

继续使用已经工作的postgres-expert MCP服务器，它使用SSE协议：

```json
{
    "name": "postgres-expert",
    "type": "sse", 
    "description": "PostgreSQL Expert MCP Server",
    "url": "http://************:8000/sse",
    "timeout": 10,
    "sse_read_timeout": 300
}
```

## 🔧 立即可行的解决方案

### **推荐方案: 检查Google AI Toolbox stdio支持**

1. **检查帮助文档**：
   ```bash
   ./toolbox --help | grep -i stdio
   ./toolbox --help | grep -i mode
   ```

2. **尝试stdio模式**：
   ```bash
   ./toolbox --tools-file "tools.yaml" --mode stdio
   # 或
   ./toolbox stdio --tools-file "tools.yaml"
   ```

3. **如果支持stdio，更新配置**：
   ```json
   {
       "name": "postgres-basic",
       "description": "Google AI Toolbox for Database Operations", 
       "command": "toolbox",
       "args": ["--tools-file", "C:/google-ai-toolbox/tools.yaml", "--mode", "stdio"],
       "timeout": 30
   }
   ```

### **备选方案: 创建简单的MCP桥接器**

如果Google AI Toolbox不支持stdio，可以创建一个简单的Python脚本作为桥接器：

```python
#!/usr/bin/env python3
"""
Google AI Toolbox MCP桥接器
将SSE请求转换为HTTP POST请求
"""
import asyncio
import json
import aiohttp
from mcp.server import Server
from mcp.types import Tool, TextContent

# 创建MCP服务器，接收SSE请求
# 转发到Google AI Toolbox的HTTP端点
# 返回结果给Agent-Zero
```

## 📊 方案比较

| 方案 | 复杂度 | 可行性 | 维护成本 |
|------|--------|--------|----------|
| stdio模式 | 低 | 高(如果支持) | 低 |
| MCP代理 | 中 | 高 | 中 |
| 扩展客户端 | 高 | 中 | 高 |
| 使用postgres-expert | 低 | 高 | 低 |

## 🎯 建议行动

1. **立即**: 检查Google AI Toolbox是否支持stdio模式
2. **短期**: 如果不支持stdio，继续使用postgres-expert
3. **长期**: 考虑创建MCP桥接器或扩展Agent-Zero的HTTP支持

## 📝 注意事项

1. **协议兼容性**: 确保所选方案与MCP标准兼容
2. **性能考虑**: 桥接器可能增加延迟
3. **维护成本**: 选择长期可维护的方案
4. **功能完整性**: 确保所有Google AI Toolbox功能都能正常使用

## ✅ 下一步行动

1. 检查Google AI Toolbox的stdio支持
2. 如果支持，更新MCP配置
3. 如果不支持，评估其他方案
4. 测试集成效果
5. 更新文档
