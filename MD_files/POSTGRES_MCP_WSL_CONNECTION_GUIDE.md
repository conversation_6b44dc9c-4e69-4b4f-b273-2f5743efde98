# PostgreSQL MCP WSL连接配置指南

## 🎯 问题描述

Agent-Zero运行在WSL环境中，postgres-mcp服务运行在Windows环境中，需要配置跨环境连接。

## 🔍 问题分析

1. **网络隔离**：WSL和Windows有不同的网络环境
2. **服务绑定**：postgres-mcp可能只绑定到127.0.0.1，WSL无法访问
3. **防火墙限制**：Windows防火墙可能阻止WSL访问
4. **IP地址配置**：需要使用正确的Windows主机IP地址

## 📋 解决步骤

### 步骤1: 修改postgres-mcp服务绑定

**问题**：postgres-mcp默认可能绑定到`127.0.0.1:8000`，只允许本地访问。

**解决方案**：
1. 找到postgres-mcp的启动配置
2. 修改绑定地址为`0.0.0.0:8000`或`*:8000`
3. 重启postgres-mcp服务

**常见配置位置**：
- 启动脚本中的`--host`参数
- 配置文件中的`host`或`bind`设置
- 环境变量`HOST`或`BIND_ADDRESS`

### 步骤2: 配置Windows防火墙

**方法1: 通过Windows设置**
1. 打开"Windows安全中心"
2. 选择"防火墙和网络保护"
3. 点击"允许应用通过防火墙"
4. 添加端口8000的入站规则

**方法2: 通过命令行**
```cmd
# 以管理员身份运行
netsh advfirewall firewall add rule name="PostgreSQL MCP" dir=in action=allow protocol=TCP localport=8000
```

### 步骤3: 获取正确的IP地址

**在WSL中运行**：
```bash
# 方法1: 查看resolv.conf
cat /etc/resolv.conf | grep nameserver

# 方法2: 查看默认网关
ip route show default

# 方法3: 测试连接
telnet <IP地址> 8000
```

**检测到的IP地址**：`************`

### 步骤4: 修改Agent-Zero MCP配置

**当前错误配置**：
```json
"postgres-expert": {
    "type": "sse",
    "url": "https://localhost:8000/sse"
}
```

**正确配置**：
```json
"postgres-expert": {
    "name": "postgres-expert",
    "description": "PostgreSQL Expert MCP Server",
    "url": "http://************:8000/sse",
    "timeout": 10,
    "sse_read_timeout": 300
}
```

## 🔧 验证步骤

### 1. 验证postgres-mcp服务状态

**在Windows中**：
```cmd
netstat -an | findstr :8000
```

应该看到：
```
TCP    0.0.0.0:8000           0.0.0.0:0              LISTENING
```

而不是：
```
TCP    127.0.0.1:8000         0.0.0.0:0              LISTENING
```

### 2. 验证WSL连接

**在WSL中**：
```bash
# 测试端口连接
telnet ************ 8000

# 测试HTTP请求
curl http://************:8000

# 测试SSE端点
curl -H "Accept: text/event-stream" http://************:8000/sse
```

### 3. 验证MCP连接

运行测试脚本：
```bash
cd /mnt/e/AI/agent-zero
conda activate AZ090
python test_postgres_mcp_wsl.py
```

## 🚀 完整配置示例

**Agent-Zero settings.json中的mcp_servers配置**：
```json
"mcp_servers": "[
    {
        \"name\": \"excel-stdio\",
        \"command\": \"uvx\",
        \"args\": [\"excel-mcp-server\", \"stdio\"],
        \"description\": \"Excel MCP Server - Excel file processing\"
    },
    {
        \"name\": \"context7\",
        \"command\": \"npx\",
        \"args\": [\"-y\", \"@upstash/context7-mcp\"],
        \"description\": \"Context7 MCP Server - Up-to-date code documentation\"
    },
    {
        \"name\": \"postgres-expert\",
        \"description\": \"PostgreSQL Expert MCP Server\",
        \"url\": \"http://************:8000/sse\",
        \"timeout\": 10,
        \"sse_read_timeout\": 300
    }
]"
```

## 🔍 故障排除

### 问题1: 连接被拒绝
- 检查postgres-mcp是否绑定到0.0.0.0
- 检查Windows防火墙设置
- 确认服务正在运行

### 问题2: 超时错误
- 增加timeout和sse_read_timeout值
- 检查网络延迟
- 确认服务响应正常

### 问题3: SSL/TLS错误
- 确保使用http://而不是https://
- 检查证书配置（如果使用HTTPS）

## 📝 注意事项

1. **IP地址可能变化**：WSL重启后IP地址可能改变，需要重新检测
2. **防火墙规则**：确保规则持久化，不会在重启后丢失
3. **服务启动顺序**：确保postgres-mcp在Agent-Zero之前启动
4. **端口冲突**：确保8000端口没有被其他服务占用

## ✅ 成功标志

当配置正确时，您应该看到：
1. Agent-Zero启动时MCP服务器连接成功
2. 可以在工具列表中看到postgres-expert相关工具
3. 可以正常调用PostgreSQL相关功能
