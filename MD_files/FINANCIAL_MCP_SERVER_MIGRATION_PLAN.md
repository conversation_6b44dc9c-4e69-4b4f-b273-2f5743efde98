# 金融分析工具MCP服务器移植开发规划

## 📋 **项目概述**

**项目名称**: 金融分析MCP服务器移植  
**执行方式**: SSE (Server-Sent Events) HTTP服务器  
**目标**: 将当前Agent-Zero项目中的金融分析工具移植为独立的MCP服务器  
**预计工期**: 6-9个工作日  

---

## 🎯 **移植策略**

### **选择SSE方式的原因**
1. **性能优势**: 常驻服务，避免频繁启动进程开销
2. **并发支持**: 支持多个客户端同时访问
3. **网络友好**: 可跨网络访问，支持分布式部署
4. **调试便利**: HTTP接口便于测试和监控
5. **扩展性强**: 易于添加新功能和API端点

---

## 📁 **项目结构设计**

```
financial-mcp-server/
├── server.py                          # MCP服务器主入口 (SSE版本)
├── requirements.txt                    # 依赖包列表
├── start_server.sh                     # 启动脚本
├── .env.example                        # 环境变量模板
├── config/
│   ├── __init__.py
│   ├── settings.py                     # 配置管理
│   ├── stock_mapping.py                # 股票名称映射
│   └── indicators_config.py            # 技术指标配置
├── clients/
│   ├── __init__.py
│   └── ifind_client.py                 # 同花顺API客户端
├── tools/
│   ├── __init__.py
│   ├── base_tool.py                    # 工具基类
│   ├── financial_data.py               # 金融数据工具
│   ├── technical_indicators.py         # 技术指标工具
│   └── stock_utils.py                  # 股票代码处理工具
├── utils/
│   ├── __init__.py
│   ├── logger.py                       # 日志工具
│   ├── validators.py                   # 数据验证
│   └── formatters.py                   # 数据格式化
├── tests/
│   ├── __init__.py
│   ├── test_tools.py                   # 工具测试
│   ├── test_client.py                  # 客户端测试
│   └── test_server.py                  # 服务器测试
└── docs/
    ├── API.md                          # API文档
    ├── DEPLOYMENT.md                   # 部署文档
    └── INTEGRATION.md                  # 集成文档
```

---

## 🔧 **详细移植计划**

### **Phase 1: 核心组件提取 (2天)**

#### **1.1 API客户端移植**
**源文件**: `python/helpers/financial_api_client.py`  
**目标文件**: `clients/ifind_client.py`

**需要移植的类和方法**:
```python
class FinancialAPIClient:
    def __init__(self, refresh_token: Optional[str] = None)
    def _get_error_message(self, error_code: int) -> str
    def _get_access_token(self) -> bool
    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]
    async def get_real_time_quotation(self, codes: str, indicators: str = "") -> Dict[str, Any]
    async def get_history_quotation(self, codes: str, startdate: str, enddate: str, indicators: str = "") -> Dict[str, Any]
    async def get_basic_data(self, codes: str, indicators: str = "") -> Dict[str, Any]
    async def get_financial_report(self, codes: str, report_type: str = "", period: str = "") -> Dict[str, Any]
    async def get_technical_indicators(self, codes: str, starttime: str, endtime: str, indicators: str) -> Dict[str, Any]
```

**修改内容**:
- 移除Agent-Zero特定的日志依赖
- 使用标准Python logging
- 添加独立的配置管理

#### **1.2 金融数据工具移植**
**源文件**: `python/tools/financial_data_tool.py`  
**目标文件**: `tools/financial_data.py`

**需要移植的类和方法**:
```python
class FinancialDataTool:
    def __init__(self, agent=None, name="financial_data_tool", **kwargs)
    def _extract_stock_codes(self, text: str) -> str
    def _detect_query_type(self, query: str) -> str
    def _parse_financial_report_query(self, query: str) -> Dict[str, str]
    def _format_real_time_data(self, result: Dict[str, Any]) -> str
    def _format_history_data(self, result: Dict[str, Any]) -> str
    def _format_basic_data(self, result: Dict[str, Any]) -> str
    def _format_financial_report(self, result: Dict[str, Any], period: str = "", report_type: str = "") -> str
    async def execute(self, query_type="auto", codes="", indicators="", **kwargs)
```

**修改内容**:
- 移除Agent依赖，创建独立的工具基类
- 重构execute方法为MCP工具格式
- 标准化返回格式

#### **1.3 技术指标工具移植**
**源文件**: `python/tools/technical_indicators_tool.py`  
**目标文件**: `tools/technical_indicators.py`

**需要移植的类和方法**:
```python
class TechnicalIndicatorsTool:
    def __init__(self, agent=None, name="technical_indicators_tool", **kwargs)
    def _validate_parameters(self, codes: str, indicators: str, period: str) -> Tuple[bool, str]
    def _parse_natural_query(self, query: str) -> Dict[str, Any]
    def _extract_stock_codes(self, query: str) -> str
    def _extract_ma_periods(self, query: str) -> List[str]
    def _calculate_period_dates(self, period: str) -> Tuple[str, str]
    def _format_technical_analysis(self, result: Dict[str, Any], codes: str, indicators: str) -> str
    def _generate_trading_signals(self, data: Dict[str, Any]) -> str
    async def execute(self, query="", codes="", indicators="MACD,RSI,KDJ", period="1M", **kwargs)
```

**修改内容**:
- 移除Agent依赖
- 重构为MCP工具格式
- 优化自然语言处理逻辑

### **Phase 2: MCP服务器架构实现 (2天)**

#### **2.1 服务器主入口**
**文件**: `server.py`

**核心组件**:
```python
# FastMCP服务器实例
mcp_server = FastMCP(name="Financial Analysis MCP Server", instructions="...")

# FastAPI应用
app = FastAPI(title="Financial MCP Server")

# 健康检查端点
@app.get("/health")
async def health_check()

# MCP工具注册
@mcp_server.tool()
async def get_stock_real_time(codes: str, indicators: str = "") -> str

@mcp_server.tool()
async def get_stock_history(codes: str, start_date: str, end_date: str, indicators: str = "") -> str

@mcp_server.tool()
async def get_stock_basic_data(codes: str, indicators: str = "") -> str

@mcp_server.tool()
async def get_financial_report(codes: str, report_type: str = "", period: str = "") -> str

@mcp_server.tool()
async def analyze_technical_indicators(query: str = "", codes: str = "", indicators: str = "MACD,RSI,KDJ", period: str = "1M") -> str

@mcp_server.tool()
async def financial_smart_query(query: str, query_type: str = "auto") -> str

# 服务器启动
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
```

#### **2.2 工具基类设计**
**文件**: `tools/base_tool.py`

```python
class BaseTool:
    def __init__(self, name: str = "")
    def get_logger(self) -> logging.Logger
    async def execute(self, **kwargs) -> str
    def format_response(self, data: Dict[str, Any], template: str = "") -> str
    def handle_error(self, error: Exception) -> str
```

#### **2.3 配置管理**
**文件**: `config/settings.py`

```python
class Settings:
    def __init__(self)
    def load_from_env(self)
    def get_ifind_config(self) -> Dict[str, str]
    def get_server_config(self) -> Dict[str, Any]
    def validate_config(self) -> bool

# 全局配置实例
settings = Settings()
```

### **Phase 3: 辅助组件实现 (1天)**

#### **3.1 股票代码处理工具**
**文件**: `tools/stock_utils.py`

```python
def extract_stock_codes(text: str) -> str
def validate_stock_code(code: str) -> bool
def normalize_stock_code(code: str) -> str
def get_stock_name_mapping() -> Dict[str, str]
def search_stock_by_name(name: str) -> Optional[str]
```

#### **3.2 数据验证器**
**文件**: `utils/validators.py`

```python
def validate_stock_codes(codes: str) -> Tuple[bool, str]
def validate_indicators(indicators: str, indicator_type: str = "basic") -> Tuple[bool, str]
def validate_date_range(start_date: str, end_date: str) -> Tuple[bool, str]
def validate_period(period: str) -> Tuple[bool, str]
```

#### **3.3 数据格式化器**
**文件**: `utils/formatters.py`

```python
def format_real_time_data(data: Dict[str, Any]) -> str
def format_history_data(data: Dict[str, Any]) -> str
def format_technical_indicators(data: Dict[str, Any]) -> str
def format_financial_report(data: Dict[str, Any]) -> str
def format_error_message(error_code: int, message: str) -> str
```

### **Phase 4: 测试和验证 (1天)**

#### **4.1 单元测试**
**文件**: `tests/test_tools.py`

```python
class TestFinancialDataTool:
    async def test_extract_stock_codes()
    async def test_detect_query_type()
    async def test_real_time_query()
    async def test_history_query()
    async def test_technical_indicators()

class TestTechnicalIndicatorsTool:
    async def test_validate_parameters()
    async def test_parse_natural_query()
    async def test_ma_periods_extraction()
    async def test_trading_signals()
```

#### **4.2 集成测试**
**文件**: `tests/test_server.py`

```python
class TestMCPServer:
    async def test_server_startup()
    async def test_health_check()
    async def test_mcp_tools_registration()
    async def test_sse_endpoint()
    async def test_tool_execution()
```

### **Phase 5: 部署和集成 (1天)**

#### **5.1 启动脚本**
**文件**: `start_server.sh`

```bash
#!/bin/bash
# 环境检查
# 依赖安装
# 服务器启动
# 日志配置
```

#### **5.2 Agent-Zero集成配置**
**Agent-Zero配置文件修改**:

```json
{
  "mcpServers": {
    "financial-analysis": {
      "type": "sse",
      "url": "http://localhost:8080/mcp/sse",
      "description": "专业金融数据分析MCP服务器",
      "enabled": true,
      "init_timeout": 10,
      "tool_timeout": 30,
      "keywords": ["股票", "金融", "技术指标", "MACD", "RSI", "KDJ"]
    }
  }
}
```

---

## 🔍 **关键技术细节**

### **MCP工具注册模式**
```python
@mcp_server.tool()
async def tool_name(param1: str, param2: str = "default") -> str:
    """工具描述"""
    try:
        # 工具逻辑
        return result
    except Exception as e:
        return f"❌ 错误: {str(e)}"
```

### **SSE端点配置**
```python
# FastMCP自动处理SSE端点
app.mount("/mcp", mcp_server.create_app())
# 实际端点: http://localhost:8080/mcp/sse
```

### **错误处理标准**
```python
def handle_api_error(result: Dict[str, Any]) -> str:
    if result.get('errorcode') != 0:
        error_msg = result.get('errmsg', '未知错误')
        return f"❌ API调用失败: {error_msg}"
    return ""
```

---

## ⚡ **性能优化策略**

1. **连接池**: 使用aiohttp连接池管理HTTP请求
2. **缓存机制**: 实时数据短期缓存，减少API调用
3. **异步处理**: 全异步架构，提高并发性能
4. **资源管理**: 合理的超时和重试机制

---

## 📊 **预期收益**

### **技术收益**
- **解耦独立**: 金融工具完全独立，不影响主项目
- **性能提升**: SSE方式提供更好的并发性能
- **可维护性**: 独立项目更易于维护和扩展
- **可复用性**: 可被其他Agent项目调用

### **功能收益**
- **功能完整**: 保持所有现有金融分析功能
- **扩展性强**: 易于添加新的金融数据源和指标
- **稳定性高**: 独立运行，不受主项目影响

---

## ⏱️ **开发时间表**

| 阶段 | 任务 | 预计时间 | 关键交付物 |
|------|------|----------|------------|
| Phase 1 | 核心组件提取 | 2天 | API客户端、工具类 |
| Phase 2 | MCP服务器实现 | 2天 | 服务器主体、工具注册 |
| Phase 3 | 辅助组件 | 1天 | 工具类、验证器 |
| Phase 4 | 测试验证 | 1天 | 测试套件 |
| Phase 5 | 部署集成 | 1天 | 启动脚本、配置文件 |

**总计**: 7个工作日

---

## 🎯 **下一步行动**

1. **确认移植方案**: 确认SSE方式和项目结构
2. **环境准备**: 创建独立的开发环境
3. **开始Phase 1**: 提取和移植核心API客户端
4. **逐步实施**: 按阶段推进，每个阶段完成后进行验证

**准备开始移植吗？** 我可以立即开始Phase 1的实施。

---

## 📝 **具体文件移植映射表**

### **源文件 → 目标文件映射**

| 源文件路径 | 目标文件路径 | 移植内容 | 修改程度 |
|------------|--------------|----------|----------|
| `python/helpers/financial_api_client.py` | `clients/ifind_client.py` | 完整API客户端类 | 中等 |
| `python/tools/financial_data_tool.py` | `tools/financial_data.py` | 金融数据工具类 | 重大 |
| `python/tools/technical_indicators_tool.py` | `tools/technical_indicators.py` | 技术指标工具类 | 重大 |
| `python/helpers/tool_selector.py` | `tools/stock_utils.py` | 股票识别逻辑 | 部分 |
| `prompts/default/agent.system.tool.financial_data.md` | `docs/API.md` | 工具文档 | 轻微 |

### **需要新建的文件**

| 文件路径 | 用途 | 优先级 |
|----------|------|--------|
| `server.py` | MCP服务器主入口 | 高 |
| `tools/base_tool.py` | 工具基类 | 高 |
| `config/settings.py` | 配置管理 | 高 |
| `utils/logger.py` | 日志工具 | 中 |
| `utils/validators.py` | 数据验证 | 中 |
| `utils/formatters.py` | 数据格式化 | 中 |
| `start_server.sh` | 启动脚本 | 高 |
| `requirements.txt` | 依赖管理 | 高 |

---

## 🔧 **关键函数移植详情**

### **FinancialAPIClient类移植**

**源位置**: `python/helpers/financial_api_client.py:47-176`

**需要移植的核心方法**:
```python
# 构造函数 - 需要修改日志依赖
def __init__(self, refresh_token: Optional[str] = None)

# Token管理 - 保持不变
def _get_access_token(self) -> bool

# 请求封装 - 需要修改日志调用
def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]

# API方法 - 保持接口不变
async def get_real_time_quotation(self, codes: str, indicators: str = "") -> Dict[str, Any]
async def get_history_quotation(self, codes: str, startdate: str, enddate: str, indicators: str = "") -> Dict[str, Any]
async def get_basic_data(self, codes: str, indicators: str = "") -> Dict[str, Any]
async def get_technical_indicators(self, codes: str, starttime: str, endtime: str, indicators: str) -> Dict[str, Any]
```

**修改要点**:
- 将 `self.agent.context.log.log()` 替换为 `self.logger.info()`
- 移除Agent依赖，使用标准logging模块
- 保持所有API接口签名不变

### **FinancialDataTool类移植**

**源位置**: `python/tools/financial_data_tool.py:31-650`

**核心方法移植**:
```python
# 股票代码提取 - 保持逻辑不变
def _extract_stock_codes(self, text: str) -> str  # 行32-65

# 查询类型检测 - 保持逻辑不变
def _detect_query_type(self, query: str) -> str  # 行67-95

# 数据格式化方法 - 需要标准化输出格式
def _format_real_time_data(self, result: Dict[str, Any]) -> str  # 行97-150
def _format_history_data(self, result: Dict[str, Any]) -> str   # 行152-200
def _format_basic_data(self, result: Dict[str, Any]) -> str     # 行202-250

# 主执行方法 - 需要重构为MCP工具格式
async def execute(self, query_type="auto", codes="", indicators="", **kwargs)  # 行560-650
```

**重构要点**:
- 移除 `Response` 类依赖，直接返回字符串
- 将 `self.agent.context.log.log()` 替换为标准日志
- 保持所有业务逻辑不变

### **TechnicalIndicatorsTool类移植**

**源位置**: `python/tools/technical_indicators_tool.py:21-400`

**核心方法移植**:
```python
# 参数验证 - 保持逻辑不变
def _validate_parameters(self, codes: str, indicators: str, period: str) -> Tuple[bool, str]  # 行80-102

# 自然语言解析 - 保持逻辑不变
def _parse_natural_query(self, query: str) -> Dict[str, Any]  # 行104-180

# 股票代码提取 - 保持逻辑不变
def _extract_stock_codes(self, query: str) -> str  # 行252-268

# 技术分析格式化 - 需要标准化输出
def _format_technical_analysis(self, result: Dict[str, Any], codes: str, indicators: str) -> str  # 行300-350

# 主执行方法 - 需要重构为MCP格式
async def execute(self, query="", codes="", indicators="MACD,RSI,KDJ", period="1M", **kwargs)  # 行350-400
```

---

## 🚀 **MCP工具注册策略**

### **工具分类和命名**

| 工具名称 | 对应原始方法 | 功能描述 |
|----------|--------------|----------|
| `get_stock_real_time` | `financial_data_tool.execute(query_type="real_time")` | 获取实时行情 |
| `get_stock_history` | `financial_data_tool.execute(query_type="history")` | 获取历史数据 |
| `get_stock_basic_data` | `financial_data_tool.execute(query_type="basic_data")` | 获取基础数据 |
| `get_financial_report` | `financial_data_tool.execute(query_type="financial_report")` | 获取财务报表 |
| `analyze_technical_indicators` | `technical_indicators_tool.execute()` | 技术指标分析 |
| `financial_smart_query` | 智能路由到对应工具 | 自然语言智能查询 |

### **工具参数标准化**

```python
# 统一的参数格式
@mcp_server.tool()
async def get_stock_real_time(
    codes: str,  # 必需：股票代码，如 "600519.SH,000858.SZ"
    indicators: str = "latest,preClose,change,changeRatio,volume"  # 可选：查询指标
) -> str:
    """获取股票实时行情数据"""

@mcp_server.tool()
async def analyze_technical_indicators(
    query: str = "",  # 可选：自然语言查询
    codes: str = "",  # 可选：股票代码
    indicators: str = "MACD,RSI,KDJ",  # 可选：技术指标
    period: str = "1M"  # 可选：分析周期
) -> str:
    """技术指标分析"""
```

---

## 📋 **环境配置和依赖管理**

### **环境变量配置**
```bash
# .env文件内容
IFIND_REFRESH_TOKEN=your_refresh_token_here
IFIND_ACCESS_TOKEN=your_access_token_here
IFIND_API_TIMEOUT=30
IFIND_MAX_RETRIES=3

# 服务器配置
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8080
LOG_LEVEL=INFO
```

### **依赖包版本锁定**
```txt
# 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
fastmcp==0.3.0
pydantic==2.5.0

# HTTP客户端
aiohttp==3.9.1
requests==2.31.0

# 工具库
python-dotenv==1.0.0
orjson==3.9.10
```

---

## 🔍 **质量保证和测试策略**

### **测试覆盖率目标**
- **单元测试**: 覆盖率 > 80%
- **集成测试**: 覆盖所有MCP工具
- **性能测试**: 响应时间 < 2秒
- **错误处理**: 覆盖所有异常场景

### **测试数据准备**
```python
# 测试用股票代码
TEST_STOCK_CODES = [
    "600519.SH",  # 贵州茅台
    "000858.SZ",  # 五粮液
    "002594.SZ"   # 比亚迪
]

# 测试查询语句
TEST_QUERIES = [
    "查询贵州茅台的实时行情",
    "分析五粮液的MACD指标",
    "获取比亚迪的历史数据"
]
```

---

## 📈 **监控和运维**

### **健康检查端点**
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "api_status": await check_ifind_api_status()
    }
```

### **日志格式标准**
```python
# 统一日志格式
logger.info(f"[{tool_name}] 执行查询: codes={codes}, indicators={indicators}")
logger.error(f"[{tool_name}] 查询失败: {error_message}")
```

---

## ✅ **验收标准**

### **功能验收**
- [ ] 所有原有金融查询功能正常工作
- [ ] MCP工具正确注册和响应
- [ ] SSE端点稳定运行
- [ ] 错误处理完善

### **性能验收**
- [ ] 服务器启动时间 < 10秒
- [ ] 单次查询响应时间 < 3秒
- [ ] 支持并发查询 > 10个
- [ ] 内存占用 < 200MB

### **集成验收**
- [ ] Agent-Zero成功连接MCP服务器
- [ ] 所有工具在Agent-Zero中可用
- [ ] 查询结果格式正确
- [ ] 错误信息清晰明确

---

**📋 移植规划文档已完成，包含了所有具体的文件、函数和实现细节。准备开始实施吗？**
