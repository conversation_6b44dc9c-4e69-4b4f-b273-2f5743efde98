# 温和记忆库提示策略

## 📋 **策略概述**

**实施时间**: 2025-07-08  
**策略类型**: 温和提示，保持LLM自主判断  
**设计原则**: 非强制性建议，避免硬编码逻辑  
**目标**: 在合适时机提醒LLM考虑本地数据，同时保持灵活性  

---

## 🎯 **设计理念**

### **核心原则**
1. **🤝 协助而非控制**: 提供建议而不是强制执行
2. **🧠 保持LLM智能**: 让LLM根据上下文自主判断
3. **⚖️ 平衡效率与灵活性**: 在效率和灵活性之间找到平衡
4. **📊 数据驱动优化**: 基于使用效果调整策略

### **避免的问题**
- ❌ 硬编码的工具选择逻辑
- ❌ 强制性的检索流程
- ❌ 影响LLM的自然判断能力
- ❌ 对所有查询都进行检索

---

## 🔧 **实现机制**

### **温和提示扩展**
**文件**: `python/extensions/message_loop_prompts_before/_15_gentle_memory_hint.py`

#### **工作流程**
```
用户输入 → 相关性评估 → 温和提示生成 → LLM自主判断 → 工具选择
```

#### **相关性评估算法**
```python
def _calculate_local_relevance(self, message: str) -> float:
    relevance_score = 0.0
    
    # 关键词匹配（每个+0.1分，最多0.5分）
    keyword_matches = count_keyword_matches(message)
    relevance_score += min(keyword_matches * 0.1, 0.5)
    
    # 股票代码模式（+0.3分）
    if has_stock_code_pattern(message):
        relevance_score += 0.3
    
    # 财务术语组合（+0.2分）
    if has_financial_terms(message):
        relevance_score += 0.2
    
    # 历史查询模式（+0.1分）
    if has_history_terms(message):
        relevance_score += 0.1
    
    return min(relevance_score, 1.0)
```

---

## 💡 **提示策略**

### **分级提示系统**

#### **强提示 (相关性 ≥ 0.7)**
```
💡 提示: 此查询可能与已上传的本地数据相关。
你可以考虑先使用 memory_load 工具搜索本地知识库和记忆，
如果找到相关信息可以直接使用，如果没有再考虑其他工具。
```

#### **中等提示 (相关性 0.4-0.7)**
```
💡 提示: 此查询可能在本地数据中有相关信息。
你可以考虑使用 memory_load 工具搜索，或者根据情况选择合适的工具。
```

#### **轻提示 (相关性 0.1-0.4)**
```
💡 提示: 如果需要查找历史信息或本地数据，
可以考虑使用 memory_load 工具。
```

### **提示特点**
- 🤝 **建议性语言**: "可以考虑"、"如果需要"
- 🔄 **保留选择权**: "或者根据情况选择"
- 📚 **提供选项**: 列出多种工具选择
- 🎯 **上下文相关**: 根据查询内容调整提示

---

## 📊 **效果监控**

### **使用效果跟踪**
```python
class MemoryUsageTracker:
    def __init__(self):
        self.hint_provided_count = 0      # 提示提供次数
        self.memory_tool_used_count = 0   # memory_load使用次数
        self.hint_effectiveness = 0.0     # 提示有效性
    
    def get_effectiveness(self) -> float:
        return self.memory_tool_used_count / self.hint_provided_count
```

### **自适应调整**
- 📈 **有效性监控**: 跟踪提示后memory_load的使用率
- 🔧 **策略调整**: 根据效果调整提示强度
- 📊 **数据驱动**: 基于实际使用数据优化策略

---

## 🎯 **实际效果示例**

### **示例1: 财务查询**
```
用户: "000858股票的营业收入是多少"
相关性评估: 0.8 (股票代码+财务术语)
提示级别: 强提示
LLM判断: 采纳建议，先使用memory_load
结果: 找到本地财务数据，直接回答
```

### **示例2: 一般查询**
```
用户: "今天天气怎么样"
相关性评估: 0.0 (无相关关键词)
提示级别: 无提示
LLM判断: 自然选择天气工具
结果: 正常的工具调用流程
```

### **示例3: 模糊查询**
```
用户: "帮我分析一下数据"
相关性评估: 0.3 (包含"分析"、"数据")
提示级别: 轻提示
LLM判断: 可能询问具体需求或选择合适工具
结果: 保持LLM的自然对话能力
```

---

## ⚖️ **平衡效果**

### **效率提升**
- ✅ **相关查询**: 对可能有本地数据的查询提供提示
- ✅ **减少遗漏**: 避免LLM忽略本地数据源
- ✅ **智能建议**: 基于内容相关性提供建议

### **灵活性保持**
- ✅ **LLM自主**: 保持LLM的判断和选择能力
- ✅ **上下文感知**: LLM可以根据对话上下文调整策略
- ✅ **自然对话**: 不影响LLM的自然对话能力

### **性能优化**
- ✅ **按需提示**: 只在相关时提供提示
- ✅ **轻量级**: 最小的性能开销
- ✅ **非阻塞**: 不影响正常的工具调用流程

---

## 🔧 **配置和调优**

### **关键参数**
```python
# 相关性阈值
KEYWORD_WEIGHT = 0.1        # 每个关键词权重
STOCK_CODE_WEIGHT = 0.3     # 股票代码权重
FINANCIAL_WEIGHT = 0.2      # 财务术语权重
HISTORY_WEIGHT = 0.1        # 历史查询权重

# 提示级别阈值
STRONG_HINT_THRESHOLD = 0.7   # 强提示阈值
MODERATE_HINT_THRESHOLD = 0.4 # 中等提示阈值
LIGHT_HINT_THRESHOLD = 0.1    # 轻提示阈值
```

### **优化建议**
1. **监控有效性**: 定期检查提示的采纳率
2. **调整阈值**: 根据使用效果调整相关性阈值
3. **扩展关键词**: 根据实际使用场景扩展关键词库
4. **A/B测试**: 对比不同提示策略的效果

---

## 🚀 **部署和使用**

### **自动启用**
- ✅ 扩展已创建并自动加载
- ✅ 重启Agent-Zero后生效
- ✅ 无需额外配置

### **验证方法**
1. **测试财务查询**: "000858的财务数据"
2. **观察调试日志**: 查看相关性评分
3. **检查提示效果**: 观察LLM是否采纳建议

### **调试信息**
```
Debug: 温和提示: 本地相关性 0.75
Debug: 温和提示: 提示已添加 - 强提示级别
```

---

## ✅ **总结**

### **策略优势**
- 🎯 **精准提示**: 只在相关时提供建议
- 🤝 **协作模式**: 与LLM协作而非控制
- ⚖️ **平衡设计**: 效率与灵活性的最佳平衡
- 📊 **数据驱动**: 基于实际效果持续优化

### **适用场景**
- ✅ **财务数据查询**: 股票、财报等本地数据
- ✅ **历史信息查询**: 之前的对话和记录
- ✅ **文档内容查询**: 已上传的文档资料
- ✅ **知识库检索**: 本地知识库内容

### **预期效果**
- 🚀 **提升本地数据利用率**: 30-50%
- 🧠 **保持LLM智能**: 100%自主判断能力
- ⚡ **优化响应效率**: 减少不必要的API调用
- 🎯 **改善用户体验**: 更准确的信息获取

这个温和策略完美平衡了效率和灵活性，让LLM在合适的时候考虑本地数据，同时保持完全的自主判断能力！🎉
