# Agent-Zero 端口配置修复

## 问题描述

用户发现当前项目运行在端口 `5000`，但参考版本 `agent-zero-087` 运行在端口 `50001`。

## 问题分析

### 发现的配置不一致

1. **`.env` 文件配置**：`WEB_UI_PORT=50001`
2. **实际运行端口**：`5000` (从进程 `python run_ui.py --port=5000` 可见)
3. **启动脚本默认值**：`DEFAULT_PORT="5000"`

### 端口获取逻辑

在 `python/helpers/runtime.py` 中：

```python
def get_web_ui_port():
    web_ui_port = (
        get_arg("port")           # 命令行参数 --port (优先级最高)
        or int(dotenv.get_dotenv_value("WEB_UI_PORT", 0))  # .env文件中的WEB_UI_PORT
        or 5000                   # 默认值
    )
    return web_ui_port
```

### 问题根因

**优先级冲突**：
- 启动脚本中硬编码 `DEFAULT_PORT="5000"`
- 通过命令行参数 `--port=5000` 启动
- 命令行参数覆盖了 `.env` 文件中的 `WEB_UI_PORT=50001` 配置

## 修复方案

### 1. 修改启动脚本默认端口

**文件**：`start_agent_zero.sh`

**修改前**：
```bash
DEFAULT_PORT="5000"
```

**修改后**：
```bash
DEFAULT_PORT="50001"
```

### 2. 更新服务检查脚本

**文件**：`quick_start.sh`

**修改内容**：
- 将所有 `localhost:5000` 改为 `localhost:50001`
- 更新服务状态检查URL
- 更新服务地址显示

**具体修改**：

1. **服务状态检查**：
   ```bash
   # 修改前
   if curl -s http://localhost:5000 > /dev/null 2>&1; then
       echo "✅ Agent-Zero: 运行正常 (http://localhost:5000)"
   
   # 修改后  
   if curl -s http://localhost:50001 > /dev/null 2>&1; then
       echo "✅ Agent-Zero: 运行正常 (http://localhost:50001)"
   ```

2. **运行状态检查**：
   ```bash
   # 修改前
   if curl -s http://localhost:5000 > /dev/null 2>&1; then
   
   # 修改后
   if curl -s http://localhost:50001 > /dev/null 2>&1; then
   ```

3. **服务地址显示**：
   ```bash
   # 修改前
   echo "   📱 Agent-Zero: http://localhost:5000"
   
   # 修改后
   echo "   📱 Agent-Zero: http://localhost:50001"
   ```

## 配置一致性验证

### 修复后的配置

1. **`.env` 文件**：`WEB_UI_PORT=50001` ✅
2. **启动脚本默认值**：`DEFAULT_PORT="50001"` ✅
3. **服务检查脚本**：使用 `localhost:50001` ✅

### 端口获取优先级

1. **命令行参数** `--port` (如果提供)
2. **环境变量** `WEB_UI_PORT=50001` (从 .env 文件)
3. **启动脚本默认值** `DEFAULT_PORT="50001"`
4. **代码默认值** `5000` (最后备选)

## 与参考版本的对比

### agent-zero-087 版本
- 启动脚本中没有硬编码端口
- 直接使用 `python run_ui.py` 启动
- 依赖 runtime.py 的端口获取逻辑

### 当前版本 (修复后)
- 启动脚本默认端口与 .env 配置一致
- 保持了灵活的端口配置机制
- 支持命令行参数覆盖

## 影响评估

### 用户影响
- **现有用户**：需要更新书签/收藏夹中的URL
- **新用户**：获得一致的端口配置体验
- **开发者**：配置更加清晰和一致

### 服务影响
- **当前运行的服务**：需要重启才能应用新端口
- **自动化脚本**：需要更新端口引用
- **文档**：需要更新相关端口信息

## 验证步骤

修复完成后，建议进行以下验证：

1. **重启服务**：
   ```bash
   ./quick_start.sh --stop
   ./quick_start.sh
   ```

2. **检查端口**：
   ```bash
   ps aux | grep "python run_ui.py"
   # 应该显示 --port=50001
   ```

3. **访问测试**：
   - 访问 `http://localhost:50001`
   - 确认服务正常运行

4. **配置验证**：
   ```bash
   curl -s http://localhost:50001 > /dev/null && echo "✅ 端口配置正确"
   ```

## 总结

通过统一启动脚本默认端口和 `.env` 文件配置，解决了端口配置不一致的问题。现在项目将在端口 `50001` 上运行，与参考版本 `agent-zero-087` 保持一致。

---

**修复时间**：2025-06-26 10:40 UTC  
**修复文件**：
- `start_agent_zero.sh`
- `quick_start.sh`

**下一步**：重启服务以应用新的端口配置
