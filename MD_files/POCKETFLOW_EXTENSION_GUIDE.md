# PocketFlow 在 Agent Zero 中的功能扩展指南

## 📖 概述

本指南详细介绍如何使用 PocketFlow 框架在 Agent Zero 项目中开发自定义功能扩展。通过本指南，您可以快速创建复杂的工作流工具，实现任意业务逻辑。

## 🎯 适用场景

- 批量数据处理
- 复杂业务流程自动化
- 多步骤内容生成
- 并行任务执行
- 智能决策工作流

## 🏗️ 架构概览

```
用户请求 → Agent Zero → PocketFlow工具 → 工作流执行 → 结果返回
    ↓           ↓            ↓            ↓           ↓
  自然语言   → 工具调用   → 图结构编排  → 节点执行   → 格式化输出
```

## 🚀 快速开始

### 前置条件

1. **环境准备**
```bash
conda activate A0
pip install pocketflow pyyaml
```

2. **确认集成基础**
确保以下文件存在：
- `python/helpers/pocketflow_adapter.py`
- `python/tools/code_generator_tool.py`（参考示例）

## 📋 扩展开发步骤

### 步骤一：设计工作流 (15分钟)

1. **明确需求**
   - 输入：用户提供什么数据？
   - 处理：需要哪些步骤？
   - 输出：期望什么结果？

2. **绘制流程图**
```mermaid
graph TD
    A[输入节点] --> B[处理节点1]
    B --> C[处理节点2]
    C --> D[输出节点]
    B --> E[并行节点]
    E --> D
```

3. **定义节点**
   - 每个处理步骤对应一个节点
   - 确定节点间的数据传递
   - 考虑错误处理和分支逻辑

### 步骤二：创建工作流节点 (30-60分钟)

1. **创建节点文件**
```bash
mkdir -p python/workflows/your_workflow_name
touch python/workflows/your_workflow_name/nodes.py
```

2. **实现节点类**
```python
# python/workflows/your_workflow_name/nodes.py
from pocketflow import Node, BatchNode
from python.helpers.pocketflow_adapter import AgentZeroLLMAdapter, YAMLParser

class InputNode(Node):
    """输入处理节点"""
    
    async def run_async(self, shared):
        # 处理输入数据
        input_data = shared.get("input_data", "")
        
        # 数据验证和预处理
        processed_data = self.preprocess_input(input_data)
        
        # 保存到共享状态
        shared["processed_input"] = processed_data
        
        print(f"✅ 输入处理完成: {len(processed_data)} 项")
    
    def preprocess_input(self, data):
        # 实现具体的预处理逻辑
        return data

class ProcessingNode(Node):
    """核心处理节点"""
    
    async def run_async(self, shared):
        llm_adapter = shared["llm_adapter"]
        input_data = shared["processed_input"]
        
        # 构建提示
        prompt = f"""
        请处理以下数据：
        {input_data}
        
        处理要求：
        1. 分析数据结构
        2. 提取关键信息
        3. 生成处理结果
        
        请以YAML格式返回结果：
        ```yaml
        analysis: |
          数据分析结果...
        
        key_points:
          - 关键点1
          - 关键点2
        
        result: |
          处理后的结果...
        ```
        """
        
        response = await llm_adapter.call_llm(prompt)
        
        try:
            yaml_str = YAMLParser.extract_yaml_from_response(response)
            result = YAMLParser.safe_load(yaml_str)
            shared["processing_result"] = result
            
            print(f"✅ 处理完成: {result.get('analysis', '')[:50]}...")
            
        except Exception as e:
            shared["processing_result"] = {"error": str(e)}
            print(f"❌ 处理失败: {e}")

class OutputNode(Node):
    """输出格式化节点"""
    
    async def run_async(self, shared):
        result = shared.get("processing_result", {})
        
        # 格式化输出
        formatted_output = self.format_output(result)
        shared["final_output"] = formatted_output
        
        print(f"✅ 输出生成完成")
    
    def format_output(self, result):
        if "error" in result:
            return f"处理过程中出现错误: {result['error']}"
        
        output = f"""
📊 处理结果报告

🔍 分析结果:
{result.get('analysis', '无分析结果')}

📋 关键要点:
"""
        for point in result.get('key_points', []):
            output += f"• {point}\n"
        
        output += f"""
📄 最终结果:
{result.get('result', '无处理结果')}
"""
        return output
```

### 步骤三：创建工作流编排 (15分钟)

```python
# python/workflows/your_workflow_name/flow.py
from pocketflow import Flow
from .nodes import InputNode, ProcessingNode, OutputNode

def create_workflow():
    """创建工作流"""
    
    # 创建节点
    input_node = InputNode()
    processing_node = ProcessingNode()
    output_node = OutputNode()
    
    # 定义流程
    input_node >> processing_node >> output_node
    
    # 创建流程
    flow = Flow(start=input_node)
    return flow

async def run_workflow(shared_data):
    """运行工作流"""
    
    # 创建工作流
    flow = create_workflow()
    
    # 按顺序执行节点
    input_node = InputNode()
    await input_node.run_async(shared_data)
    
    processing_node = ProcessingNode()
    await processing_node.run_async(shared_data)
    
    output_node = OutputNode()
    await output_node.run_async(shared_data)
    
    return shared_data.get("final_output", "工作流执行完成")
```

### 步骤四：创建 Agent Zero 工具 (20分钟)

```python
# python/tools/your_tool_name.py
from python.helpers.tool import Tool, Response
from python.helpers.pocketflow_adapter import AgentZeroLLMAdapter
from python.workflows.your_workflow_name.flow import run_workflow

class YourCustomTool(Tool):
    """
    基于 PocketFlow 的自定义工具
    实现特定的业务逻辑处理
    """
    
    async def execute(self, input_data="", **kwargs):
        if not input_data:
            return Response(
                message="请提供需要处理的数据",
                break_loop=False
            )
        
        try:
            # 创建适配器
            llm_adapter = AgentZeroLLMAdapter(self.agent)
            
            # 初始化共享状态
            shared = {
                "input_data": input_data,
                "llm_adapter": llm_adapter,
                "tool_args": kwargs
            }
            
            print(f"🚀 开始执行工作流...")
            print(f"📝 输入数据: {input_data[:100]}...")
            
            # 运行工作流
            result = await run_workflow(shared)
            
            return Response(
                message=result,
                break_loop=False
            )
            
        except Exception as e:
            return Response(
                message=f"工作流执行失败: {str(e)}",
                break_loop=False
            )
```

### 步骤五：添加系统提示 (10分钟)

```markdown
<!-- prompts/default/agent.system.tool.your_tool_name.md -->
## Your Custom Tool

基于 PocketFlow 的自定义工具，用于处理特定的业务逻辑。

**功能特性**：
- 🔄 多步骤数据处理
- 🧠 智能分析和提取
- 📊 结构化结果输出
- ⚡ 高效工作流执行

**使用方法**：
```json
{
  "tool_name": "your_tool_name",
  "parameters": {
    "input_data": "需要处理的数据内容"
  }
}
```

**参数说明**：
- `input_data` (必需): 需要处理的原始数据

**适用场景**：
- 数据分析和处理
- 内容提取和整理
- 批量信息处理
- 复杂逻辑执行

**示例用法**：
```
请使用自定义工具处理以下数据：
[您的数据内容]
```
```

### 步骤六：注册工具 (5分钟)

1. **添加到工具列表**
```bash
# 编辑 prompts/default/agent.system.tools.md
echo "{{ include './agent.system.tool.your_tool_name.md' }}" >> prompts/default/agent.system.tools.md
```

2. **验证注册**
```bash
grep -r "your_tool_name" prompts/default/
```

### 步骤七：测试和调试 (30分钟)

1. **创建测试脚本**
```python
# examples/test_your_tool.py
import asyncio
from initialize import initialize_agent
from python.tools.your_tool_name import YourCustomTool

async def test_tool():
    config = initialize_agent()
    agent = Agent(0, config)
    
    tool = YourCustomTool(
        agent=agent,
        name="your_tool_name",
        method="",
        args={"input_data": "测试数据"},
        message=""
    )
    
    result = await tool.execute(input_data="测试数据")
    print(result.message)

if __name__ == "__main__":
    asyncio.run(test_tool())
```

2. **运行测试**
```bash
python examples/test_your_tool.py
```

3. **在 Agent Zero 中测试**
启动 Agent Zero，发送消息：
```
请使用自定义工具处理以下数据：这是一些测试数据
```

## 🎯 高级功能

### 并行处理

```python
class ParallelProcessingNode(BatchNode):
    def prep(self, shared):
        # 准备并行任务
        data_chunks = shared["data_chunks"]
        return [(chunk, shared["config"]) for chunk in data_chunks]
    
    def exec(self, task_data):
        chunk, config = task_data
        # 处理单个数据块
        return self.process_chunk(chunk, config)
    
    def post(self, shared, prep_res, exec_res_list):
        # 合并并行结果
        shared["parallel_results"] = exec_res_list
```

### 条件分支

```python
class DecisionNode(Node):
    def post(self, shared, prep_res, exec_res):
        if exec_res["confidence"] > 0.8:
            return "high_confidence"
        else:
            return "low_confidence"

# 在流程中使用
decision_node - "high_confidence" >> success_node
decision_node - "low_confidence" >> retry_node
```

### 错误处理

```python
class RobustProcessingNode(Node):
    async def run_async(self, shared):
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await self.process_data(shared)
                shared["result"] = result
                return
            except Exception as e:
                if attempt == max_retries - 1:
                    shared["error"] = str(e)
                    print(f"❌ 处理失败: {e}")
                else:
                    print(f"⚠️ 重试 {attempt + 1}/{max_retries}: {e}")
```

## 📚 最佳实践

### 1. 代码组织
```
python/workflows/
├── __init__.py
├── data_processing/
│   ├── __init__.py
│   ├── nodes.py
│   └── flow.py
├── content_generation/
│   ├── __init__.py
│   ├── nodes.py
│   └── flow.py
└── common/
    ├── __init__.py
    └── base_nodes.py
```

### 2. LLM 调用最佳实践 ⭐⭐⭐
```python
class AgentZeroLLMAdapter:
    async def call_llm(self, prompt: str, system_prompt: str = "") -> str:
        try:
            # ✅ 推荐：统一使用 utility_model
            if system_prompt:
                response = await self.agent.call_utility_model(
                    system=system_prompt,
                    message=prompt
                )
            else:
                response = await self.agent.call_utility_model(
                    system="你是一个有用的AI助手，请根据用户的要求提供准确的回答。",
                    message=prompt
                )
            return response
        except Exception as e:
            print(f"❌ LLM调用错误: {str(e)}")
            return f"LLM调用错误: {str(e)}"
```

### 3. 简化工作流设计 ⭐⭐⭐
```python
# ✅ 推荐：简化的工作流
async def _run_simplified_workflow(self, shared):
    print("📋 步骤1: 数据处理")
    await self._process_data(shared)

    print("💻 步骤2: 生成结果")
    await self._generate_results(shared)

    print("🧪 步骤3: 验证输出")
    await self._validate_output(shared)

# ❌ 避免：过度复杂的 PocketFlow 调用
# flow = self._create_complex_flow()
# await self._run_complex_async_flow(flow, shared)
```

### 4. 错误处理
- 始终包含 try-catch 块
- 提供有意义的错误消息
- 实现重试机制
- 记录详细的执行日志

### 5. 性能优化
- 使用批处理节点处理大量数据
- 实现并行处理减少执行时间
- 缓存中间结果避免重复计算
- 合理设置超时时间

### 6. 用户体验
- 提供清晰的进度提示
- 生成结构化的输出报告
- 包含详细的使用说明
- 支持参数验证和默认值

## 🔧 故障排除

### 常见问题

1. **工具未被识别**
   - 检查文件路径和命名
   - 确认工具已注册到系统提示
   - 重启 Agent Zero 服务

2. **LLM 调用失败**
   - 检查 API 密钥配置
   - 验证网络连接
   - 查看错误日志

3. **工作流执行错误**
   - 检查节点间数据传递
   - 验证 YAML 解析逻辑
   - 添加调试输出

4. **⚠️ 超时问题（重要）**
   - **症状**：工具执行后出现 30 秒超时警告，进程暂停
   - **原因**：复杂的异步调用链或 LLM 调用方式不当
   - **解决方案**：
     ```python
     # ❌ 避免复杂的 chat_model 调用
     chat_prompt = ChatPromptTemplate.from_messages([HumanMessage(content=prompt)])
     response = await self.agent.call_chat_model(chat_prompt)

     # ✅ 推荐使用 utility_model
     response = await self.agent.call_utility_model(
         system="你是一个有用的AI助手，请根据用户的要求提供准确的回答。",
         message=prompt
     )
     ```

5. **异步调用复杂性**
   - **症状**：工具执行卡住或出现异常
   - **原因**：过度复杂的 PocketFlow 节点调用
   - **解决方案**：简化工作流，直接调用处理方法

### 调试技巧

```python
# 添加调试输出
print(f"🔍 调试信息: {shared}")

# 保存中间结果
import json
with open("debug_output.json", "w") as f:
    json.dump(shared, f, indent=2, ensure_ascii=False)

# 分步执行测试
async def debug_workflow():
    shared = {"input_data": "test"}
    
    node1 = InputNode()
    await node1.run_async(shared)
    print("Node 1 完成:", shared)
    
    node2 = ProcessingNode()
    await node2.run_async(shared)
    print("Node 2 完成:", shared)
```

## ⚠️ 重要注意事项

### 基于实际问题修复的经验

在实际开发过程中，我们发现了一些关键问题和解决方案：

#### 1. **避免超时问题**
- **问题**：复杂的异步调用链可能导致 30 秒超时
- **解决**：使用简化的工作流设计，避免过度复杂的 PocketFlow 节点调用

#### 2. **LLM 调用最佳实践**
- **推荐**：统一使用 `agent.call_utility_model()`
- **避免**：复杂的 `call_chat_model()` 调用链

#### 3. **调试友好的设计**
- 添加详细的进度输出：`print(f"🚀 步骤1: ...")`
- 包含异常处理和错误日志
- 提供分步测试能力

#### 4. **参考资料**
- 查看 `POCKETFLOW_TROUBLESHOOTING_GUIDE.md` 获取详细的故障排除指南
- 使用 `test_code_generator_fix.py` 作为测试模板
- 参考修复后的 `python/tools/code_generator_tool.py` 实现

## 🎉 总结

通过本指南，您可以：
- ✅ 快速创建自定义 PocketFlow 工具
- ✅ 实现复杂的业务逻辑工作流
- ✅ 集成到 Agent Zero 生态系统
- ✅ 提供优秀的用户体验
- ✅ 避免常见的超时和调用问题

**开发时间估算**：
- 简单工具：2-4 小时
- 中等复杂度：1-2 天
- 复杂工作流：3-5 天

**下一步**：选择一个具体的业务场景，按照本指南开始您的第一个扩展开发！

**记住**：简单的解决方案往往是最好的解决方案！
