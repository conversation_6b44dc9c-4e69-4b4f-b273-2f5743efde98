# Agent-Zero 启动指南

本文档介绍如何使用新的独立启动脚本来运行 Agent-Zero 和 SearXNG 服务。

## 概述

项目现在提供了四种启动方式：

1. **快速启动（推荐）** - `quick_start.sh`
2. **独立启动 Agent-Zero** - `start_agent_zero.sh`
3. **独立启动 SearXNG** - `start_searxng.sh`
4. **统一启动所有服务** - `start_all.sh`

## 端口配置

- **Agent-Zero**: 默认端口 `5000`
- **SearXNG**: 固定端口 `8888` (匹配 Agent-Zero 配置)

## 独立启动脚本

### 1. 启动 SearXNG 服务

```bash
# 后台运行（默认）
./start_searxng.sh

# 前台运行
./start_searxng.sh --foreground
./start_searxng.sh -f
```

**SearXNG 服务信息：**
- 访问地址: http://127.0.0.1:8888
- 日志文件: /tmp/searxng_standalone.log
- 停止服务: `pkill -f 'searx.webapp'`

### 2. 启动 Agent-Zero 服务

```bash
# 基本启动（前台运行）
./start_agent_zero.sh

# 后台运行
./start_agent_zero.sh --background
./start_agent_zero.sh -b

# 指定端口和主机
./start_agent_zero.sh --port 8080 --host 0.0.0.0

# 强制使用特定环境
./start_agent_zero.sh --env conda    # 使用 conda 环境
./start_agent_zero.sh --env venv     # 使用 venv 环境

# 仅检查环境
./start_agent_zero.sh --check-only

# 详细输出
./start_agent_zero.sh --verbose

# 查看帮助
./start_agent_zero.sh --help
```

**Agent-Zero 服务信息：**
- 访问地址: http://localhost:5000 (默认)
- 日志文件: /tmp/agent_zero.log (后台模式)
- 停止服务: `pkill -f 'python run_ui.py'`

## 快速启动脚本

### 一键启动（最简单）

```bash
# 一键启动所有服务
./quick_start.sh

# 停止所有服务
./quick_start.sh --stop

# 检查服务状态
./quick_start.sh --check

# 查看帮助
./quick_start.sh --help
```

## 推荐的启动流程

### 方案一：快速启动（最推荐）

```bash
# 一键启动：SearXNG（后台）+ Agent-Zero（前台）
./quick_start.sh
```

### 方案二：独立启动

```bash
# 1. 先启动 SearXNG（后台）
./start_searxng.sh

# 2. 再启动 Agent-Zero（前台）
./start_agent_zero.sh
```

### 方案三：统一启动

```bash
# 一次性启动所有服务
./start_all.sh
```

## 环境要求

### Conda 环境
- **SearXNG**: `searxng` 环境 (Python 3.11)
- **Agent-Zero**: `A0` 环境 (Python 3.12)

### Venv 环境
- **Agent-Zero**: `./venv` 目录

### 系统依赖
- **FFmpeg**: 用于音频处理
- **curl**: 用于服务健康检查

## 故障排除

### 1. SearXNG 启动失败

```bash
# 检查日志
tail -f /tmp/searxng_standalone.log

# 检查端口占用
netstat -tlnp | grep 8888

# 手动停止进程
pkill -f 'searx.webapp'
```

### 2. Agent-Zero 启动失败

```bash
# 检查环境
./start_agent_zero.sh --check-only --verbose

# 检查日志（后台模式）
tail -f /tmp/agent_zero.log

# 检查端口占用
netstat -tlnp | grep 5000
```

### 3. 环境问题

```bash
# 检查 conda 环境
conda env list

# 检查 venv 环境
ls -la venv/

# 检查 Python 版本
python --version
```

### 4. API 密钥配置

```bash
# 检查 API 密钥配置
./check_api_keys.sh --verbose

# 交互式配置 API 密钥
./check_api_keys.sh --interactive

# 创建 .env 文件（如果不存在）
./check_api_keys.sh --create

# 手动编辑配置
nano .env
```

**支持的 API 密钥：**
- ✅ **OpenAI** - 最成熟的AI服务
- ✅ **Anthropic** - Claude模型
- ✅ **SiliconFlow** - 国内服务，访问速度快 🆕
- ✅ **VolcEngine** - 字节跳动AI服务 🆕
- ✅ **DeepSeek** - 国产AI，性价比高
- ✅ **Groq** - 推理速度极快
- ✅ **Google** - Gemini模型
- ✅ **MistralAI** - 欧洲AI服务
- ✅ **OpenRouter** - 多模型聚合
- ✅ **Sambanova** - 高性能推理
- ✅ **HuggingFace** - 开源模型平台
- ✅ **Chutes** - 专业AI服务

## 服务管理

### 停止所有服务

```bash
# 停止 SearXNG
pkill -f 'searx.webapp'

# 停止 Agent-Zero
pkill -f 'python run_ui.py'
```

### 查看运行状态

```bash
# 检查进程
ps aux | grep -E "(searx|run_ui)"

# 检查端口
netstat -tlnp | grep -E "(5000|8888)"

# 测试服务
curl -s http://127.0.0.1:8888 > /dev/null && echo "SearXNG OK"
curl -s http://localhost:5000 > /dev/null && echo "Agent-Zero OK"
```

## 配置文件

### Agent-Zero 配置
- 主配置: `.env`
- SearXNG URL: `python/helpers/searxng.py` (http://localhost:8888/search)

### SearXNG 配置
- 项目路径: `/mnt/e/AI/searxng`
- 端口: 8888
- 主机: 127.0.0.1

## 注意事项

1. **环境隔离**: SearXNG 和 Agent-Zero 使用不同的 conda 环境，避免依赖冲突
2. **端口固定**: SearXNG 必须使用端口 8888，Agent-Zero 可以自定义端口
3. **启动顺序**: 建议先启动 SearXNG，再启动 Agent-Zero
4. **日志监控**: 后台运行时注意监控日志文件
5. **资源清理**: 停止服务时确保进程完全终止

## 开发模式

项目已优化为 WSL 本地开发模式：
- 移除了 Docker 依赖
- 移除了 RFC 安全机制
- 使用本地执行替代 SSH 调用
- 支持 conda 和 venv 虚拟环境
