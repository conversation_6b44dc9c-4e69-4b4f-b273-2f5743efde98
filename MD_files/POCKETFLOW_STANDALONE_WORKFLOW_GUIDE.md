# PocketFlow 独立工作流开发指南

## 🎯 概述

本指南介绍如何使用 PocketFlow 开发独立的工作流应用，同时借用 Agent Zero 的模型配置和 LLM 调用能力。这种方式让您可以：

- 🚀 快速开发独立的 AI 工作流应用
- 🔧 复用 Agent Zero 的模型管理和配置
- 📦 创建可独立运行的脚本或服务
- 🎨 专注于业务逻辑而非基础设施

## ⚠️ 重要说明：独立性

**独立工作流不需要 Agent Zero 服务运行！**

- ✅ **只需要**：Agent Zero 的代码库、配置文件、API 密钥
- ❌ **不需要**：Agent Zero 的 Web 服务、主进程、数据库连接

独立工作流会创建自己的 Agent 实例，直接使用 Agent Zero 的配置和模型管理能力，但完全独立运行。

## 🏗️ 架构设计

```
独立 PocketFlow 应用
├── 业务逻辑层 (PocketFlow Nodes)
├── 模型适配层 (Agent Zero LLM Adapter)
├── 配置管理层 (Agent Zero Settings)
└── 基础设施层 (Agent Zero Models & API)
```

### 🔄 运行机制

```
独立工作流启动
    ↓
读取 Agent Zero 配置文件
    ↓
创建新的 Agent 实例
    ↓
初始化模型适配器
    ↓
执行 PocketFlow 工作流
    ↓
直接调用 LLM API
```

**关键点**：
- 不依赖运行中的 Agent Zero 服务
- 直接读取配置文件和环境变量
- 创建独立的 Agent 实例
- 使用相同的模型配置和 API 密钥

## 📋 前置条件

1. **Agent Zero 项目**：已配置好的 Agent Zero 环境
2. **PocketFlow**：已安装 PocketFlow 框架
3. **Python 环境**：Python 3.12+ 和相关依赖

```bash
# 确保在 Agent Zero 的 conda 环境中
conda activate A0
pip install pocketflow pyyaml
```

## 🚀 快速开始

### 步骤1：创建独立工作流项目结构

```bash
# 在 Agent Zero 项目根目录下创建独立工作流目录
mkdir standalone_workflows
cd standalone_workflows

# 创建项目结构
mkdir my_workflow
cd my_workflow
touch __init__.py
touch workflow.py
touch config.py
touch main.py
```

### 步骤2：创建模型适配器

```python
# standalone_workflows/my_workflow/config.py
import sys
import os

# 添加 Agent Zero 项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from initialize import initialize_agent
from agent import Agent
from python.helpers import settings


class AgentZeroModelAdapter:
    """Agent Zero 模型适配器，用于独立工作流"""
    
    def __init__(self):
        self.agent = None
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化 Agent Zero 实例"""
        try:
            # 使用 Agent Zero 的配置初始化
            config = initialize_agent()
            self.agent = Agent(0, config)
            
            # 获取当前模型配置信息
            current_settings = settings.get_settings()
            self.chat_model_info = {
                'provider': current_settings['chat_model_provider'],
                'name': current_settings['chat_model_name']
            }
            self.utility_model_info = {
                'provider': current_settings['util_model_provider'],
                'name': current_settings['util_model_name']
            }
            
            print(f"✅ 模型适配器初始化成功")
            print(f"📋 Chat Model: {self.chat_model_info['provider']} - {self.chat_model_info['name']}")
            print(f"📋 Utility Model: {self.utility_model_info['provider']} - {self.utility_model_info['name']}")
            
        except Exception as e:
            print(f"❌ 模型适配器初始化失败: {e}")
            raise
    
    async def call_llm(self, prompt: str, system_prompt: str = "", use_chat_model: bool = False) -> str:
        """
        调用 LLM 模型
        
        Args:
            prompt: 用户提示
            system_prompt: 系统提示
            use_chat_model: 是否使用 chat_model（默认使用 utility_model）
        
        Returns:
            LLM 响应文本
        """
        try:
            if use_chat_model:
                # 使用 chat_model（更适合对话）
                from langchain_core.prompts import ChatPromptTemplate
                from langchain_core.messages import HumanMessage, SystemMessage
                
                messages = []
                if system_prompt:
                    messages.append(SystemMessage(content=system_prompt))
                messages.append(HumanMessage(content=prompt))
                
                chat_prompt = ChatPromptTemplate.from_messages(messages)
                response = await self.agent.call_chat_model(chat_prompt)
            else:
                # 使用 utility_model（推荐，更稳定）
                if not system_prompt:
                    system_prompt = "你是一个有用的AI助手，请根据用户的要求提供准确的回答。"
                
                response = await self.agent.call_utility_model(
                    system=system_prompt,
                    message=prompt
                )
            
            return response
            
        except Exception as e:
            print(f"❌ LLM 调用失败: {e}")
            return f"LLM调用错误: {str(e)}"
    
    def get_model_info(self):
        """获取当前模型配置信息"""
        return {
            'chat_model': self.chat_model_info,
            'utility_model': self.utility_model_info
        }
```

### 步骤3：创建 PocketFlow 工作流

```python
# standalone_workflows/my_workflow/workflow.py
from pocketflow import Node, Flow
import asyncio
import yaml
from .config import AgentZeroModelAdapter


class DataInputNode(Node):
    """数据输入节点"""
    
    def prep(self, shared):
        input_data = shared.get('input_data', '')
        print(f"📥 接收输入数据: {input_data[:100]}...")
        return input_data
    
    def exec(self, input_data):
        # 数据预处理
        if not input_data:
            return {"error": "输入数据为空"}
        
        return {
            "processed_data": input_data,
            "data_length": len(str(input_data)),
            "status": "success"
        }
    
    def post(self, shared, prep_res, exec_res):
        if exec_res.get("error"):
            shared["input_error"] = exec_res["error"]
        else:
            shared["processed_input"] = exec_res["processed_data"]
            shared["input_stats"] = {
                "length": exec_res["data_length"],
                "status": exec_res["status"]
            }
        print(f"✅ 输入处理完成")


class LLMProcessingNode(Node):
    """LLM 处理节点"""
    
    async def run_async(self, shared):
        """异步执行 LLM 处理"""
        if shared.get("input_error"):
            print("❌ 跳过 LLM 处理：输入错误")
            return
        
        model_adapter = shared.get("model_adapter")
        processed_input = shared.get("processed_input", "")
        task_type = shared.get("task_type", "analyze")
        
        # 根据任务类型构建不同的提示
        if task_type == "analyze":
            prompt = f"""
            请分析以下数据：
            {processed_input}
            
            请提供：
            1. 数据概要
            2. 关键信息提取
            3. 分析结论
            
            请以YAML格式返回结果：
            ```yaml
            summary: |
              数据概要...
            key_points:
              - 关键点1
              - 关键点2
            conclusion: |
              分析结论...
            ```
            """
        elif task_type == "translate":
            target_language = shared.get("target_language", "英文")
            prompt = f"请将以下内容翻译为{target_language}：\n\n{processed_input}"
        else:
            prompt = f"请处理以下数据：\n\n{processed_input}"
        
        try:
            print(f"🧠 开始 LLM 处理 ({task_type})...")
            response = await model_adapter.call_llm(
                prompt=prompt,
                system_prompt="你是一个专业的数据分析师，请提供准确和有用的分析。"
            )
            
            shared["llm_response"] = response
            shared["processing_status"] = "success"
            print(f"✅ LLM 处理完成")
            
        except Exception as e:
            shared["llm_error"] = str(e)
            shared["processing_status"] = "failed"
            print(f"❌ LLM 处理失败: {e}")


class OutputFormattingNode(Node):
    """输出格式化节点"""
    
    def prep(self, shared):
        return {
            "llm_response": shared.get("llm_response", ""),
            "input_stats": shared.get("input_stats", {}),
            "task_type": shared.get("task_type", "unknown"),
            "processing_status": shared.get("processing_status", "unknown")
        }
    
    def exec(self, data):
        if data["processing_status"] != "success":
            return {
                "formatted_output": f"❌ 处理失败，无法生成输出",
                "success": False
            }
        
        # 格式化输出
        output = f"""
🎯 工作流执行报告

📊 输入统计:
• 数据长度: {data['input_stats'].get('length', 0)} 字符
• 处理状态: {data['input_stats'].get('status', 'unknown')}

🧠 处理结果:
• 任务类型: {data['task_type']}
• 处理状态: {data['processing_status']}

📄 LLM 响应:
{data['llm_response']}

✅ 工作流执行完成
"""
        
        return {
            "formatted_output": output,
            "success": True
        }
    
    def post(self, shared, prep_res, exec_res):
        shared["final_output"] = exec_res["formatted_output"]
        shared["workflow_success"] = exec_res["success"]
        print(f"📊 输出格式化完成")


class MyWorkflow:
    """独立的 PocketFlow 工作流"""
    
    def __init__(self):
        self.model_adapter = AgentZeroModelAdapter()
        self.input_node = DataInputNode()
        self.processing_node = LLMProcessingNode()
        self.output_node = OutputFormattingNode()
    
    def create_flow(self):
        """创建 PocketFlow 工作流"""
        # 定义节点连接
        self.input_node >> self.processing_node >> self.output_node
        
        # 创建流程
        flow = Flow(start=self.input_node)
        return flow
    
    async def run(self, input_data: str, task_type: str = "analyze", **kwargs):
        """
        运行工作流
        
        Args:
            input_data: 输入数据
            task_type: 任务类型 (analyze/translate/process)
            **kwargs: 其他参数
        
        Returns:
            工作流执行结果
        """
        print(f"🚀 启动独立工作流")
        print(f"📝 任务类型: {task_type}")
        
        # 初始化共享状态
        shared = {
            "input_data": input_data,
            "task_type": task_type,
            "model_adapter": self.model_adapter,
            **kwargs
        }
        
        try:
            # 手动执行节点（简化版本，避免复杂的异步调用）
            # 1. 输入处理
            print("📥 步骤1: 输入处理")
            prep_res = self.input_node.prep(shared)
            exec_res = self.input_node.exec(prep_res)
            self.input_node.post(shared, prep_res, exec_res)
            
            # 2. LLM 处理
            print("🧠 步骤2: LLM 处理")
            await self.processing_node.run_async(shared)
            
            # 3. 输出格式化
            print("📊 步骤3: 输出格式化")
            prep_res = self.output_node.prep(shared)
            exec_res = self.output_node.exec(prep_res)
            self.output_node.post(shared, prep_res, exec_res)
            
            return {
                "success": shared.get("workflow_success", False),
                "output": shared.get("final_output", ""),
                "model_info": self.model_adapter.get_model_info()
            }
            
        except Exception as e:
            print(f"❌ 工作流执行失败: {e}")
            return {
                "success": False,
                "output": f"工作流执行失败: {str(e)}",
                "error": str(e)
            }
```

### 步骤4：创建主程序

```python
# standalone_workflows/my_workflow/main.py
import asyncio
import sys
from .workflow import MyWorkflow


async def main():
    """主程序入口"""
    print("🎯 PocketFlow 独立工作流示例")
    print("="*60)
    
    # 创建工作流实例
    workflow = MyWorkflow()
    
    # 示例1：数据分析任务
    print("\n📋 示例1: 数据分析任务")
    result1 = await workflow.run(
        input_data="""
        销售数据：
        Q1: 100万元，增长15%
        Q2: 120万元，增长20%
        Q3: 110万元，下降8%
        Q4: 130万元，增长18%
        """,
        task_type="analyze"
    )
    
    print("结果1:")
    print(result1["output"])
    
    # 示例2：翻译任务
    print("\n📋 示例2: 翻译任务")
    result2 = await workflow.run(
        input_data="Hello, this is a test message for translation.",
        task_type="translate",
        target_language="中文"
    )
    
    print("结果2:")
    print(result2["output"])
    
    # 显示模型信息
    print(f"\n📊 使用的模型信息:")
    model_info = result1.get("model_info", {})
    print(f"Chat Model: {model_info.get('chat_model', {})}")
    print(f"Utility Model: {model_info.get('utility_model', {})}")


if __name__ == "__main__":
    asyncio.run(main())
```

### 步骤5：创建启动脚本

```python
# standalone_workflows/run_my_workflow.py
#!/usr/bin/env python3
"""
独立 PocketFlow 工作流启动脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from my_workflow.main import main


if __name__ == "__main__":
    try:
        print("🚀 启动独立 PocketFlow 工作流...")
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 工作流被用户中断")
    except Exception as e:
        print(f"\n❌ 工作流执行出错: {e}")
        import traceback
        traceback.print_exc()
```

## 🎮 使用方法

### 运行独立工作流

```bash
# 在 Agent Zero 项目根目录下
cd standalone_workflows
python run_my_workflow.py
```

### 集成到其他项目

```python
# 在其他 Python 项目中使用
from standalone_workflows.my_workflow.workflow import MyWorkflow

async def use_workflow():
    workflow = MyWorkflow()
    result = await workflow.run(
        input_data="你的数据",
        task_type="analyze"
    )
    print(result["output"])
```

## 🔧 自定义和扩展

### 添加新的节点类型

```python
class CustomProcessingNode(Node):
    async def run_async(self, shared):
        # 你的自定义处理逻辑
        pass
```

### 支持不同的模型

```python
# 在工作流中指定使用不同的模型
result = await workflow.run(
    input_data="数据",
    task_type="analyze",
    use_chat_model=True  # 使用 chat_model 而不是 utility_model
)
```

### 批处理支持

```python
class BatchProcessingWorkflow(MyWorkflow):
    async def run_batch(self, data_list: list, task_type: str = "analyze"):
        results = []
        for data in data_list:
            result = await self.run(data, task_type)
            results.append(result)
        return results
```

## 📚 最佳实践

1. **模型选择**：
   - 使用 `utility_model` 进行数据处理（更稳定）
   - 使用 `chat_model` 进行对话交互

2. **错误处理**：
   - 在每个节点中包含异常处理
   - 提供有意义的错误信息

3. **性能优化**：
   - 对于大量数据，考虑使用批处理
   - 添加进度显示和日志

4. **配置管理**：
   - 通过环境变量或配置文件管理参数
   - 支持不同的运行模式

## 🎯 总结

这种方式让您可以：
- ✅ 创建完全独立的 PocketFlow 应用
- ✅ 复用 Agent Zero 的强大模型管理能力
- ✅ 保持代码的模块化和可维护性
- ✅ 轻松扩展和定制工作流逻辑

您现在可以基于这个模板创建任何类型的独立 AI 工作流应用！
