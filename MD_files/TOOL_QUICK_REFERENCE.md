# Agent-Zero 工具系统快速参考

## 📋 **快速导航**

| 文档类型 | 文件名 | 用途 |
|----------|--------|------|
| 完整指南 | [TOOL_SYSTEM_COMPREHENSIVE_GUIDE.md](./TOOL_SYSTEM_COMPREHENSIVE_GUIDE.md) | 系统架构和详细说明 |
| 使用策略 | [TOOL_USAGE_STRATEGY_GUIDE.md](./TOOL_USAGE_STRATEGY_GUIDE.md) | 工具选择和使用策略 |
| 开发指南 | [TOOL_DEVELOPER_GUIDE.md](./TOOL_DEVELOPER_GUIDE.md) | 工具开发和集成 |
| 快速参考 | [TOOL_QUICK_REFERENCE.md](./TOOL_QUICK_REFERENCE.md) | 本文档 |

---

## 🔧 **核心工具速查**

### **基础工具**

| 工具名 | 用途 | 触发关键词 | 调用示例 |
|--------|------|------------|----------|
| `response` | 直接回答 | 无特定关键词 | 知识问答、简单解释 |
| `search_engine` | 基础搜索 | 搜索、查询 | 一般信息查找 |
| `browser` | 网页浏览 | 访问、浏览 | 单页面访问、截图 |
| `code_exe` | 代码执行 | 执行、运行 | 代码测试、计算验证 |
| `memory` | 记忆管理 | 记住、保存 | 信息存储、检索 |
| `scheduler` | 任务调度 | 定时、计划 | 定时任务、提醒 |

### **增强工具**

| 工具名 | 用途 | 触发关键词 | 最佳场景 |
|--------|------|------------|----------|
| `enhanced_search_engine` | 深度搜索 | 深入、详细、全面、研究 | 学术研究、复杂主题 |
| `sequential_thinking` | 系统分析 | 分析、逻辑、结构、系统 | 复杂问题、多步推理 |
| `web_crawler` | 智能爬虫 | 爬取、抓取、采集、收集 | 批量数据、网站内容 |
| `code_generator` | 代码生成 | 代码生成、编程、算法、实现 | 算法题、测试驱动开发 |

### **MCP工具**

| 工具名 | 用途 | 功能 |
|--------|------|------|
| `excel-stdio` | Excel处理 | 读取、分析Excel文件 |
| `context7` | 代码文档 | 代码分析、文档生成 |

---

## 🎯 **工具选择速查**

### **按需求类型选择**

```
📚 学术研究 → enhanced_search_engine + sequential_thinking
💻 编程开发 → code_generator + code_exe
🌐 数据收集 → web_crawler + search_engine
🔍 问题分析 → sequential_thinking + search_engine
📊 数据处理 → excel-stdio + code_exe
📝 文档分析 → context7 + response
```

### **按关键词选择**

```
"深入研究..." → enhanced_search_engine
"系统分析..." → sequential_thinking
"爬取网站..." → web_crawler
"生成代码..." → code_generator
"搜索信息..." → search_engine
"访问网页..." → browser
"执行代码..." → code_exe
"记住这个..." → memory
"定时提醒..." → scheduler
```

---

## 📝 **常用调用格式**

### **Enhanced Search Engine**
```json
{
  "tool_name": "enhanced_search_engine",
  "tool_args": {
    "query": "深度研究主题",
    "max_results": 10,
    "quality_threshold": 0.8
  }
}
```

### **Sequential Thinking**
```json
{
  "tool_name": "sequential_thinking",
  "tool_args": {
    "problem": "复杂问题描述",
    "analysis_depth": "deep"
  }
}
```

### **Web Crawler**
```json
{
  "tool_name": "web_crawler",
  "tool_args": {
    "url": "目标网站URL",
    "extraction_type": "content",
    "max_pages": 10
  }
}
```

### **Code Generator**
```json
{
  "tool_name": "code_generator",
  "tool_args": {
    "problem": "编程问题描述",
    "max_iterations": 3,
    "num_examples": 1
  }
}
```

### **Search Engine**
```json
{
  "tool_name": "search_engine",
  "tool_args": {
    "query": "搜索关键词"
  }
}
```

### **Browser**
```json
{
  "tool_name": "browser",
  "tool_args": {
    "url": "https://example.com",
    "action": "screenshot"
  }
}
```

---

## 🚀 **最佳实践速查**

### **工具选择原则**
1. **明确指定 > 自动选择**
2. **专用工具 > 通用工具**
3. **简单工具 > 复杂工具**

### **高效使用技巧**
```
✅ "使用enhanced_search_engine深入研究..."
✅ "用sequential_thinking分析..."
✅ "请code_generator生成..."
✅ "用web_crawler爬取..."

❌ "搜索一下..." (模糊指向)
❌ "帮我分析..." (不明确工具)
❌ "生成代码..." (可能选择错误工具)
```

### **工具组合策略**
```
研究型: enhanced_search_engine → sequential_thinking → response
开发型: code_generator → code_exe → response
数据型: web_crawler → search_engine → sequential_thinking
分析型: search_engine → sequential_thinking → response
```

---

## 🔍 **故障排除速查**

### **常见问题**

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| Tool not found | 工具名错误/未注册 | 检查工具名、确认注册 |
| 参数错误 | 参数类型/格式错误 | 检查参数格式和类型 |
| 执行超时 | 网络问题/任务复杂 | 检查网络、简化任务 |
| 权限错误 | 缺少必要权限 | 检查用户权限设置 |

### **调试命令**
```bash
# 检查工具加载
python -c "from python.helpers.extract_tools import load_classes_from_folder; print(load_classes_from_folder('python/tools', '*.py', object))"

# 测试工具选择器
python -c "from python.helpers.tool_selector import tool_selector; print(tool_selector.analyze_user_input('测试输入'))"

# 查看日志
tail -f logs/log_$(date +%Y%m%d)_*.html
```

---

## 📊 **性能参考**

### **工具性能对比**

| 工具 | 执行时间 | 内存使用 | 网络依赖 | 并发支持 |
|------|----------|----------|----------|----------|
| response | < 1s | 低 | 无 | 高 |
| search_engine | 2-5s | 低 | 是 | 中 |
| enhanced_search_engine | 10-30s | 中 | 是 | 低 |
| sequential_thinking | 5-15s | 中 | 否 | 中 |
| web_crawler | 10-60s | 高 | 是 | 低 |
| code_generator | 15-45s | 中 | 是 | 低 |
| browser | 3-10s | 中 | 是 | 中 |

### **优化建议**
- **缓存结果**: 相同参数的重复调用
- **并发控制**: 限制同时执行的工具数量
- **超时设置**: 设置合理的超时时间
- **资源管理**: 及时释放不需要的资源

---

## 🔧 **开发速查**

### **新工具开发步骤**
1. 创建 `python/tools/tool_name.py`
2. 实现继承自 `Tool` 的类
3. 创建 `prompts/default/agent.system.tool.tool_name.md`
4. 在 `agent.system.tools.md` 中注册
5. 更新 `tool_selector.py` 添加关键词

### **工具类模板**
```python
from python.helpers.tool import Tool, Response

class MyTool(Tool):
    async def execute(self, **kwargs):
        try:
            # 参数验证
            self._validate_parameters(kwargs)
            
            # 核心逻辑
            result = await self._core_logic(**kwargs)
            
            # 返回结果
            return Response(message=str(result))
        except Exception as e:
            return Response(message=f"执行失败: {e}")
    
    def _validate_parameters(self, kwargs):
        # 参数验证逻辑
        pass
    
    async def _core_logic(self, **kwargs):
        # 核心功能实现
        pass
```

### **描述文件模板**
```markdown
## Tool Name

工具描述...

**使用方法**：
```json
{
  "tool_name": "tool_name",
  "tool_args": {
    "param1": "value1"
  }
}
```

**参数说明**：
- `param1` (string): 参数说明

**使用示例**：
具体使用示例...
```

---

## 📚 **相关资源**

### **配置文件位置**
```
prompts/default/
├── agent.system.tools.md              # 主工具注册
├── agent.system.tool.*.md             # 各工具描述
python/tools/                          # 工具实现
python/helpers/tool_selector.py        # 工具选择器
python/extensions/system_prompt/       # 系统提示扩展
```

### **关键模块**
```python
from python.helpers.tool import Tool, Response
from python.helpers.extract_tools import load_classes_from_folder
from python.helpers.tool_selector import tool_selector
```

### **日志位置**
```
logs/log_YYYYMMDD_HHMMSS.html          # 运行日志
tmp/scheduler/tasks.json               # 调度任务
```

---

## 🎯 **使用建议**

### **新用户**
1. 从基础工具开始熟悉
2. 学习明确指定工具名
3. 了解各工具的适用场景

### **高级用户**
1. 掌握工具组合策略
2. 学习性能优化技巧
3. 参与工具开发和扩展

### **开发者**
1. 阅读完整的开发指南
2. 遵循最佳实践和代码规范
3. 参与社区贡献

---

**快速参考版本**: v1.0  
**最后更新**: 2025-07-07  
**维护团队**: Agent-Zero开发团队
