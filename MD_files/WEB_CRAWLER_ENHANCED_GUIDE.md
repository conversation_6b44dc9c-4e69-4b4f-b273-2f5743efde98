# Web Crawler Enhanced 使用指南

## 📋 **概述**

基于Crawl4AI v0.6.x开发的增强版网页爬虫工具，提供智能网站分析、LLM驱动的策略生成和多种提取方式。

## 🚀 **主要特性**

### ✨ **智能特性**
- **网站类型自动识别**: 自动识别新闻、电商、博客等网站类型
- **LLM策略生成**: 使用项目LLM生成最优爬取策略
- **多种提取方式**: 支持Markdown、结构化数据、纯文本提取
- **智能内容过滤**: 自动去除广告、导航等噪音内容
- **错误处理重试**: 智能重试和降级机制

### 🔧 **技术特性**
- **基于Crawl4AI v0.6.x**: 使用最新的API结构和功能
- **异步处理**: 高性能异步爬取
- **配置灵活**: 支持手动和自动策略
- **多LLM支持**: 兼容项目所有LLM提供商

## 📝 **Prompt写法建议**

### 🎯 **最佳Prompt模式**

#### **1. 明确意图型**
```
爬取 https://example.com/news 获取最新科技新闻的标题和摘要
```

#### **2. 结构化数据型**
```
从 https://shop.example.com/products 提取产品信息，包括名称、价格、评分和描述
```

#### **3. 特定内容型**
```
爬取 https://blog.example.com/article 的文章内容，重点关注技术细节和代码示例
```

#### **4. 数据分析型**
```
从 https://data.example.com/stats 收集统计数据，提取表格和图表信息
```

### 📋 **Prompt结构建议**

#### **完整格式**
```
[动作] + [URL] + [具体需求] + [输出要求]

示例：
爬取 https://news.example.com 获取今日头条新闻，提取标题、作者、发布时间和正文内容
```

#### **关键词触发**
支持中英文关键词：
- **中文**: 爬取、抓取、收集、采集、获取、提取
- **英文**: crawl、scrape、extract、fetch、collect、gather

### 🎨 **不同场景的Prompt示例**

#### **新闻网站**
```
爬取 https://news.site.com 获取最新新闻，重点提取标题、作者、发布时间和正文
```

#### **电商网站**
```
从 https://shop.site.com/search?q=laptop 提取笔记本电脑信息，包括品牌、型号、价格、评分
```

#### **技术博客**
```
爬取 https://tech.blog.com/ai-tutorial 的技术教程，提取代码示例和技术要点
```

#### **数据网站**
```
从 https://data.site.com/covid-stats 收集疫情统计数据，提取表格和趋势信息
```

#### **社交媒体**
```
爬取 https://social.site.com/trending 获取热门话题，提取话题标签和讨论内容
```

## ⚙️ **参数配置**

### 🔧 **基础参数**
- `url`: 目标网址（必需）
- `user_intent`: 用户意图描述（推荐）
- `extract_type`: 提取类型（auto/markdown/structured/text）
- `auto_strategy`: 是否启用智能策略（默认true）
- `max_retries`: 最大重试次数（默认3）

### 📊 **高级配置**
- `css_selector`: 手动CSS选择器
- `content_filter`: 内容过滤方式
- `filter_threshold`: 过滤阈值

## 🎯 **使用场景与策略**

### 📰 **新闻/媒体网站**
**特点**: 文章结构清晰，内容丰富
**策略**: 
- 提取类型: `markdown`
- CSS选择器: `article, .article-content, .post-content`
- 内容过滤: `pruning` (去除广告导航)

**Prompt示例**:
```
爬取 https://news.example.com/tech 获取科技新闻，提取标题、作者、发布时间和完整正文
```

### 🛒 **电商网站**
**特点**: 结构化数据，产品信息规整
**策略**:
- 提取类型: `structured`
- CSS选择器: `.product, .product-item, .search-result`
- 内容过滤: `pruning`

**Prompt示例**:
```
从 https://shop.example.com/category/phones 提取手机产品信息，包括品牌、型号、价格、规格和用户评价
```

### 📝 **博客网站**
**特点**: 个人化内容，格式多样
**策略**:
- 提取类型: `markdown`
- CSS选择器: `.post, .entry-content, .article-body`
- 内容过滤: `bm25` (基于关键词)

**Prompt示例**:
```
爬取 https://blog.example.com/ai-guide 的AI教程，重点提取技术概念、实现方法和代码示例
```

### 📊 **数据网站**
**特点**: 表格数据，统计信息
**策略**:
- 提取类型: `structured`
- CSS选择器: `table, .data-table, .grid, .stats`
- 内容过滤: `pruning`

**Prompt示例**:
```
从 https://data.example.com/market-stats 收集市场统计数据，提取表格数据和趋势图表信息
```

## 🔍 **智能策略生成**

### 🧠 **LLM分析流程**
1. **网站类型识别**: 分析URL、标题、描述
2. **内容结构分析**: 预览页面内容结构
3. **策略生成**: 基于分析结果生成最优策略
4. **策略验证**: 验证和优化生成的策略

### 📋 **策略配置项**
```json
{
    "website_type": "news/ecommerce/blog/data/social",
    "extract_type": "markdown/structured/text",
    "css_selector": "优化的CSS选择器",
    "content_filter": "pruning/bm25/none",
    "filter_threshold": 0.48,
    "wait_for": "等待元素选择器",
    "js_code": "JavaScript代码",
    "reasoning": "策略选择理由"
}
```

## 🛠️ **错误处理与优化**

### 🔄 **重试机制**
- **智能重试**: 最多3次重试
- **降级策略**: 失败时自动简化配置
- **指数退避**: 重试间隔逐渐增加

### ⚡ **性能优化**
- **缓存控制**: 绕过缓存获取最新内容
- **超时设置**: 合理的页面加载超时
- **资源限制**: 禁用不必要的资源加载

### 🔧 **常见问题解决**

#### **问题1: 内容提取不完整**
**解决方案**: 
- 使用更具体的用户意图描述
- 尝试不同的提取类型
- 检查网站是否需要JavaScript渲染

#### **问题2: 提取到太多噪音内容**
**解决方案**:
- 启用内容过滤 (`pruning`)
- 调整过滤阈值
- 使用更精确的CSS选择器

#### **问题3: 动态内容无法获取**
**解决方案**:
- 在用户意图中说明需要等待加载
- 工具会自动生成等待策略
- 考虑使用JavaScript执行

## 📈 **最佳实践**

### ✅ **推荐做法**
1. **明确用户意图**: 详细描述需要提取的内容
2. **启用智能策略**: 让LLM分析并生成最优策略
3. **合理设置重试**: 对于重要任务增加重试次数
4. **验证结果**: 检查提取的内容质量和完整性

### ❌ **避免做法**
1. **过于模糊的描述**: "爬取这个网站的内容"
2. **忽略网站特性**: 不考虑网站类型和结构
3. **过度依赖手动配置**: 不利用智能策略生成
4. **忽略错误处理**: 不检查爬取结果的成功状态

## 🎉 **总结**

增强版Web Crawler工具通过结合Crawl4AI的强大功能和智能LLM策略生成，为用户提供了一个高效、智能、易用的网页爬取解决方案。

**核心优势**:
- 🧠 **智能化**: LLM驱动的策略生成
- 🚀 **高性能**: 基于最新Crawl4AI技术
- 🔧 **灵活性**: 支持多种配置和场景
- 🛡️ **可靠性**: 完善的错误处理和重试机制

通过合理使用Prompt和配置参数，您可以轻松应对各种网页爬取需求，获得高质量的数据提取结果。

---

## 🔧 **最新功能更新 (2025-07-06)**

### ✅ **图片下载功能增强**

**新增功能**:
- ✅ **用户指定路径支持**: 现在可以指定具体的下载路径
- ✅ **路径自动转换**: 支持`/a0/`开发环境路径自动转换
- ✅ **下载验证机制**: 自动验证文件是否真正下载成功
- ✅ **详细调试日志**: 提供完整的下载过程日志

**使用示例**:
```
# 指定具体文件路径下载
从 https://example.com/image.jpg 下载图片到 /a0/tmp/downloads/my_image.jpg

# 指定目录路径下载
爬取 https://gallery.com 下载所有图片到 /a0/tmp/downloads/ 目录
```

**技术改进**:
- 修复了save_path参数被忽略的问题
- 改进了文件路径和目录路径的处理逻辑
- 增强了错误检测和报告机制
- 优化了与ImageGet API的兼容性

**相关文档**:
- 详细修复报告: `WEB_CRAWLER_DOWNLOAD_FIX_2025_07_06.md`
- 图片下载指南: `WEB_CRAWLER_IMAGE_DOWNLOAD_GUIDE.md`
