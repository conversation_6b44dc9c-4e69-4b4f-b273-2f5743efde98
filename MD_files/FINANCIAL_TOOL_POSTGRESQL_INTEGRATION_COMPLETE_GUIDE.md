# 金融数据工具PostgreSQL集成完整方案

## 📋 **方案概述**

**创建时间**: 2025-07-08  
**目标**: 为Agent-Zero的金融数据工具集成PostgreSQL数据库  
**架构**: 单工具集成，混合查询模式  
**数据源**: 手动导出的财务数据 + API备选  

---

## 🎯 **业务需求**

### **核心问题**
- 某些财务指标只能从数据源导出，无法通过API获取
- 需要将导出的Excel/CSV数据存储到PostgreSQL
- 通过金融数据工具查询本地存储的财务数据

### **解决方案**
```
数据源导出 → PostgreSQL存储 → 金融数据工具查询 → 用户获得结果
```

### **实施优势**
- ✅ **风险可控**: 只修改单个工具，不影响核心系统
- ✅ **开发简单**: 代码修改量最小
- ✅ **数据完整**: 手动导出数据更准确全面
- ✅ **查询高效**: 本地数据库查询速度快
- ✅ **离线可用**: 不依赖外部API

---

## 🔧 **环境要求**

### **PostgreSQL环境**
- **版本要求**: PostgreSQL 14+ (推荐17)
- **必需扩展**: uuid-ossp, pg_trgm
- **硬件要求**: 8GB+ RAM, 100GB+ 存储空间

### **Python环境**
- **版本**: Python 3.12+ (当前Agent-Zero使用)
- **新增依赖**:
  ```txt
  psycopg2-binary==2.9.9
  sqlalchemy==2.0.23
  pandas==2.1.4
  ```

### **网络配置**
- Windows PostgreSQL服务运行在端口5432
- WSL通过Windows主机IP访问 (通常172.x.x.1)
- 防火墙允许5432端口访问

---

## 🗄️ **数据库设计**

### **核心表结构**

#### **股票基本信息表 (stocks)**
```sql
CREATE TABLE stocks (
    stock_code VARCHAR(10) PRIMARY KEY,        -- 股票代码
    stock_name VARCHAR(100) NOT NULL,          -- 股票名称
    market VARCHAR(10),                        -- 市场 SH/SZ/BJ
    industry VARCHAR(100),                     -- 行业
    sector VARCHAR(100),                       -- 板块
    list_date DATE,                           -- 上市日期
    delist_date DATE,                         -- 退市日期
    is_active BOOLEAN DEFAULT TRUE,           -- 是否活跃
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **财务指标数据表 (financial_indicators)**
```sql
CREATE TABLE financial_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stock_code VARCHAR(10) REFERENCES stocks(stock_code),
    report_date DATE NOT NULL,                -- 报告期
    report_type VARCHAR(20) NOT NULL,         -- 报告类型
    
    -- 盈利能力指标
    revenue DECIMAL(20,2),                    -- 营业收入 (万元)
    net_profit DECIMAL(20,2),                 -- 净利润 (万元)
    gross_profit DECIMAL(20,2),               -- 毛利润 (万元)
    operating_profit DECIMAL(20,2),           -- 营业利润 (万元)
    
    -- 比率指标
    gross_profit_margin DECIMAL(8,4),         -- 毛利率
    net_profit_margin DECIMAL(8,4),           -- 净利率
    roe DECIMAL(8,4),                         -- 净资产收益率
    roa DECIMAL(8,4),                         -- 总资产收益率
    
    -- 偿债能力指标
    current_ratio DECIMAL(8,4),               -- 流动比率
    quick_ratio DECIMAL(8,4),                 -- 速动比率
    debt_to_equity DECIMAL(8,4),              -- 资产负债率
    
    -- 营运能力指标
    inventory_turnover DECIMAL(8,4),          -- 存货周转率
    receivables_turnover DECIMAL(8,4),        -- 应收账款周转率
    total_asset_turnover DECIMAL(8,4),        -- 总资产周转率
    
    -- 成长能力指标
    revenue_growth DECIMAL(8,4),              -- 营收增长率
    profit_growth DECIMAL(8,4),               -- 利润增长率
    
    -- 估值指标
    pe_ratio DECIMAL(8,4),                    -- 市盈率
    pb_ratio DECIMAL(8,4),                    -- 市净率
    ps_ratio DECIMAL(8,4),                    -- 市销率
    
    -- 每股指标
    eps DECIMAL(8,4),                         -- 每股收益
    bps DECIMAL(8,4),                         -- 每股净资产
    
    -- 资产负债表主要项目
    total_assets DECIMAL(20,2),               -- 总资产
    total_liabilities DECIMAL(20,2),          -- 总负债
    shareholders_equity DECIMAL(20,2),        -- 股东权益
    
    -- 现金流量表主要项目
    operating_cash_flow DECIMAL(20,2),        -- 经营活动现金流
    investing_cash_flow DECIMAL(20,2),        -- 投资活动现金流
    financing_cash_flow DECIMAL(20,2),        -- 筹资活动现金流
    
    -- 元数据
    data_source VARCHAR(100),                 -- 数据来源
    import_batch VARCHAR(50),                 -- 导入批次
    data_quality_score INTEGER DEFAULT 100,   -- 数据质量评分
    notes TEXT,                               -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(stock_code, report_date, report_type)
);
```

### **索引设计**
```sql
-- 主要查询索引
CREATE INDEX idx_financial_stock_date ON financial_indicators(stock_code, report_date DESC);
CREATE INDEX idx_financial_date ON financial_indicators(report_date DESC);
CREATE INDEX idx_financial_type ON financial_indicators(report_type);
CREATE INDEX idx_financial_batch ON financial_indicators(import_batch);

-- 指标查询索引
CREATE INDEX idx_financial_revenue ON financial_indicators(revenue DESC);
CREATE INDEX idx_financial_profit ON financial_indicators(net_profit DESC);
CREATE INDEX idx_financial_roe ON financial_indicators(roe DESC);

-- 股票表索引
CREATE INDEX idx_stocks_market ON stocks(market);
CREATE INDEX idx_stocks_industry ON stocks(industry);
CREATE INDEX idx_stocks_active ON stocks(is_active);
```

---

## 📥 **数据导入系统**

### **Excel/CSV导入工具**
```python
# financial_data_importer.py
import pandas as pd
import psycopg2
from datetime import datetime
import uuid

class FinancialDataImporter:
    def __init__(self, db_config):
        self.db_config = db_config
        
    def import_from_excel(self, file_path, sheet_name=None):
        """从Excel文件导入财务数据"""
        
        # 读取Excel文件
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 数据清洗和标准化
        df_cleaned = self._clean_data(df)
        
        # 导入到数据库
        return self._import_to_database(df_cleaned, source=f"Excel:{file_path}")
    
    def import_from_csv(self, file_path, encoding='utf-8'):
        """从CSV文件导入财务数据"""
        
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding=encoding)
        
        # 数据清洗和标准化
        df_cleaned = self._clean_data(df)
        
        # 导入到数据库
        return self._import_to_database(df_cleaned, source=f"CSV:{file_path}")
    
    def _clean_data(self, df):
        """数据清洗和标准化"""
        
        # 标准化列名映射
        column_mapping = {
            '股票代码': 'stock_code',
            '股票名称': 'stock_name', 
            '报告期': 'report_date',
            '报告类型': 'report_type',
            '营业收入': 'revenue',
            '净利润': 'net_profit',
            '毛利润': 'gross_profit',
            '营业利润': 'operating_profit',
            'ROE': 'roe',
            '净资产收益率': 'roe',
            'ROA': 'roa',
            '总资产收益率': 'roa',
            '毛利率': 'gross_profit_margin',
            '净利率': 'net_profit_margin',
            '流动比率': 'current_ratio',
            '速动比率': 'quick_ratio',
            '资产负债率': 'debt_to_equity',
            '存货周转率': 'inventory_turnover',
            '应收账款周转率': 'receivables_turnover',
            '总资产周转率': 'total_asset_turnover',
            '营收增长率': 'revenue_growth',
            '利润增长率': 'profit_growth',
            '市盈率': 'pe_ratio',
            '市净率': 'pb_ratio',
            '市销率': 'ps_ratio',
            '每股收益': 'eps',
            '每股净资产': 'bps',
            '总资产': 'total_assets',
            '总负债': 'total_liabilities',
            '股东权益': 'shareholders_equity',
            '经营活动现金流': 'operating_cash_flow',
            '投资活动现金流': 'investing_cash_flow',
            '筹资活动现金流': 'financing_cash_flow'
        }
        
        # 重命名列
        df_renamed = df.rename(columns=column_mapping)
        
        # 数据类型转换
        if 'stock_code' in df_renamed.columns:
            df_renamed['stock_code'] = df_renamed['stock_code'].astype(str).str.zfill(6)
        
        if 'report_date' in df_renamed.columns:
            df_renamed['report_date'] = pd.to_datetime(df_renamed['report_date'])
        
        # 设置默认报告类型
        if 'report_type' not in df_renamed.columns:
            df_renamed['report_type'] = '年报'
        
        # 数值列处理
        numeric_columns = [
            'revenue', 'net_profit', 'gross_profit', 'operating_profit',
            'roe', 'roa', 'gross_profit_margin', 'net_profit_margin',
            'current_ratio', 'quick_ratio', 'debt_to_equity',
            'inventory_turnover', 'receivables_turnover', 'total_asset_turnover',
            'revenue_growth', 'profit_growth', 'pe_ratio', 'pb_ratio', 'ps_ratio',
            'eps', 'bps', 'total_assets', 'total_liabilities', 'shareholders_equity',
            'operating_cash_flow', 'investing_cash_flow', 'financing_cash_flow'
        ]
        
        for col in numeric_columns:
            if col in df_renamed.columns:
                df_renamed[col] = pd.to_numeric(df_renamed[col], errors='coerce')
        
        return df_renamed
    
    def _import_to_database(self, df, source):
        """导入数据到数据库"""
        
        batch_id = str(uuid.uuid4())[:8]
        imported_count = 0
        error_count = 0
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            
            for index, row in df.iterrows():
                try:
                    # 插入或更新股票基本信息
                    if 'stock_code' in row and 'stock_name' in row and pd.notna(row['stock_code']):
                        cursor.execute("""
                            INSERT INTO stocks (stock_code, stock_name, updated_at)
                            VALUES (%s, %s, CURRENT_TIMESTAMP)
                            ON CONFLICT (stock_code) 
                            DO UPDATE SET 
                                stock_name = EXCLUDED.stock_name, 
                                updated_at = CURRENT_TIMESTAMP
                        """, (row['stock_code'], row.get('stock_name', '')))
                    
                    # 插入财务指标数据
                    cursor.execute("""
                        INSERT INTO financial_indicators (
                            stock_code, report_date, report_type,
                            revenue, net_profit, gross_profit, operating_profit,
                            roe, roa, gross_profit_margin, net_profit_margin,
                            current_ratio, quick_ratio, debt_to_equity,
                            inventory_turnover, receivables_turnover, total_asset_turnover,
                            revenue_growth, profit_growth,
                            pe_ratio, pb_ratio, ps_ratio, eps, bps,
                            total_assets, total_liabilities, shareholders_equity,
                            operating_cash_flow, investing_cash_flow, financing_cash_flow,
                            data_source, import_batch
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s
                        )
                        ON CONFLICT (stock_code, report_date, report_type)
                        DO UPDATE SET
                            revenue = EXCLUDED.revenue,
                            net_profit = EXCLUDED.net_profit,
                            gross_profit = EXCLUDED.gross_profit,
                            operating_profit = EXCLUDED.operating_profit,
                            roe = EXCLUDED.roe,
                            roa = EXCLUDED.roa,
                            gross_profit_margin = EXCLUDED.gross_profit_margin,
                            net_profit_margin = EXCLUDED.net_profit_margin,
                            current_ratio = EXCLUDED.current_ratio,
                            quick_ratio = EXCLUDED.quick_ratio,
                            debt_to_equity = EXCLUDED.debt_to_equity,
                            inventory_turnover = EXCLUDED.inventory_turnover,
                            receivables_turnover = EXCLUDED.receivables_turnover,
                            total_asset_turnover = EXCLUDED.total_asset_turnover,
                            revenue_growth = EXCLUDED.revenue_growth,
                            profit_growth = EXCLUDED.profit_growth,
                            pe_ratio = EXCLUDED.pe_ratio,
                            pb_ratio = EXCLUDED.pb_ratio,
                            ps_ratio = EXCLUDED.ps_ratio,
                            eps = EXCLUDED.eps,
                            bps = EXCLUDED.bps,
                            total_assets = EXCLUDED.total_assets,
                            total_liabilities = EXCLUDED.total_liabilities,
                            shareholders_equity = EXCLUDED.shareholders_equity,
                            operating_cash_flow = EXCLUDED.operating_cash_flow,
                            investing_cash_flow = EXCLUDED.investing_cash_flow,
                            financing_cash_flow = EXCLUDED.financing_cash_flow,
                            data_source = EXCLUDED.data_source,
                            import_batch = EXCLUDED.import_batch,
                            updated_at = CURRENT_TIMESTAMP
                    """, (
                        row.get('stock_code'),
                        row.get('report_date'),
                        row.get('report_type', '年报'),
                        row.get('revenue'),
                        row.get('net_profit'),
                        row.get('gross_profit'),
                        row.get('operating_profit'),
                        row.get('roe'),
                        row.get('roa'),
                        row.get('gross_profit_margin'),
                        row.get('net_profit_margin'),
                        row.get('current_ratio'),
                        row.get('quick_ratio'),
                        row.get('debt_to_equity'),
                        row.get('inventory_turnover'),
                        row.get('receivables_turnover'),
                        row.get('total_asset_turnover'),
                        row.get('revenue_growth'),
                        row.get('profit_growth'),
                        row.get('pe_ratio'),
                        row.get('pb_ratio'),
                        row.get('ps_ratio'),
                        row.get('eps'),
                        row.get('bps'),
                        row.get('total_assets'),
                        row.get('total_liabilities'),
                        row.get('shareholders_equity'),
                        row.get('operating_cash_flow'),
                        row.get('investing_cash_flow'),
                        row.get('financing_cash_flow'),
                        source,
                        batch_id
                    ))
                    
                    imported_count += 1
                    
                except Exception as e:
                    error_count += 1
                    print(f"导入第{index+1}行数据失败: {e}")
                    print(f"数据内容: {row.to_dict()}")
                    continue
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"✅ 数据导入完成!")
            print(f"📊 成功导入: {imported_count} 条记录")
            print(f"❌ 导入失败: {error_count} 条记录")
            print(f"📦 导入批次: {batch_id}")
            
            return {
                'success': True,
                'imported_count': imported_count,
                'error_count': error_count,
                'batch_id': batch_id
            }
            
        except Exception as e:
            print(f"❌ 数据导入失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# 使用示例
if __name__ == "__main__":
    # 数据库配置
    db_config = {
        'host': '************',  # Windows主机IP
        'port': 5432,
        'database': 'agent_zero',
        'user': 'agent_zero',
        'password': 'your_password'
    }
    
    importer = FinancialDataImporter(db_config)
    
    # 导入Excel文件
    result = importer.import_from_excel('财务数据.xlsx', sheet_name='Sheet1')
    
    if result['success']:
        print(f"导入成功: {result['imported_count']} 条记录")
    else:
        print(f"导入失败: {result['error']}")
```

---

## 🗄️ **PostgreSQL数据库脚本**

### **1. 初始化脚本 (01_init_schema.sql)**
```sql
-- =====================================================
-- 金融数据库初始化脚本
-- 用途: 创建表结构、索引、约束
-- =====================================================

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 股票基本信息表
CREATE TABLE IF NOT EXISTS stocks (
    stock_code VARCHAR(10) PRIMARY KEY,
    stock_name VARCHAR(100) NOT NULL,
    market VARCHAR(10) CHECK (market IN ('SH', 'SZ', 'BJ')),
    industry VARCHAR(100),
    sector VARCHAR(100),
    list_date DATE,
    delist_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 财务指标数据表
CREATE TABLE IF NOT EXISTS financial_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stock_code VARCHAR(10) REFERENCES stocks(stock_code) ON DELETE CASCADE,
    report_date DATE NOT NULL,
    report_type VARCHAR(20) NOT NULL CHECK (report_type IN ('年报', '半年报', '一季报', '三季报')),

    -- 盈利能力指标
    revenue DECIMAL(20,2),                    -- 营业收入 (万元)
    net_profit DECIMAL(20,2),                 -- 净利润 (万元)
    gross_profit DECIMAL(20,2),               -- 毛利润 (万元)
    operating_profit DECIMAL(20,2),           -- 营业利润 (万元)
    total_profit DECIMAL(20,2),               -- 利润总额 (万元)

    -- 比率指标
    gross_profit_margin DECIMAL(8,4),         -- 毛利率
    net_profit_margin DECIMAL(8,4),           -- 净利率
    operating_profit_margin DECIMAL(8,4),     -- 营业利润率
    roe DECIMAL(8,4),                         -- 净资产收益率
    roa DECIMAL(8,4),                         -- 总资产收益率
    roic DECIMAL(8,4),                        -- 投入资本回报率

    -- 偿债能力指标
    current_ratio DECIMAL(8,4),               -- 流动比率
    quick_ratio DECIMAL(8,4),                 -- 速动比率
    cash_ratio DECIMAL(8,4),                  -- 现金比率
    debt_to_equity DECIMAL(8,4),              -- 资产负债率
    debt_to_asset DECIMAL(8,4),               -- 负债总资产比

    -- 营运能力指标
    inventory_turnover DECIMAL(8,4),          -- 存货周转率
    receivables_turnover DECIMAL(8,4),        -- 应收账款周转率
    total_asset_turnover DECIMAL(8,4),        -- 总资产周转率
    working_capital_turnover DECIMAL(8,4),    -- 营运资金周转率

    -- 成长能力指标
    revenue_growth DECIMAL(8,4),              -- 营收增长率
    profit_growth DECIMAL(8,4),               -- 利润增长率
    asset_growth DECIMAL(8,4),                -- 资产增长率
    equity_growth DECIMAL(8,4),               -- 净资产增长率

    -- 估值指标
    pe_ratio DECIMAL(8,4),                    -- 市盈率
    pb_ratio DECIMAL(8,4),                    -- 市净率
    ps_ratio DECIMAL(8,4),                    -- 市销率
    pcf_ratio DECIMAL(8,4),                   -- 市现率

    -- 每股指标
    eps DECIMAL(8,4),                         -- 每股收益
    bps DECIMAL(8,4),                         -- 每股净资产
    ocfps DECIMAL(8,4),                       -- 每股经营现金流

    -- 资产负债表主要项目
    total_assets DECIMAL(20,2),               -- 总资产
    total_liabilities DECIMAL(20,2),          -- 总负债
    shareholders_equity DECIMAL(20,2),        -- 股东权益

    -- 现金流量表主要项目
    operating_cash_flow DECIMAL(20,2),        -- 经营活动现金流
    investing_cash_flow DECIMAL(20,2),        -- 投资活动现金流
    financing_cash_flow DECIMAL(20,2),        -- 筹资活动现金流

    -- 元数据
    data_source VARCHAR(100),                 -- 数据来源
    import_batch VARCHAR(50),                 -- 导入批次
    data_quality_score INTEGER DEFAULT 100,   -- 数据质量评分
    notes TEXT,                               -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 唯一约束
    UNIQUE(stock_code, report_date, report_type)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_stocks_market ON stocks(market);
CREATE INDEX IF NOT EXISTS idx_stocks_industry ON stocks(industry);
CREATE INDEX IF NOT EXISTS idx_stocks_active ON stocks(is_active);

CREATE INDEX IF NOT EXISTS idx_financial_stock_date ON financial_indicators(stock_code, report_date DESC);
CREATE INDEX IF NOT EXISTS idx_financial_date ON financial_indicators(report_date DESC);
CREATE INDEX IF NOT EXISTS idx_financial_type ON financial_indicators(report_type);
CREATE INDEX IF NOT EXISTS idx_financial_batch ON financial_indicators(import_batch);
CREATE INDEX IF NOT EXISTS idx_financial_revenue ON financial_indicators(revenue DESC);
CREATE INDEX IF NOT EXISTS idx_financial_profit ON financial_indicators(net_profit DESC);
CREATE INDEX IF NOT EXISTS idx_financial_roe ON financial_indicators(roe DESC);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间触发器
CREATE TRIGGER update_stocks_updated_at
    BEFORE UPDATE ON stocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_indicators_updated_at
    BEFORE UPDATE ON financial_indicators
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **2. 查询函数脚本 (02_query_functions.sql)**
```sql
-- =====================================================
-- 金融数据查询函数
-- 用途: 为金融工具提供标准化查询接口
-- =====================================================

-- 获取股票基本信息
CREATE OR REPLACE FUNCTION get_stock_info(p_stock_code VARCHAR)
RETURNS TABLE (
    stock_code VARCHAR,
    stock_name VARCHAR,
    market VARCHAR,
    industry VARCHAR,
    list_date DATE,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.stock_code, s.stock_name, s.market, s.industry, s.list_date, s.is_active
    FROM stocks s
    WHERE s.stock_code = p_stock_code;
END;
$$ LANGUAGE plpgsql;

-- 获取最新财务数据
CREATE OR REPLACE FUNCTION get_latest_financial_data(p_stock_code VARCHAR)
RETURNS TABLE (
    stock_code VARCHAR,
    stock_name VARCHAR,
    report_date DATE,
    report_type VARCHAR,
    revenue DECIMAL,
    net_profit DECIMAL,
    roe DECIMAL,
    pe_ratio DECIMAL,
    pb_ratio DECIMAL,
    eps DECIMAL,
    bps DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fi.stock_code,
        s.stock_name,
        fi.report_date,
        fi.report_type,
        fi.revenue,
        fi.net_profit,
        fi.roe,
        fi.pe_ratio,
        fi.pb_ratio,
        fi.eps,
        fi.bps
    FROM financial_indicators fi
    JOIN stocks s ON fi.stock_code = s.stock_code
    WHERE fi.stock_code = p_stock_code
    ORDER BY fi.report_date DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 获取指定指标的历史数据
CREATE OR REPLACE FUNCTION get_indicator_history(
    p_stock_code VARCHAR,
    p_indicator VARCHAR,
    p_periods INTEGER DEFAULT 8
)
RETURNS TABLE (
    report_date DATE,
    report_type VARCHAR,
    indicator_value DECIMAL
) AS $$
BEGIN
    CASE p_indicator
        WHEN 'revenue' THEN
            RETURN QUERY
            SELECT fi.report_date, fi.report_type, fi.revenue
            FROM financial_indicators fi
            WHERE fi.stock_code = p_stock_code AND fi.revenue IS NOT NULL
            ORDER BY fi.report_date DESC
            LIMIT p_periods;

        WHEN 'net_profit' THEN
            RETURN QUERY
            SELECT fi.report_date, fi.report_type, fi.net_profit
            FROM financial_indicators fi
            WHERE fi.stock_code = p_stock_code AND fi.net_profit IS NOT NULL
            ORDER BY fi.report_date DESC
            LIMIT p_periods;

        WHEN 'roe' THEN
            RETURN QUERY
            SELECT fi.report_date, fi.report_type, fi.roe
            FROM financial_indicators fi
            WHERE fi.stock_code = p_stock_code AND fi.roe IS NOT NULL
            ORDER BY fi.report_date DESC
            LIMIT p_periods;

        WHEN 'gross_profit_margin' THEN
            RETURN QUERY
            SELECT fi.report_date, fi.report_type, fi.gross_profit_margin
            FROM financial_indicators fi
            WHERE fi.stock_code = p_stock_code AND fi.gross_profit_margin IS NOT NULL
            ORDER BY fi.report_date DESC
            LIMIT p_periods;

        WHEN 'pe_ratio' THEN
            RETURN QUERY
            SELECT fi.report_date, fi.report_type, fi.pe_ratio
            FROM financial_indicators fi
            WHERE fi.stock_code = p_stock_code AND fi.pe_ratio IS NOT NULL
            ORDER BY fi.report_date DESC
            LIMIT p_periods;

        ELSE
            -- 默认返回营业收入
            RETURN QUERY
            SELECT fi.report_date, fi.report_type, fi.revenue
            FROM financial_indicators fi
            WHERE fi.stock_code = p_stock_code AND fi.revenue IS NOT NULL
            ORDER BY fi.report_date DESC
            LIMIT p_periods;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- 计算增长率
CREATE OR REPLACE FUNCTION calculate_growth_rate(
    p_stock_code VARCHAR,
    p_indicator VARCHAR,
    p_periods INTEGER DEFAULT 4
)
RETURNS TABLE (
    report_date DATE,
    indicator_value DECIMAL,
    yoy_growth DECIMAL,
    qoq_growth DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH indicator_data AS (
        SELECT * FROM get_indicator_history(p_stock_code, p_indicator, p_periods)
    ),
    growth_calc AS (
        SELECT
            id.report_date,
            id.indicator_value,
            LAG(id.indicator_value, 4) OVER (ORDER BY id.report_date) AS prev_year_value,
            LAG(id.indicator_value, 1) OVER (ORDER BY id.report_date) AS prev_quarter_value
        FROM indicator_data id
    )
    SELECT
        gc.report_date,
        gc.indicator_value,
        CASE
            WHEN gc.prev_year_value IS NOT NULL AND gc.prev_year_value != 0
            THEN (gc.indicator_value - gc.prev_year_value) / gc.prev_year_value
            ELSE NULL
        END AS yoy_growth,
        CASE
            WHEN gc.prev_quarter_value IS NOT NULL AND gc.prev_quarter_value != 0
            THEN (gc.indicator_value - gc.prev_quarter_value) / gc.prev_quarter_value
            ELSE NULL
        END AS qoq_growth
    FROM growth_calc gc
    ORDER BY gc.report_date DESC;
END;
$$ LANGUAGE plpgsql;

-- 行业对比分析
CREATE OR REPLACE FUNCTION get_industry_comparison(
    p_stock_code VARCHAR,
    p_indicator VARCHAR DEFAULT 'roe'
)
RETURNS TABLE (
    stock_code VARCHAR,
    stock_name VARCHAR,
    indicator_value DECIMAL,
    industry_rank INTEGER,
    industry_percentile DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_data AS (
        SELECT
            fi.stock_code,
            s.stock_name,
            s.industry,
            CASE p_indicator
                WHEN 'roe' THEN fi.roe
                WHEN 'revenue' THEN fi.revenue
                WHEN 'net_profit' THEN fi.net_profit
                WHEN 'gross_profit_margin' THEN fi.gross_profit_margin
                WHEN 'pe_ratio' THEN fi.pe_ratio
                WHEN 'pb_ratio' THEN fi.pb_ratio
                ELSE fi.roe
            END AS indicator_value,
            ROW_NUMBER() OVER (PARTITION BY fi.stock_code ORDER BY fi.report_date DESC) as rn
        FROM financial_indicators fi
        JOIN stocks s ON fi.stock_code = s.stock_code
        WHERE s.industry = (SELECT industry FROM stocks WHERE stock_code = p_stock_code)
    ),
    ranked_data AS (
        SELECT
            ld.stock_code,
            ld.stock_name,
            ld.indicator_value,
            RANK() OVER (ORDER BY ld.indicator_value DESC) as industry_rank,
            PERCENT_RANK() OVER (ORDER BY ld.indicator_value DESC) as percentile
        FROM latest_data ld
        WHERE ld.rn = 1 AND ld.indicator_value IS NOT NULL
    )
    SELECT
        rd.stock_code,
        rd.stock_name,
        rd.indicator_value,
        rd.industry_rank,
        (1 - rd.percentile) * 100 as industry_percentile
    FROM ranked_data rd
    ORDER BY rd.industry_rank;
END;
$$ LANGUAGE plpgsql;

-- 综合财务评分
CREATE OR REPLACE FUNCTION get_financial_score(p_stock_code VARCHAR)
RETURNS TABLE (
    stock_code VARCHAR,
    stock_name VARCHAR,
    profitability_score INTEGER,
    growth_score INTEGER,
    solvency_score INTEGER,
    efficiency_score INTEGER,
    overall_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH latest_data AS (
        SELECT
            fi.stock_code,
            s.stock_name,
            fi.roe,
            fi.net_profit_margin,
            fi.revenue_growth,
            fi.profit_growth,
            fi.current_ratio,
            fi.debt_to_equity,
            fi.total_asset_turnover,
            fi.receivables_turnover
        FROM financial_indicators fi
        JOIN stocks s ON fi.stock_code = s.stock_code
        WHERE fi.stock_code = p_stock_code
        ORDER BY fi.report_date DESC
        LIMIT 1
    )
    SELECT
        ld.stock_code,
        ld.stock_name,
        -- 盈利能力评分 (0-100)
        CASE
            WHEN ld.roe >= 0.15 AND ld.net_profit_margin >= 0.10 THEN 90
            WHEN ld.roe >= 0.10 AND ld.net_profit_margin >= 0.05 THEN 70
            WHEN ld.roe >= 0.05 AND ld.net_profit_margin >= 0.02 THEN 50
            ELSE 30
        END AS profitability_score,
        -- 成长能力评分 (0-100)
        CASE
            WHEN ld.revenue_growth >= 0.20 AND ld.profit_growth >= 0.20 THEN 90
            WHEN ld.revenue_growth >= 0.10 AND ld.profit_growth >= 0.10 THEN 70
            WHEN ld.revenue_growth >= 0.05 AND ld.profit_growth >= 0.05 THEN 50
            ELSE 30
        END AS growth_score,
        -- 偿债能力评分 (0-100)
        CASE
            WHEN ld.current_ratio >= 2.0 AND ld.debt_to_equity <= 0.3 THEN 90
            WHEN ld.current_ratio >= 1.5 AND ld.debt_to_equity <= 0.5 THEN 70
            WHEN ld.current_ratio >= 1.0 AND ld.debt_to_equity <= 0.7 THEN 50
            ELSE 30
        END AS solvency_score,
        -- 运营效率评分 (0-100)
        CASE
            WHEN ld.total_asset_turnover >= 1.0 AND ld.receivables_turnover >= 6.0 THEN 90
            WHEN ld.total_asset_turnover >= 0.7 AND ld.receivables_turnover >= 4.0 THEN 70
            WHEN ld.total_asset_turnover >= 0.5 AND ld.receivables_turnover >= 2.0 THEN 50
            ELSE 30
        END AS efficiency_score,
        -- 综合评分
        (
            CASE
                WHEN ld.roe >= 0.15 AND ld.net_profit_margin >= 0.10 THEN 90
                WHEN ld.roe >= 0.10 AND ld.net_profit_margin >= 0.05 THEN 70
                WHEN ld.roe >= 0.05 AND ld.net_profit_margin >= 0.02 THEN 50
                ELSE 30
            END +
            CASE
                WHEN ld.revenue_growth >= 0.20 AND ld.profit_growth >= 0.20 THEN 90
                WHEN ld.revenue_growth >= 0.10 AND ld.profit_growth >= 0.10 THEN 70
                WHEN ld.revenue_growth >= 0.05 AND ld.profit_growth >= 0.05 THEN 50
                ELSE 30
            END +
            CASE
                WHEN ld.current_ratio >= 2.0 AND ld.debt_to_equity <= 0.3 THEN 90
                WHEN ld.current_ratio >= 1.5 AND ld.debt_to_equity <= 0.5 THEN 70
                WHEN ld.current_ratio >= 1.0 AND ld.debt_to_equity <= 0.7 THEN 50
                ELSE 30
            END +
            CASE
                WHEN ld.total_asset_turnover >= 1.0 AND ld.receivables_turnover >= 6.0 THEN 90
                WHEN ld.total_asset_turnover >= 0.7 AND ld.receivables_turnover >= 4.0 THEN 70
                WHEN ld.total_asset_turnover >= 0.5 AND ld.receivables_turnover >= 2.0 THEN 50
                ELSE 30
            END
        ) / 4 AS overall_score
    FROM latest_data ld;
END;
$$ LANGUAGE plpgsql;
```

### **3. 数据维护脚本 (03_data_maintenance.sql)**
```sql
-- =====================================================
-- 数据维护和管理脚本
-- 用途: 数据清理、更新、统计
-- =====================================================

-- 数据质量检查
CREATE OR REPLACE FUNCTION check_data_quality()
RETURNS TABLE (
    check_type VARCHAR,
    issue_count BIGINT,
    description TEXT
) AS $$
BEGIN
    -- 检查缺失的股票基本信息
    RETURN QUERY
    SELECT
        'missing_stock_info'::VARCHAR,
        COUNT(*)::BIGINT,
        '财务数据中存在但股票表中缺失的股票代码'::TEXT
    FROM (
        SELECT DISTINCT fi.stock_code
        FROM financial_indicators fi
        LEFT JOIN stocks s ON fi.stock_code = s.stock_code
        WHERE s.stock_code IS NULL
    ) missing;

    -- 检查重复数据
    RETURN QUERY
    SELECT
        'duplicate_records'::VARCHAR,
        COUNT(*)::BIGINT,
        '重复的财务数据记录'::TEXT
    FROM (
        SELECT stock_code, report_date, report_type, COUNT(*) as cnt
        FROM financial_indicators
        GROUP BY stock_code, report_date, report_type
        HAVING COUNT(*) > 1
    ) duplicates;

    -- 检查异常数据
    RETURN QUERY
    SELECT
        'abnormal_roe'::VARCHAR,
        COUNT(*)::BIGINT,
        'ROE超过100%或小于-100%的异常数据'::TEXT
    FROM financial_indicators
    WHERE roe > 1.0 OR roe < -1.0;

    -- 检查空值数据
    RETURN QUERY
    SELECT
        'missing_revenue'::VARCHAR,
        COUNT(*)::BIGINT,
        '缺失营业收入数据的记录'::TEXT
    FROM financial_indicators
    WHERE revenue IS NULL;

END;
$$ LANGUAGE plpgsql;

-- 清理重复数据
CREATE OR REPLACE FUNCTION clean_duplicate_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH duplicates AS (
        SELECT id,
               ROW_NUMBER() OVER (
                   PARTITION BY stock_code, report_date, report_type
                   ORDER BY created_at DESC
               ) as rn
        FROM financial_indicators
    )
    DELETE FROM financial_indicators
    WHERE id IN (
        SELECT id FROM duplicates WHERE rn > 1
    );

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 更新数据质量评分
CREATE OR REPLACE FUNCTION update_data_quality_scores()
RETURNS VOID AS $$
BEGIN
    UPDATE financial_indicators
    SET data_quality_score = (
        CASE
            WHEN revenue IS NULL THEN 0
            WHEN net_profit IS NULL THEN 20
            WHEN roe IS NULL THEN 40
            WHEN pe_ratio IS NULL THEN 60
            WHEN pb_ratio IS NULL THEN 80
            ELSE 100
        END
    );
END;
$$ LANGUAGE plpgsql;

-- 获取数据统计信息
CREATE OR REPLACE FUNCTION get_data_statistics()
RETURNS TABLE (
    metric VARCHAR,
    value BIGINT,
    description TEXT
) AS $$
BEGIN
    -- 股票总数
    RETURN QUERY
    SELECT
        'total_stocks'::VARCHAR,
        COUNT(*)::BIGINT,
        '股票总数'::TEXT
    FROM stocks;

    -- 财务记录总数
    RETURN QUERY
    SELECT
        'total_financial_records'::VARCHAR,
        COUNT(*)::BIGINT,
        '财务记录总数'::TEXT
    FROM financial_indicators;

    -- 最新数据日期
    RETURN QUERY
    SELECT
        'latest_data_date'::VARCHAR,
        EXTRACT(YEAR FROM MAX(report_date))::BIGINT,
        '最新数据年份'::TEXT
    FROM financial_indicators;

    -- 数据覆盖的年份范围
    RETURN QUERY
    SELECT
        'data_year_span'::VARCHAR,
        (EXTRACT(YEAR FROM MAX(report_date)) - EXTRACT(YEAR FROM MIN(report_date)) + 1)::BIGINT,
        '数据覆盖年份数'::TEXT
    FROM financial_indicators;

    -- 活跃股票数
    RETURN QUERY
    SELECT
        'active_stocks'::VARCHAR,
        COUNT(*)::BIGINT,
        '活跃股票数'::TEXT
    FROM stocks
    WHERE is_active = TRUE;

END;
$$ LANGUAGE plpgsql;

-- 批量更新股票行业信息
CREATE OR REPLACE FUNCTION update_stock_industry(
    p_stock_code VARCHAR,
    p_industry VARCHAR,
    p_sector VARCHAR DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE stocks
    SET
        industry = p_industry,
        sector = COALESCE(p_sector, sector),
        updated_at = CURRENT_TIMESTAMP
    WHERE stock_code = p_stock_code;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;
```

### **4. 业务视图脚本 (04_business_views.sql)**
```sql
-- =====================================================
-- 业务视图定义
-- 用途: 简化常用查询，提供业务友好的数据视图
-- =====================================================

-- 最新财务数据视图
CREATE OR REPLACE VIEW v_latest_financial_data AS
WITH latest_reports AS (
    SELECT
        stock_code,
        MAX(report_date) as latest_date
    FROM financial_indicators
    GROUP BY stock_code
)
SELECT
    s.stock_code,
    s.stock_name,
    s.market,
    s.industry,
    s.sector,
    fi.report_date,
    fi.report_type,
    fi.revenue,
    fi.net_profit,
    fi.gross_profit,
    fi.operating_profit,
    fi.roe,
    fi.roa,
    fi.gross_profit_margin,
    fi.net_profit_margin,
    fi.operating_profit_margin,
    fi.pe_ratio,
    fi.pb_ratio,
    fi.ps_ratio,
    fi.eps,
    fi.bps,
    fi.debt_to_equity,
    fi.current_ratio,
    fi.quick_ratio,
    fi.total_asset_turnover,
    fi.inventory_turnover,
    fi.receivables_turnover,
    fi.revenue_growth,
    fi.profit_growth,
    fi.total_assets,
    fi.total_liabilities,
    fi.shareholders_equity,
    fi.operating_cash_flow,
    fi.data_quality_score
FROM stocks s
JOIN financial_indicators fi ON s.stock_code = fi.stock_code
JOIN latest_reports lr ON fi.stock_code = lr.stock_code AND fi.report_date = lr.latest_date;

-- 行业排名视图
CREATE OR REPLACE VIEW v_industry_rankings AS
SELECT
    industry,
    stock_code,
    stock_name,
    roe,
    RANK() OVER (PARTITION BY industry ORDER BY roe DESC) as roe_rank,
    revenue,
    RANK() OVER (PARTITION BY industry ORDER BY revenue DESC) as revenue_rank,
    net_profit,
    RANK() OVER (PARTITION BY industry ORDER BY net_profit DESC) as profit_rank,
    pe_ratio,
    RANK() OVER (PARTITION BY industry ORDER BY pe_ratio ASC) as pe_rank,
    pb_ratio,
    RANK() OVER (PARTITION BY industry ORDER BY pb_ratio ASC) as pb_rank
FROM v_latest_financial_data
WHERE roe IS NOT NULL AND industry IS NOT NULL;

-- 成长性分析视图
CREATE OR REPLACE VIEW v_growth_analysis AS
WITH yearly_data AS (
    SELECT
        stock_code,
        EXTRACT(YEAR FROM report_date) as year,
        SUM(CASE WHEN report_type = '年报' THEN revenue ELSE 0 END) as annual_revenue,
        SUM(CASE WHEN report_type = '年报' THEN net_profit ELSE 0 END) as annual_profit
    FROM financial_indicators
    WHERE report_type = '年报'
    GROUP BY stock_code, EXTRACT(YEAR FROM report_date)
),
growth_calc AS (
    SELECT
        stock_code,
        year,
        annual_revenue,
        annual_profit,
        LAG(annual_revenue) OVER (PARTITION BY stock_code ORDER BY year) as prev_revenue,
        LAG(annual_profit) OVER (PARTITION BY stock_code ORDER BY year) as prev_profit
    FROM yearly_data
    WHERE annual_revenue > 0
)
SELECT
    s.stock_code,
    s.stock_name,
    s.industry,
    gc.year,
    gc.annual_revenue,
    gc.annual_profit,
    CASE
        WHEN gc.prev_revenue > 0
        THEN (gc.annual_revenue - gc.prev_revenue) / gc.prev_revenue
        ELSE NULL
    END as revenue_growth_rate,
    CASE
        WHEN gc.prev_profit > 0
        THEN (gc.annual_profit - gc.prev_profit) / gc.prev_profit
        ELSE NULL
    END as profit_growth_rate
FROM growth_calc gc
JOIN stocks s ON gc.stock_code = s.stock_code
WHERE gc.prev_revenue IS NOT NULL;

-- 估值分析视图
CREATE OR REPLACE VIEW v_valuation_analysis AS
SELECT
    stock_code,
    stock_name,
    industry,
    pe_ratio,
    pb_ratio,
    ps_ratio,
    -- 行业平均估值
    AVG(pe_ratio) OVER (PARTITION BY industry) as industry_avg_pe,
    AVG(pb_ratio) OVER (PARTITION BY industry) as industry_avg_pb,
    AVG(ps_ratio) OVER (PARTITION BY industry) as industry_avg_ps,
    -- 估值相对位置
    CASE
        WHEN pe_ratio < AVG(pe_ratio) OVER (PARTITION BY industry) * 0.8 THEN '低估'
        WHEN pe_ratio > AVG(pe_ratio) OVER (PARTITION BY industry) * 1.2 THEN '高估'
        ELSE '合理'
    END as pe_valuation_level,
    CASE
        WHEN pb_ratio < AVG(pb_ratio) OVER (PARTITION BY industry) * 0.8 THEN '低估'
        WHEN pb_ratio > AVG(pb_ratio) OVER (PARTITION BY industry) * 1.2 THEN '高估'
        ELSE '合理'
    END as pb_valuation_level,
    -- 估值百分位
    PERCENT_RANK() OVER (PARTITION BY industry ORDER BY pe_ratio) * 100 as pe_percentile,
    PERCENT_RANK() OVER (PARTITION BY industry ORDER BY pb_ratio) * 100 as pb_percentile
FROM v_latest_financial_data
WHERE pe_ratio IS NOT NULL AND pb_ratio IS NOT NULL AND industry IS NOT NULL;

-- 财务健康度视图
CREATE OR REPLACE VIEW v_financial_health AS
SELECT
    stock_code,
    stock_name,
    industry,
    -- 盈利能力评级
    CASE
        WHEN roe >= 0.15 AND net_profit_margin >= 0.10 THEN 'A'
        WHEN roe >= 0.10 AND net_profit_margin >= 0.05 THEN 'B'
        WHEN roe >= 0.05 AND net_profit_margin >= 0.02 THEN 'C'
        ELSE 'D'
    END as profitability_grade,
    -- 偿债能力评级
    CASE
        WHEN current_ratio >= 2.0 AND debt_to_equity <= 0.3 THEN 'A'
        WHEN current_ratio >= 1.5 AND debt_to_equity <= 0.5 THEN 'B'
        WHEN current_ratio >= 1.0 AND debt_to_equity <= 0.7 THEN 'C'
        ELSE 'D'
    END as solvency_grade,
    -- 成长能力评级
    CASE
        WHEN revenue_growth >= 0.20 AND profit_growth >= 0.20 THEN 'A'
        WHEN revenue_growth >= 0.10 AND profit_growth >= 0.10 THEN 'B'
        WHEN revenue_growth >= 0.05 AND profit_growth >= 0.05 THEN 'C'
        ELSE 'D'
    END as growth_grade,
    -- 综合评级
    CASE
        WHEN (
            CASE WHEN roe >= 0.15 AND net_profit_margin >= 0.10 THEN 4
                 WHEN roe >= 0.10 AND net_profit_margin >= 0.05 THEN 3
                 WHEN roe >= 0.05 AND net_profit_margin >= 0.02 THEN 2
                 ELSE 1 END +
            CASE WHEN current_ratio >= 2.0 AND debt_to_equity <= 0.3 THEN 4
                 WHEN current_ratio >= 1.5 AND debt_to_equity <= 0.5 THEN 3
                 WHEN current_ratio >= 1.0 AND debt_to_equity <= 0.7 THEN 2
                 ELSE 1 END +
            CASE WHEN revenue_growth >= 0.20 AND profit_growth >= 0.20 THEN 4
                 WHEN revenue_growth >= 0.10 AND profit_growth >= 0.10 THEN 3
                 WHEN revenue_growth >= 0.05 AND profit_growth >= 0.05 THEN 2
                 ELSE 1 END
        ) / 3.0 >= 3.5 THEN 'A'
        WHEN (
            CASE WHEN roe >= 0.15 AND net_profit_margin >= 0.10 THEN 4
                 WHEN roe >= 0.10 AND net_profit_margin >= 0.05 THEN 3
                 WHEN roe >= 0.05 AND net_profit_margin >= 0.02 THEN 2
                 ELSE 1 END +
            CASE WHEN current_ratio >= 2.0 AND debt_to_equity <= 0.3 THEN 4
                 WHEN current_ratio >= 1.5 AND debt_to_equity <= 0.5 THEN 3
                 WHEN current_ratio >= 1.0 AND debt_to_equity <= 0.7 THEN 2
                 ELSE 1 END +
            CASE WHEN revenue_growth >= 0.20 AND profit_growth >= 0.20 THEN 4
                 WHEN revenue_growth >= 0.10 AND profit_growth >= 0.10 THEN 3
                 WHEN revenue_growth >= 0.05 AND profit_growth >= 0.05 THEN 2
                 ELSE 1 END
        ) / 3.0 >= 2.5 THEN 'B'
        WHEN (
            CASE WHEN roe >= 0.15 AND net_profit_margin >= 0.10 THEN 4
                 WHEN roe >= 0.10 AND net_profit_margin >= 0.05 THEN 3
                 WHEN roe >= 0.05 AND net_profit_margin >= 0.02 THEN 2
                 ELSE 1 END +
            CASE WHEN current_ratio >= 2.0 AND debt_to_equity <= 0.3 THEN 4
                 WHEN current_ratio >= 1.5 AND debt_to_equity <= 0.5 THEN 3
                 WHEN current_ratio >= 1.0 AND debt_to_equity <= 0.7 THEN 2
                 ELSE 1 END +
            CASE WHEN revenue_growth >= 0.20 AND profit_growth >= 0.20 THEN 4
                 WHEN revenue_growth >= 0.10 AND profit_growth >= 0.10 THEN 3
                 WHEN revenue_growth >= 0.05 AND profit_growth >= 0.05 THEN 2
                 ELSE 1 END
        ) / 3.0 >= 1.5 THEN 'C'
        ELSE 'D'
    END as overall_grade,
    roe,
    net_profit_margin,
    current_ratio,
    debt_to_equity,
    revenue_growth,
    profit_growth
FROM v_latest_financial_data;
```

---

## 🔧 **金融工具修改**

### **数据库接口类 (financial_db_interface.py)**
```python
# python/helpers/financial_db_interface.py
import psycopg2
import os
from typing import Dict, List, Optional, Tuple
from decimal import Decimal

class FinancialDBInterface:
    """金融数据库接口类"""

    def __init__(self):
        self.db_config = self._get_db_config()

    def _get_db_config(self) -> Dict:
        """获取数据库配置"""
        return {
            'host': os.getenv('DB_HOST', '************'),
            'port': int(os.getenv('DB_PORT', '5432')),
            'database': os.getenv('DB_NAME', 'agent_zero'),
            'user': os.getenv('DB_USER', 'agent_zero'),
            'password': os.getenv('DB_PASSWORD', ''),
            'connect_timeout': 10
        }

    def get_stock_info(self, stock_code: str) -> Optional[Dict]:
        """获取股票基本信息"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM get_stock_info(%s)", (stock_code,))
            result = cursor.fetchone()

            if result:
                return {
                    'stock_code': result[0],
                    'stock_name': result[1],
                    'market': result[2],
                    'industry': result[3],
                    'list_date': result[4],
                    'is_active': result[5]
                }
            return None

        except Exception as e:
            print(f"查询股票信息失败: {e}")
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def get_latest_financial_data(self, stock_code: str) -> Optional[Dict]:
        """获取最新财务数据"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM get_latest_financial_data(%s)", (stock_code,))
            result = cursor.fetchone()

            if result:
                return {
                    'stock_code': result[0],
                    'stock_name': result[1],
                    'report_date': result[2],
                    'report_type': result[3],
                    'revenue': float(result[4]) if result[4] else None,
                    'net_profit': float(result[5]) if result[5] else None,
                    'roe': float(result[6]) if result[6] else None,
                    'pe_ratio': float(result[7]) if result[7] else None,
                    'pb_ratio': float(result[8]) if result[8] else None,
                    'eps': float(result[9]) if result[9] else None,
                    'bps': float(result[10]) if result[10] else None
                }
            return None

        except Exception as e:
            print(f"查询最新财务数据失败: {e}")
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def get_indicator_history(self, stock_code: str, indicator: str, periods: int = 8) -> List[Dict]:
        """获取指标历史数据"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT * FROM get_indicator_history(%s, %s, %s)",
                (stock_code, indicator, periods)
            )
            results = cursor.fetchall()

            return [
                {
                    'report_date': row[0],
                    'report_type': row[1],
                    'indicator_value': float(row[2]) if row[2] else None
                }
                for row in results
            ]

        except Exception as e:
            print(f"查询指标历史失败: {e}")
            return []
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def get_industry_comparison(self, stock_code: str, indicator: str = 'roe') -> List[Dict]:
        """获取行业对比数据"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT * FROM get_industry_comparison(%s, %s)",
                (stock_code, indicator)
            )
            results = cursor.fetchall()

            return [
                {
                    'stock_code': row[0],
                    'stock_name': row[1],
                    'indicator_value': float(row[2]) if row[2] else None,
                    'industry_rank': row[3],
                    'industry_percentile': float(row[4]) if row[4] else None
                }
                for row in results
            ]

        except Exception as e:
            print(f"查询行业对比失败: {e}")
            return []
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def get_financial_score(self, stock_code: str) -> Optional[Dict]:
        """获取财务评分"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM get_financial_score(%s)", (stock_code,))
            result = cursor.fetchone()

            if result:
                return {
                    'stock_code': result[0],
                    'stock_name': result[1],
                    'profitability_score': result[2],
                    'growth_score': result[3],
                    'solvency_score': result[4],
                    'efficiency_score': result[5],
                    'overall_score': result[6]
                }
            return None

        except Exception as e:
            print(f"查询财务评分失败: {e}")
            return None
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def search_stocks_by_criteria(self, criteria: Dict) -> List[Dict]:
        """根据条件搜索股票"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # 构建查询条件
            where_conditions = []
            params = []

            if criteria.get('industry'):
                where_conditions.append("industry = %s")
                params.append(criteria['industry'])

            if criteria.get('min_roe'):
                where_conditions.append("roe >= %s")
                params.append(criteria['min_roe'])

            if criteria.get('max_pe'):
                where_conditions.append("pe_ratio <= %s")
                params.append(criteria['max_pe'])

            if criteria.get('min_revenue'):
                where_conditions.append("revenue >= %s")
                params.append(criteria['min_revenue'])

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            sql = f"""
                SELECT stock_code, stock_name, industry, roe, pe_ratio, pb_ratio, revenue, net_profit
                FROM v_latest_financial_data
                WHERE {where_clause}
                ORDER BY roe DESC
                LIMIT 50
            """

            cursor.execute(sql, params)
            results = cursor.fetchall()

            return [
                {
                    'stock_code': row[0],
                    'stock_name': row[1],
                    'industry': row[2],
                    'roe': float(row[3]) if row[3] else None,
                    'pe_ratio': float(row[4]) if row[4] else None,
                    'pb_ratio': float(row[5]) if row[5] else None,
                    'revenue': float(row[6]) if row[6] else None,
                    'net_profit': float(row[7]) if row[7] else None
                }
                for row in results
            ]

        except Exception as e:
            print(f"搜索股票失败: {e}")
            return []
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def get_data_statistics(self) -> Dict:
        """获取数据统计信息"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM get_data_statistics()")
            results = cursor.fetchall()

            stats = {}
            for row in results:
                stats[row[0]] = {
                    'value': row[1],
                    'description': row[2]
                }

            return stats

        except Exception as e:
            print(f"查询数据统计失败: {e}")
            return {}
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            return result[0] == 1
        except Exception as e:
            print(f"数据库连接测试失败: {e}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
```

### **修改后的金融数据工具 (financial_data_tool.py)**
```python
# python/tools/financial_data_tool.py
from python.helpers.tool import Tool, Response
from python.helpers.financial_db_interface import FinancialDBInterface
import re

class FinancialDataTool(Tool):
    """
    金融数据查询工具 - 集成PostgreSQL本地数据库
    支持查询股票财务指标、行业对比、历史趋势等
    """

    def __init__(self):
        self.db_interface = FinancialDBInterface()

    async def execute(self, stock_code="", indicator="", period="", analysis_type="", **kwargs):
        """
        查询财务数据

        Args:
            stock_code: 股票代码 (如: 000858, 五粮液)
            indicator: 指标名称 (如: 营业收入, ROE, 净利润, 市盈率)
            period: 时间期间 (如: 最新, 历史, 2023年报)
            analysis_type: 分析类型 (如: 基本信息, 行业对比, 财务评分, 历史趋势)
        """

        # 参数预处理
        stock_code = self._normalize_stock_code(stock_code)
        indicator = self._normalize_indicator(indicator)

        if not stock_code:
            return Response(message="❌ 请提供股票代码或股票名称")

        try:
            # 1. 测试数据库连接
            if not self.db_interface.test_connection():
                return Response(message="❌ 数据库连接失败，请检查配置")

            # 2. 根据分析类型选择处理方式
            if analysis_type == "基本信息" or (not indicator and not analysis_type):
                return await self._get_basic_info(stock_code)
            elif analysis_type == "行业对比":
                return await self._get_industry_comparison(stock_code, indicator)
            elif analysis_type == "财务评分":
                return await self._get_financial_score(stock_code)
            elif analysis_type == "历史趋势" or period in ["历史", "趋势"]:
                return await self._get_historical_trend(stock_code, indicator)
            else:
                # 默认查询最新财务数据
                return await self._get_latest_data(stock_code, indicator)

        except Exception as e:
            return Response(message=f"❌ 查询财务数据时出错: {e}")

    def _normalize_stock_code(self, input_code: str) -> str:
        """标准化股票代码"""
        if not input_code:
            return ""

        # 移除空格和特殊字符
        code = re.sub(r'[^\w\u4e00-\u9fff]', '', input_code)

        # 如果是6位数字，直接返回
        if re.match(r'^\d{6}$', code):
            return code

        # 如果是股票名称，尝试查找对应代码
        if re.search(r'[\u4e00-\u9fff]', code):  # 包含中文
            # 这里可以添加股票名称到代码的映射逻辑
            name_to_code = {
                '五粮液': '000858',
                '平安银行': '000001',
                '茅台': '600519',
                '贵州茅台': '600519'
            }
            return name_to_code.get(code, code)

        return code.zfill(6) if code.isdigit() else code

    def _normalize_indicator(self, indicator: str) -> str:
        """标准化指标名称"""
        if not indicator:
            return ""

        # 指标名称映射
        indicator_mapping = {
            '营业收入': 'revenue',
            '收入': 'revenue',
            '营收': 'revenue',
            '净利润': 'net_profit',
            '利润': 'net_profit',
            'ROE': 'roe',
            '净资产收益率': 'roe',
            'ROA': 'roa',
            '总资产收益率': 'roa',
            '毛利率': 'gross_profit_margin',
            '净利率': 'net_profit_margin',
            '市盈率': 'pe_ratio',
            'PE': 'pe_ratio',
            '市净率': 'pb_ratio',
            'PB': 'pb_ratio',
            '每股收益': 'eps',
            'EPS': 'eps'
        }

        return indicator_mapping.get(indicator, indicator.lower())

    async def _get_basic_info(self, stock_code: str) -> Response:
        """获取股票基本信息和最新财务数据"""

        # 获取股票基本信息
        stock_info = self.db_interface.get_stock_info(stock_code)
        if not stock_info:
            return Response(message=f"❌ 未找到股票代码 {stock_code} 的信息，请检查代码是否正确")

        # 获取最新财务数据
        financial_data = self.db_interface.get_latest_financial_data(stock_code)
        if not financial_data:
            return Response(message=f"❌ 未找到股票 {stock_code} 的财务数据，请先导入相关数据")

        # 格式化结果
        result = f"📊 **{financial_data['stock_name']} ({financial_data['stock_code']}) 财务概览**\n\n"

        # 基本信息
        result += f"🏢 **基本信息**:\n"
        result += f"  • 市场: {stock_info['market']}\n"
        if stock_info['industry']:
            result += f"  • 行业: {stock_info['industry']}\n"
        if stock_info['list_date']:
            result += f"  • 上市日期: {stock_info['list_date']}\n"

        # 最新财务数据
        result += f"\n📅 **最新财务数据** ({financial_data['report_date']} {financial_data['report_type']}):\n"

        if financial_data['revenue']:
            result += f"  • 营业收入: {financial_data['revenue']:,.2f} 万元\n"
        if financial_data['net_profit']:
            result += f"  • 净利润: {financial_data['net_profit']:,.2f} 万元\n"
        if financial_data['roe']:
            result += f"  • ROE: {financial_data['roe']:.2%}\n"
        if financial_data['eps']:
            result += f"  • 每股收益: {financial_data['eps']:.2f} 元\n"
        if financial_data['bps']:
            result += f"  • 每股净资产: {financial_data['bps']:.2f} 元\n"

        # 估值指标
        if financial_data['pe_ratio'] or financial_data['pb_ratio']:
            result += f"\n📈 **估值指标**:\n"
            if financial_data['pe_ratio']:
                result += f"  • 市盈率(PE): {financial_data['pe_ratio']:.2f}\n"
            if financial_data['pb_ratio']:
                result += f"  • 市净率(PB): {financial_data['pb_ratio']:.2f}\n"

        result += f"\n💡 **数据来源**: 本地PostgreSQL数据库"

        return Response(message=result)

    async def _get_latest_data(self, stock_code: str, indicator: str) -> Response:
        """获取最新的特定指标数据"""

        financial_data = self.db_interface.get_latest_financial_data(stock_code)
        if not financial_data:
            return Response(message=f"❌ 未找到股票 {stock_code} 的财务数据")

        result = f"📊 **{financial_data['stock_name']} ({financial_data['stock_code']}) 最新数据**\n\n"
        result += f"📅 **报告期**: {financial_data['report_date']} ({financial_data['report_type']})\n\n"

        # 根据指标返回相应数据
        if indicator in ['revenue', '营业收入', '收入']:
            if financial_data['revenue']:
                result += f"💰 **营业收入**: {financial_data['revenue']:,.2f} 万元\n"
            else:
                result += f"❌ 暂无营业收入数据\n"

        elif indicator in ['net_profit', '净利润', '利润']:
            if financial_data['net_profit']:
                result += f"💵 **净利润**: {financial_data['net_profit']:,.2f} 万元\n"
            else:
                result += f"❌ 暂无净利润数据\n"

        elif indicator in ['roe', 'ROE', '净资产收益率']:
            if financial_data['roe']:
                result += f"📈 **ROE**: {financial_data['roe']:.2%}\n"
            else:
                result += f"❌ 暂无ROE数据\n"

        elif indicator in ['pe_ratio', 'PE', '市盈率']:
            if financial_data['pe_ratio']:
                result += f"📊 **市盈率**: {financial_data['pe_ratio']:.2f}\n"
            else:
                result += f"❌ 暂无市盈率数据\n"

        else:
            # 显示主要指标
            if financial_data['revenue']:
                result += f"💰 **营业收入**: {financial_data['revenue']:,.2f} 万元\n"
            if financial_data['net_profit']:
                result += f"💵 **净利润**: {financial_data['net_profit']:,.2f} 万元\n"
            if financial_data['roe']:
                result += f"📈 **ROE**: {financial_data['roe']:.2%}\n"

        result += f"\n💡 **数据来源**: 本地PostgreSQL数据库"

        return Response(message=result)

    async def _get_historical_trend(self, stock_code: str, indicator: str) -> Response:
        """获取历史趋势数据"""

        if not indicator:
            indicator = 'revenue'  # 默认查询营业收入趋势

        history_data = self.db_interface.get_indicator_history(stock_code, indicator, 8)
        if not history_data:
            return Response(message=f"❌ 未找到股票 {stock_code} 的{indicator}历史数据")

        # 获取股票名称
        stock_info = self.db_interface.get_stock_info(stock_code)
        stock_name = stock_info['stock_name'] if stock_info else stock_code

        # 指标中文名称
        indicator_names = {
            'revenue': '营业收入',
            'net_profit': '净利润',
            'roe': 'ROE',
            'pe_ratio': '市盈率',
            'pb_ratio': '市净率'
        }
        indicator_cn = indicator_names.get(indicator, indicator)

        result = f"📈 **{stock_name} ({stock_code}) {indicator_cn}历史趋势**\n\n"

        # 显示历史数据
        for i, data in enumerate(history_data):
            value = data['indicator_value']
            if value is not None:
                if indicator in ['revenue', 'net_profit']:
                    result += f"  {data['report_date']} ({data['report_type']}): {value:,.2f} 万元\n"
                elif indicator in ['roe']:
                    result += f"  {data['report_date']} ({data['report_type']}): {value:.2%}\n"
                else:
                    result += f"  {data['report_date']} ({data['report_type']}): {value:.2f}\n"

        # 计算趋势
        if len(history_data) >= 2:
            latest = history_data[0]['indicator_value']
            previous = history_data[1]['indicator_value']
            if latest and previous and previous != 0:
                change_rate = (latest - previous) / previous
                trend = "📈 上升" if change_rate > 0 else "📉 下降"
                result += f"\n📊 **最新趋势**: {trend} {abs(change_rate):.2%}\n"

        result += f"\n💡 **数据来源**: 本地PostgreSQL数据库"

        return Response(message=result)

    async def _get_industry_comparison(self, stock_code: str, indicator: str) -> Response:
        """获取行业对比数据"""

        if not indicator:
            indicator = 'roe'  # 默认对比ROE

        comparison_data = self.db_interface.get_industry_comparison(stock_code, indicator)
        if not comparison_data:
            return Response(message=f"❌ 未找到股票 {stock_code} 的行业对比数据")

        # 找到目标股票的数据
        target_stock = None
        for data in comparison_data:
            if data['stock_code'] == stock_code:
                target_stock = data
                break

        if not target_stock:
            return Response(message=f"❌ 未找到股票 {stock_code} 在行业中的排名")

        # 指标中文名称
        indicator_names = {
            'roe': 'ROE',
            'revenue': '营业收入',
            'net_profit': '净利润',
            'gross_profit_margin': '毛利率'
        }
        indicator_cn = indicator_names.get(indicator, indicator)

        result = f"🏆 **{target_stock['stock_name']} ({stock_code}) 行业{indicator_cn}对比**\n\n"

        # 目标股票排名
        result += f"📊 **行业排名**: 第 {target_stock['industry_rank']} 名\n"
        result += f"📈 **行业百分位**: {target_stock['industry_percentile']:.1f}%\n"

        if indicator in ['roe']:
            result += f"🎯 **{indicator_cn}值**: {target_stock['indicator_value']:.2%}\n"
        elif indicator in ['revenue', 'net_profit']:
            result += f"🎯 **{indicator_cn}**: {target_stock['indicator_value']:,.2f} 万元\n"
        else:
            result += f"🎯 **{indicator_cn}**: {target_stock['indicator_value']:.2f}\n"

        # 显示行业前5名
        result += f"\n🏅 **行业前5名**:\n"
        for i, data in enumerate(comparison_data[:5]):
            rank_emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i]
            if indicator in ['roe']:
                result += f"  {rank_emoji} {data['stock_name']} ({data['stock_code']}): {data['indicator_value']:.2%}\n"
            elif indicator in ['revenue', 'net_profit']:
                result += f"  {rank_emoji} {data['stock_name']} ({data['stock_code']}): {data['indicator_value']:,.0f} 万元\n"
            else:
                result += f"  {rank_emoji} {data['stock_name']} ({data['stock_code']}): {data['indicator_value']:.2f}\n"

        result += f"\n💡 **数据来源**: 本地PostgreSQL数据库"

        return Response(message=result)

    async def _get_financial_score(self, stock_code: str) -> Response:
        """获取财务评分"""

        score_data = self.db_interface.get_financial_score(stock_code)
        if not score_data:
            return Response(message=f"❌ 未找到股票 {stock_code} 的财务评分数据")

        result = f"⭐ **{score_data['stock_name']} ({stock_code}) 财务评分**\n\n"

        # 各项评分
        result += f"📊 **分项评分**:\n"
        result += f"  • 盈利能力: {score_data['profitability_score']}/100\n"
        result += f"  • 成长能力: {score_data['growth_score']}/100\n"
        result += f"  • 偿债能力: {score_data['solvency_score']}/100\n"
        result += f"  • 运营效率: {score_data['efficiency_score']}/100\n"

        # 综合评分
        overall_score = score_data['overall_score']
        result += f"\n🎯 **综合评分**: {overall_score}/100\n"

        # 评级
        if overall_score >= 80:
            grade = "A (优秀)"
            emoji = "🌟"
        elif overall_score >= 60:
            grade = "B (良好)"
            emoji = "⭐"
        elif overall_score >= 40:
            grade = "C (一般)"
            emoji = "📊"
        else:
            grade = "D (较差)"
            emoji = "⚠️"

        result += f"🏆 **财务评级**: {emoji} {grade}\n"

        result += f"\n💡 **数据来源**: 本地PostgreSQL数据库"

        return Response(message=result)
```

---

## 📋 **实施指南**

### **Step 1: 环境准备**

#### **1.1 PostgreSQL配置**
```bash
# 确认PostgreSQL服务运行
Get-Service postgresql-x64-17

# 配置postgresql.conf
listen_addresses = '*'
port = 5432

# 配置pg_hba.conf
host    all             all             **********/12           md5

# 重启服务
Restart-Service postgresql-x64-17
```

#### **1.2 创建数据库和用户**
```sql
-- 连接PostgreSQL
psql -U postgres

-- 创建数据库
CREATE DATABASE agent_zero;

-- 创建用户
CREATE USER agent_zero WITH PASSWORD 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE agent_zero TO agent_zero;
```

#### **1.3 WSL环境配置**
```bash
# 安装PostgreSQL客户端
sudo apt install postgresql-client

# 测试连接
psql -h ************ -p 5432 -U agent_zero -d agent_zero

# 安装Python依赖
pip install psycopg2-binary pandas
```

### **Step 2: 数据库初始化**

#### **2.1 执行初始化脚本**
```bash
# 在Agent-Zero项目根目录
cd /mnt/e/AI/agent-zero

# 执行数据库脚本
psql -h ************ -U agent_zero -d agent_zero -f 01_init_schema.sql
psql -h ************ -U agent_zero -d agent_zero -f 02_query_functions.sql
psql -h ************ -U agent_zero -d agent_zero -f 03_data_maintenance.sql
psql -h ************ -U agent_zero -d agent_zero -f 04_business_views.sql
```

#### **2.2 验证安装**
```sql
-- 检查表是否创建成功
\dt

-- 检查函数是否创建成功
\df get_*

-- 检查视图是否创建成功
\dv v_*

-- 测试函数
SELECT * FROM get_data_statistics();
```

### **Step 3: 环境变量配置**

#### **3.1 修改.env文件**
```bash
# 编辑Agent-Zero的.env文件
nano .env

# 添加数据库配置
DB_HOST=************
DB_PORT=5432
DB_NAME=agent_zero
DB_USER=agent_zero
DB_PASSWORD=your_secure_password
```

### **Step 4: 代码部署**

#### **4.1 创建数据库接口文件**
```bash
# 创建financial_db_interface.py
# 将上面的代码保存到 python/helpers/financial_db_interface.py
```

#### **4.2 修改金融数据工具**
```bash
# 备份原文件
cp python/tools/financial_data_tool.py python/tools/financial_data_tool.py.backup

# 替换为新的实现
# 将上面的代码保存到 python/tools/financial_data_tool.py
```

### **Step 5: 数据导入**

#### **5.1 准备数据文件**
Excel/CSV文件格式示例：
```
股票代码 | 股票名称 | 报告期     | 营业收入 | 净利润 | ROE  | 市盈率
000858  | 五粮液   | 2023-12-31 | 123456  | 23456 | 0.15 | 25.6
000001  | 平安银行 | 2023-12-31 | 234567  | 34567 | 0.12 | 18.9
```

#### **5.2 执行数据导入**
```python
# 创建导入脚本
python import_financial_data.py

# 或者直接使用
from financial_data_importer import FinancialDataImporter

db_config = {
    'host': '************',
    'port': 5432,
    'database': 'agent_zero',
    'user': 'agent_zero',
    'password': 'your_password'
}

importer = FinancialDataImporter(db_config)
result = importer.import_from_excel('财务数据.xlsx')
```

### **Step 6: 测试验证**

#### **6.1 连接测试**
```python
# 测试数据库连接
from python.helpers.financial_db_interface import FinancialDBInterface

db_interface = FinancialDBInterface()
if db_interface.test_connection():
    print("✅ 数据库连接成功")
else:
    print("❌ 数据库连接失败")
```

#### **6.2 功能测试**
```python
# 测试查询功能
stock_info = db_interface.get_stock_info('000858')
print(f"股票信息: {stock_info}")

financial_data = db_interface.get_latest_financial_data('000858')
print(f"财务数据: {financial_data}")
```

---

## 🎯 **使用示例**

### **用户查询示例**

#### **基本信息查询**
```
用户: "000858股票的基本信息"
系统: 返回五粮液的基本信息、最新财务数据、估值指标等
```

#### **特定指标查询**
```
用户: "000858的营业收入是多少"
系统: 返回最新的营业收入数据

用户: "五粮液的ROE指标"
系统: 返回最新的ROE数据
```

#### **历史趋势分析**
```
用户: "000858营业收入的历史趋势"
系统: 返回近8期的营业收入数据和趋势分析

用户: "五粮液ROE的历史变化"
系统: 返回ROE历史数据和变化趋势
```

#### **行业对比分析**
```
用户: "000858在行业中的ROE排名"
系统: 返回在同行业中的ROE排名和对比

用户: "五粮液的行业地位分析"
系统: 返回多个指标的行业对比
```

#### **财务评分**
```
用户: "000858的财务评分"
系统: 返回盈利能力、成长能力、偿债能力等综合评分
```

### **预期响应示例**

#### **基本信息查询响应**
```
📊 **五粮液 (000858) 财务概览**

🏢 **基本信息**:
  • 市场: SZ
  • 行业: 食品饮料
  • 上市日期: 1998-04-27

📅 **最新财务数据** (2023-12-31 年报):
  • 营业收入: 123,456.78 万元
  • 净利润: 23,456.78 万元
  • ROE: 15.23%
  • 每股收益: 6.78 元
  • 每股净资产: 44.56 元

📈 **估值指标**:
  • 市盈率(PE): 25.60
  • 市净率(PB): 3.45

💡 **数据来源**: 本地PostgreSQL数据库
```

#### **行业对比响应**
```
🏆 **五粮液 (000858) 行业ROE对比**

📊 **行业排名**: 第 3 名
📈 **行业百分位**: 85.2%
🎯 **ROE值**: 15.23%

🏅 **行业前5名**:
  🥇 贵州茅台 (600519): 18.45%
  🥈 剑南春 (000123): 16.78%
  🥉 五粮液 (000858): 15.23%
  4️⃣ 泸州老窖 (000568): 14.56%
  5️⃣ 水井坊 (600779): 13.89%

💡 **数据来源**: 本地PostgreSQL数据库
```

---

## 📊 **数据管理**

### **数据导入流程**
1. **准备数据**: 从数据源导出Excel/CSV文件
2. **数据清洗**: 标准化列名和数据格式
3. **批量导入**: 使用导入工具批量导入数据
4. **质量检查**: 运行数据质量检查函数
5. **验证测试**: 测试查询功能是否正常

### **数据维护**
```sql
-- 检查数据质量
SELECT * FROM check_data_quality();

-- 清理重复数据
SELECT clean_duplicate_data();

-- 更新数据质量评分
SELECT update_data_quality_scores();

-- 查看数据统计
SELECT * FROM get_data_statistics();
```

### **数据备份**
```bash
# 备份数据库
pg_dump -h ************ -U agent_zero agent_zero > backup_$(date +%Y%m%d).sql

# 恢复数据库
psql -h ************ -U agent_zero agent_zero < backup_20250708.sql
```

---

## ⚠️ **注意事项**

### **安全考虑**
- 使用强密码保护数据库
- 限制数据库访问IP范围
- 定期备份重要数据
- 监控数据库访问日志

### **性能优化**
- 合理使用索引
- 定期分析查询性能
- 监控数据库连接数
- 清理过期数据

### **故障排查**
1. **连接失败**: 检查IP地址、端口、防火墙
2. **查询慢**: 检查索引、优化SQL
3. **数据不准确**: 检查导入过程、数据质量
4. **内存不足**: 调整数据库配置、清理缓存

---

## ✅ **总结**

### **实施成果**
- ✅ **完整的数据库架构**: 表结构、索引、函数、视图
- ✅ **强大的查询功能**: 基本查询、历史趋势、行业对比、财务评分
- ✅ **便捷的数据导入**: 支持Excel/CSV批量导入
- ✅ **完善的数据维护**: 质量检查、清理、统计功能
- ✅ **用户友好的接口**: 自然语言查询，智能结果格式化

### **业务价值**
- 📊 **数据完整性**: 手动导出的数据更准确全面
- ⚡ **查询效率**: 本地数据库查询速度快
- 🔍 **深度分析**: 支持复杂的财务分析和对比
- 📈 **历史追踪**: 完整的历史数据和趋势分析
- 💰 **成本节约**: 减少外部API调用费用

### **扩展潜力**
- 🔄 **实时更新**: 可以添加定时更新机制
- 📊 **报表生成**: 可以生成各种财务报表
- 🤖 **智能分析**: 可以添加更多AI分析功能
- 🌐 **多数据源**: 可以集成更多数据源
- 📱 **移动支持**: 可以为移动端提供API接口

这套完整的方案为Agent-Zero的金融数据工具提供了强大的PostgreSQL支持，实现了从数据导入到智能查询的完整闭环！🚀
```
```
