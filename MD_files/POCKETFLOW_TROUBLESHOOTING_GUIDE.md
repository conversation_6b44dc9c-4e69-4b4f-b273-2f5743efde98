# PocketFlow 故障排除指南

## 🚨 常见问题及解决方案

### 1. 超时问题 ⚠️⚠️⚠️

**症状**：
- 工具执行后出现多次 "30 秒超时警告"
- 进程暂停，不再更新
- 日志显示：`Warning: Returning control to agent after 30 seconds with no output`

**根本原因**：
- 复杂的异步调用链导致阻塞
- LLM 调用方式不当
- PocketFlow 节点调用过于复杂

**解决方案**：

#### ✅ 正确的 LLM 调用方式
```python
# ❌ 错误：复杂的 chat_model 调用
chat_prompt = ChatPromptTemplate.from_messages([HumanMessage(content=prompt)])
response = await self.agent.call_chat_model(chat_prompt)

# ✅ 正确：使用 utility_model
response = await self.agent.call_utility_model(
    system="你是一个有用的AI助手，请根据用户的要求提供准确的回答。",
    message=prompt
)
```

#### ✅ 简化工作流设计
```python
# ❌ 错误：复杂的 PocketFlow 调用
flow = self._create_code_generator_flow()
await self._run_flow_async(flow, shared)

# ✅ 正确：直接的步骤调用
async def _run_simplified_workflow(self, shared):
    print("📋 步骤1: 处理输入")
    await self._process_input(shared)
    
    print("💻 步骤2: 核心处理")
    await self._process_core(shared)
    
    print("📊 步骤3: 生成结果")
    await self._generate_results(shared)
```

### 2. LLM 调用失败

**症状**：
- 返回 "LLM调用错误" 消息
- 工具执行中断

**解决方案**：
```python
class AgentZeroLLMAdapter:
    async def call_llm(self, prompt: str, system_prompt: str = "") -> str:
        try:
            # 统一使用 utility_model，更稳定
            if system_prompt:
                response = await self.agent.call_utility_model(
                    system=system_prompt,
                    message=prompt
                )
            else:
                response = await self.agent.call_utility_model(
                    system="你是一个有用的AI助手，请根据用户的要求提供准确的回答。",
                    message=prompt
                )
            return response
        except Exception as e:
            print(f"❌ LLM调用错误: {str(e)}")
            return f"LLM调用错误: {str(e)}"
```

### 3. 代码执行问题

**症状**：
- 代码执行工具调用失败
- 返回 "代码执行失败" 消息

**解决方案**：
```python
async def execute_python(self, code: str, inputs: Dict[str, Any]) -> tuple[Any, str]:
    try:
        full_code = self._build_code_with_inputs(code, inputs)
        print(f"🔧 执行代码: {full_code[:100]}...")
        
        from python.tools.code_execution_tool import CodeExecution
        
        code_tool = CodeExecution(
            agent=self.agent,
            name="code_execution_tool",
            method="",
            args={"runtime": "python", "code": full_code, "session": 0},
            message=""
        )
        
        result = await code_tool.execute()  # 注意：不传递额外参数
        
        if result and hasattr(result, 'message'):
            output = self._parse_execution_result(result.message)
            print(f"✅ 代码执行成功: {output}")
            return output, None
        else:
            return None, "代码执行失败"
            
    except Exception as e:
        print(f"❌ 代码执行异常: {str(e)}")
        return None, str(e)
```

### 4. YAML 解析错误

**症状**：
- "YAML 解析错误" 消息
- 无法提取 LLM 响应中的结构化数据

**解决方案**：
```python
def extract_yaml_from_response(response: str) -> str:
    """从 LLM 响应中提取 YAML 内容"""
    # 查找 ```yaml 和 ``` 之间的内容
    start_marker = "```yaml"
    end_marker = "```"
    
    start_idx = response.find(start_marker)
    if start_idx == -1:
        # 尝试其他格式
        start_marker = "```"
        start_idx = response.find(start_marker)
        if start_idx == -1:
            raise ValueError("响应中未找到代码块")
    
    start_idx += len(start_marker)
    end_idx = response.find(end_marker, start_idx)
    
    if end_idx == -1:
        raise ValueError("代码块未正确关闭")
    
    return response[start_idx:end_idx].strip()
```

## 🔍 调试步骤

### 1. 启用详细日志
```python
async def execute(self, **kwargs):
    try:
        print(f"🚀 开始执行工具: {self.name}")
        print(f"📝 输入参数: {kwargs}")
        
        # 你的工具逻辑
        result = await self._process(kwargs)
        
        print(f"✅ 工具执行完成")
        return result
        
    except Exception as e:
        print(f"❌ 工具执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
```

### 2. 分步测试
```python
# 创建独立测试脚本
async def test_tool_step_by_step():
    # 1. 测试 LLM 调用
    llm_adapter = AgentZeroLLMAdapter(agent)
    response = await llm_adapter.call_llm("测试提示")
    print(f"LLM 响应: {response}")
    
    # 2. 测试 YAML 解析
    yaml_str = YAMLParser.extract_yaml_from_response(response)
    result = YAMLParser.safe_load(yaml_str)
    print(f"解析结果: {result}")
    
    # 3. 测试完整工具
    tool_result = await tool.execute(test_input="测试数据")
    print(f"工具结果: {tool_result}")
```

### 3. 检查环境配置
```bash
# 检查 conda 环境
conda info --envs

# 检查 Python 包
pip list | grep pocketflow
pip list | grep pyyaml

# 检查 API 密钥
echo $API_KEY_OPENAI
echo $OPENAI_API_KEY
```

## 📋 预防措施

### 1. 工具设计原则
- **简单优于复杂**：避免过度设计的异步调用链
- **统一接口**：使用 Agent Zero 的标准 LLM 调用方式
- **渐进开发**：先实现基本功能，再添加复杂特性
- **充分测试**：每个组件都要独立测试

### 2. 代码模板使用
```python
# 使用经过验证的模板
cp templates/pocketflow_tool_template.py python/tools/my_tool.py

# 或使用自动化脚本
./scripts/create_new_tool.sh my_tool "我的工具描述"
```

### 3. 错误处理
```python
async def robust_llm_call(self, prompt: str) -> str:
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = await self.agent.call_utility_model(
                system="你是一个有用的AI助手",
                message=prompt
            )
            return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            print(f"⚠️ 重试 {attempt + 1}/{max_retries}: {e}")
            await asyncio.sleep(1)
```

## 🆘 紧急修复

如果工具完全无法工作，可以使用这个最小化版本：

```python
from python.helpers.tool import Tool, Response

class MinimalTool(Tool):
    async def execute(self, input_data="", **kwargs):
        try:
            # 最简单的 LLM 调用
            response = await self.agent.call_utility_model(
                system="你是一个有用的AI助手",
                message=f"请处理以下数据: {input_data}"
            )
            
            return Response(
                message=f"处理结果: {response}",
                break_loop=False
            )
        except Exception as e:
            return Response(
                message=f"处理失败: {str(e)}",
                break_loop=False
            )
```

## 📞 获取帮助

1. **检查日志**：查看 `logs/` 目录下的最新日志文件
2. **运行测试**：使用 `test_code_generator_fix.py` 验证修复
3. **参考文档**：查看 `POCKETFLOW_EXTENSION_GUIDE.md` 获取详细指导
4. **使用模板**：从 `templates/` 目录复制经过验证的代码模板

记住：**简单的解决方案往往是最好的解决方案**！
