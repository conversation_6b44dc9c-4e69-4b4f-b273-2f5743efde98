# 新增模型提供方使用指南

## 🆕 新增的模型提供方

Agent-Zero 项目新增了两个模型提供方：

### 1. SiliconFlow (硅基流动)
- **官网**: https://siliconflow.cn/
- **特点**: 国内AI服务平台，支持多种开源模型
- **优势**: 国内访问速度快，支持多种主流开源模型

### 2. VolcEngine (火山引擎)
- **官网**: https://www.volcengine.com/
- **特点**: 字节跳动旗下AI服务平台
- **优势**: 企业级解决方案，稳定可靠

## 🔧 配置方法

### 1. 环境变量配置

在您的 `.env` 文件中添加以下配置：

```bash
# SiliconFlow 配置
API_KEY_SILICONFLOW=your_siliconflow_api_key_here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# VolcEngine 配置
API_KEY_VOLCENGINE=your_volcengine_api_key_here
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
```

### 2. API密钥获取

#### SiliconFlow
1. 访问 https://siliconflow.cn/
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 将密钥添加到环境变量 `API_KEY_SILICONFLOW`

#### VolcEngine
1. 访问 https://www.volcengine.com/
2. 注册账号并登录
3. 开通AI服务并创建API密钥
4. 将密钥添加到环境变量 `API_KEY_VOLCENGINE`

## 💻 使用示例

### 在Python代码中使用

```python
from models import get_siliconflow_chat, get_volcengine_chat

# 使用SiliconFlow
siliconflow_model = get_siliconflow_chat(
    model_name="Qwen/Qwen2.5-7B-Instruct",
    temperature=0.7
)

# 使用VolcEngine
volcengine_model = get_volcengine_chat(
    model_name="ep-20241230140000-xxxxx",  # 使用您的端点ID
    temperature=0.7
)
```

### 在Agent配置中使用

在Agent配置文件中，您可以这样配置：

```python
# 使用SiliconFlow模型
agent_config = {
    "chat_model": {
        "provider": "siliconflow",
        "name": "Qwen/Qwen2.5-7B-Instruct",
        "ctx_length": 4096,
        "temperature": 0.7
    }
}

# 使用VolcEngine模型
agent_config = {
    "chat_model": {
        "provider": "volcengine", 
        "name": "ep-20241230140000-xxxxx",
        "ctx_length": 4096,
        "temperature": 0.7
    }
}
```

## 🎯 支持的功能

### SiliconFlow
- ✅ **Chat模型**: 支持对话生成
- ✅ **Embedding模型**: 支持文本嵌入
- ✅ **流式输出**: 支持实时响应
- ✅ **多种模型**: 支持Qwen、ChatGLM等开源模型

### VolcEngine
- ✅ **Chat模型**: 支持对话生成
- ✅ **Embedding模型**: 支持文本嵌入
- ✅ **流式输出**: 支持实时响应
- ✅ **企业级**: 提供企业级稳定性和安全性

## 🔍 常见模型

### SiliconFlow 推荐模型
```
# 对话模型
- Qwen/Qwen2.5-7B-Instruct
- Qwen/Qwen2.5-14B-Instruct
- THUDM/chatglm3-6b
- 01-ai/Yi-1.5-9B-Chat

# Embedding模型
- BAAI/bge-large-zh-v1.5
- BAAI/bge-base-zh-v1.5
```

### VolcEngine 模型配置
```
# 使用端点ID作为模型名称
- ep-20241230140000-xxxxx  # 您的具体端点ID
```

## 🚀 优势对比

| 特性 | SiliconFlow | VolcEngine | OpenAI |
|------|-------------|------------|--------|
| **国内访问** | ✅ 快速 | ✅ 快速 | ❌ 需要代理 |
| **开源模型** | ✅ 丰富 | ✅ 支持 | ❌ 仅自有模型 |
| **企业级** | ✅ 支持 | ✅ 专业 | ✅ 成熟 |
| **价格** | 💰 经济 | 💰 合理 | 💰💰 较高 |
| **中文支持** | ✅ 优秀 | ✅ 优秀 | ✅ 良好 |

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: 401 Unauthorized
   解决: 检查API_KEY_SILICONFLOW或API_KEY_VOLCENGINE是否正确设置
   ```

2. **网络连接问题**
   ```
   错误: Connection timeout
   解决: 检查网络连接，确认BASE_URL是否正确
   ```

3. **模型不存在**
   ```
   错误: Model not found
   解决: 确认模型名称是否正确，检查服务商支持的模型列表
   ```

### 调试方法

```python
# 测试连接
from models import get_siliconflow_chat

try:
    model = get_siliconflow_chat("Qwen/Qwen2.5-7B-Instruct")
    response = model.invoke("Hello, world!")
    print("连接成功:", response.content)
except Exception as e:
    print("连接失败:", str(e))
```

## 📝 注意事项

1. **API配额**: 注意各服务商的API调用配额限制
2. **模型名称**: 不同服务商的模型名称格式可能不同
3. **费用**: 使用前请了解各服务商的计费方式
4. **合规性**: 确保使用符合相关法律法规要求

---

**更新日期**: 2025-01-02
**版本**: v1.0.0
