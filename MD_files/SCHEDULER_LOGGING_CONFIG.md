# 调度器日志配置说明

## 问题描述

用户反馈调度器日志每60秒显示一次状态信息，希望能够关闭或减少这些显示，只在必要时记录日志。

```
🔄 Scheduler tick at 2025-06-26 02:46:30
📊 Scheduler status: 0 total tasks, 0 due tasks
```

## 解决方案

### 新增配置选项

在 `python/helpers/job_loop.py` 中添加了以下配置选项：

```python
# 调度器日志配置
SCHEDULER_VERBOSE_LOGGING = False  # 设置为True显示详细日志，False只显示重要事件
SCHEDULER_LOG_INTERVAL = 300  # 无任务时的日志间隔（秒），0表示不显示
```

### 日志显示逻辑

#### 1. 详细模式 (`SCHEDULER_VERBOSE_LOGGING = True`)
- **显示频率**：每次tick都显示（60秒间隔）
- **显示内容**：
  - 时间戳：`🔄 Scheduler tick at 2025-06-26 02:46:30`
  - 状态信息：`📊 Scheduler status: X total tasks, Y due tasks`
  - 任务详情：如果有到期任务

#### 2. 静默模式 (`SCHEDULER_VERBOSE_LOGGING = False`) - **默认设置**
- **有任务时**：总是显示状态和任务信息
- **无任务时**：
  - 如果 `SCHEDULER_LOG_INTERVAL = 0`：完全不显示
  - 如果 `SCHEDULER_LOG_INTERVAL > 0`：按间隔显示状态

### 配置选项详解

#### `SCHEDULER_VERBOSE_LOGGING`
- **类型**：布尔值
- **默认值**：`False`
- **作用**：
  - `True`：显示所有调度器活动（包括时间戳）
  - `False`：只在有重要事件时显示

#### `SCHEDULER_LOG_INTERVAL`
- **类型**：整数（秒）
- **默认值**：`300`（5分钟）
- **作用**：
  - `0`：无任务时完全不显示状态
  - `> 0`：无任务时按此间隔显示状态

## 使用场景配置

### 场景1：完全静默（推荐用于生产环境）
```python
SCHEDULER_VERBOSE_LOGGING = False
SCHEDULER_LOG_INTERVAL = 0
```
**效果**：只在有任务执行时显示日志

### 场景2：定期状态报告（推荐用于开发环境）
```python
SCHEDULER_VERBOSE_LOGGING = False
SCHEDULER_LOG_INTERVAL = 300  # 5分钟
```
**效果**：有任务时立即显示，无任务时每5分钟显示一次状态

### 场景3：详细调试模式
```python
SCHEDULER_VERBOSE_LOGGING = True
SCHEDULER_LOG_INTERVAL = 60  # 此时此参数被忽略
```
**效果**：每60秒显示完整的调度器信息

## 修改配置方法

### 方法1：直接修改代码文件
编辑 `python/helpers/job_loop.py` 文件：

```python
# 找到这些行并修改值
SCHEDULER_VERBOSE_LOGGING = False  # 改为True启用详细日志
SCHEDULER_LOG_INTERVAL = 0         # 改为0完全关闭状态日志
```

### 方法2：通过环境变量（未来可扩展）
可以考虑将这些配置移到 `.env` 文件中：

```env
SCHEDULER_VERBOSE_LOGGING=false
SCHEDULER_LOG_INTERVAL=0
```

## 日志输出示例

### 完全静默模式 (`SCHEDULER_LOG_INTERVAL = 0`)
```
# 启动时
🔄 Job Loop starting...
   Verbose logging: False
   Status log interval: 0s (0=disabled)

# 运行时：无任务时不显示任何内容
# 有任务时：
   ⏰ Due task: Test_Task_123 (UUID: abc12345...)
✅ Scheduler tick completed, processed 1 due tasks
```

### 定期状态模式 (`SCHEDULER_LOG_INTERVAL = 300`)
```
# 启动时
🔄 Job Loop starting...
   Verbose logging: False
   Status log interval: 300s (0=disabled)

# 每5分钟显示一次（无任务时）
📊 Scheduler status: 0 total tasks, 0 due tasks

# 有任务时立即显示
📊 Scheduler status: 1 total tasks, 1 due tasks
   ⏰ Due task: Test_Task_123 (UUID: abc12345...)
✅ Scheduler tick completed, processed 1 due tasks
```

### 详细模式 (`SCHEDULER_VERBOSE_LOGGING = True`)
```
# 每60秒显示
🔄 Scheduler tick at 2025-06-26 02:46:30
📊 Scheduler status: 0 total tasks, 0 due tasks

🔄 Scheduler tick at 2025-06-26 02:47:30
📊 Scheduler status: 1 total tasks, 1 due tasks
   ⏰ Due task: Test_Task_123 (UUID: abc12345...)
✅ Scheduler tick completed, processed 1 due tasks
```

## 推荐配置

### 用户日常使用
```python
SCHEDULER_VERBOSE_LOGGING = False
SCHEDULER_LOG_INTERVAL = 0
```
**理由**：减少控制台噪音，只在有实际任务活动时显示信息

### 开发调试
```python
SCHEDULER_VERBOSE_LOGGING = False
SCHEDULER_LOG_INTERVAL = 300
```
**理由**：保持对调度器状态的可见性，但不过于频繁

### 问题诊断
```python
SCHEDULER_VERBOSE_LOGGING = True
SCHEDULER_LOG_INTERVAL = 60
```
**理由**：获得最详细的调度器活动信息

## 应用配置

修改配置后，需要重启服务：

```bash
# 停止当前服务
./quick_start.sh --stop

# 重新启动
./quick_start.sh
```

---

**配置修改时间**：2025-06-26 10:50 UTC  
**默认配置**：静默模式（`SCHEDULER_VERBOSE_LOGGING = False`, `SCHEDULER_LOG_INTERVAL = 300`）  
**推荐用户配置**：完全静默（`SCHEDULER_LOG_INTERVAL = 0`）
