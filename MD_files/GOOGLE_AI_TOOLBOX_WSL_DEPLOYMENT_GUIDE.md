# Google AI Toolbox WSL部署指南

## 🎯 项目概述

Google AI Toolbox (MCP Toolbox for Databases) 是一个专门为数据库操作设计的MCP服务器，完全适合在WSL环境中部署。

### ✅ **WSL兼容性分析**
- **Go语言项目**：WSL完全支持Go开发环境
- **数据库连接**：支持连接Windows/WSL中的数据库
- **MCP协议**：与Agent-Zero完美集成
- **轻量级部署**：单一二进制文件，易于管理

## 📋 部署步骤

### 步骤1: 准备WSL环境

**检查Go环境**：
```bash
# 在WSL中检查Go版本
go version

# 如果没有安装Go，安装最新版本
wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz
sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

### 步骤2: 下载和安装Toolbox

**方法1: 下载预编译二进制文件**
```bash
# 创建工作目录
mkdir -p ~/google-ai-toolbox
cd ~/google-ai-toolbox

# 下载最新版本 (v0.9.0)
export VERSION=0.9.0
curl -O https://storage.googleapis.com/genai-toolbox/v$VERSION/linux/amd64/toolbox
chmod +x toolbox

# 验证安装
./toolbox --version
```

**方法2: 从源码编译**
```bash
# 克隆项目
git clone https://github.com/googleapis/genai-toolbox.git
cd genai-toolbox

# 编译
go build -o toolbox .

# 验证编译
./toolbox --version
```

### 步骤3: 配置数据库连接

**创建配置文件 `tools.yaml`**：
```yaml
# PostgreSQL数据库配置示例
sources:
  postgres-main:
    kind: postgres
    host: ************  # Windows主机IP
    port: 5432
    database: your_database
    user: your_username
    password: your_password
    
  # 如果有WSL本地数据库
  postgres-local:
    kind: postgres
    host: localhost
    port: 5432
    database: local_db
    user: local_user
    password: local_password

# 定义工具
tools:
  list-tables:
    kind: postgres-sql
    source: postgres-main
    description: List all tables in the database
    statement: |
      SELECT table_name, table_schema 
      FROM information_schema.tables 
      WHERE table_type = 'BASE TABLE'
      ORDER BY table_schema, table_name;
      
  describe-table:
    kind: postgres-sql
    source: postgres-main
    description: Describe table structure
    parameters:
      - name: table_name
        type: string
        description: Name of the table to describe
    statement: |
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = $1
      ORDER BY ordinal_position;
      
  execute-query:
    kind: postgres-sql
    source: postgres-main
    description: Execute custom SQL query
    parameters:
      - name: query
        type: string
        description: SQL query to execute
    statement: $1

# 定义工具集
toolsets:
  database-admin:
    - list-tables
    - describe-table
  database-query:
    - execute-query
  all-tools:
    - list-tables
    - describe-table
    - execute-query
```

### 步骤4: 启动服务

**创建启动脚本 `start-toolbox.sh`**：
```bash
#!/bin/bash

# Google AI Toolbox 启动脚本
echo "🚀 启动 Google AI Toolbox..."

# 检查配置文件
if [ ! -f "tools.yaml" ]; then
    echo "❌ 配置文件 tools.yaml 不存在"
    exit 1
fi

# 启动服务
echo "📡 启动MCP服务器在端口5000..."
./toolbox --tools-file "tools.yaml" --port 5000 --host 0.0.0.0

echo "✅ Google AI Toolbox 已启动"
```

**使脚本可执行并启动**：
```bash
chmod +x start-toolbox.sh
./start-toolbox.sh
```

### 步骤5: 验证部署

**测试服务状态**：
```bash
# 检查服务是否运行
curl http://localhost:5000/health

# 检查可用工具
curl http://localhost:5000/tools

# 检查工具集
curl http://localhost:5000/toolsets
```

## 🔧 与Agent-Zero集成

### 配置Agent-Zero MCP客户端

**在Agent-Zero的settings.json中添加**：
```json
{
    "name": "google-ai-toolbox",
    "description": "Google AI Toolbox for Database Operations",
    "url": "http://localhost:5000/mcp",
    "timeout": 30,
    "sse_read_timeout": 300
}
```

### 使用Python SDK集成

**安装SDK**：
```bash
# 在Agent-Zero的conda环境中
conda activate AZ090
pip install toolbox-core
```

**集成代码示例**：
```python
from toolbox_core import ToolboxClient

async def use_toolbox():
    async with ToolboxClient("http://localhost:5000") as client:
        # 加载所有工具
        tools = await client.load_toolset("all-tools")
        
        # 或加载特定工具集
        admin_tools = await client.load_toolset("database-admin")
        
        return tools
```

## 🔍 高级配置

### 多数据库支持

```yaml
sources:
  postgres-prod:
    kind: postgres
    host: prod-server.example.com
    port: 5432
    database: production_db
    user: prod_user
    password: ${PROD_DB_PASSWORD}  # 环境变量
    
  mysql-analytics:
    kind: mysql
    host: analytics-server.example.com
    port: 3306
    database: analytics_db
    user: analytics_user
    password: ${MYSQL_PASSWORD}
```

### 安全配置

```yaml
# 添加认证和SSL
sources:
  secure-postgres:
    kind: postgres
    host: secure-db.example.com
    port: 5432
    database: secure_db
    user: secure_user
    password: ${DB_PASSWORD}
    sslmode: require
    sslcert: /path/to/client-cert.pem
    sslkey: /path/to/client-key.pem
    sslrootcert: /path/to/ca-cert.pem
```

## 🚀 生产部署优化

### 使用Docker部署

**创建Dockerfile**：
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o toolbox .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/toolbox .
COPY tools.yaml .
EXPOSE 5000
CMD ["./toolbox", "--tools-file", "tools.yaml", "--port", "5000", "--host", "0.0.0.0"]
```

### 使用systemd服务

**创建服务文件 `/etc/systemd/system/google-ai-toolbox.service`**：
```ini
[Unit]
Description=Google AI Toolbox MCP Server
After=network.target

[Service]
Type=simple
User=toolbox
WorkingDirectory=/home/<USER>/google-ai-toolbox
ExecStart=/home/<USER>/google-ai-toolbox/toolbox --tools-file tools.yaml --port 5000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 📊 监控和日志

### 启用详细日志

```bash
# 启动时启用调试日志
./toolbox --tools-file "tools.yaml" --log-level debug
```

### 性能监控

```bash
# 监控资源使用
htop

# 监控网络连接
ss -tlnp | grep :5000

# 监控日志
tail -f /var/log/google-ai-toolbox.log
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库凭据
   - 确认防火墙设置

2. **端口冲突**
   - 更改端口：`--port 5001`
   - 检查端口占用：`netstat -tlnp | grep :5000`

3. **权限问题**
   - 确保二进制文件可执行
   - 检查配置文件权限

### 调试命令

```bash
# 验证配置文件
./toolbox --tools-file "tools.yaml" --validate-only

# 测试数据库连接
./toolbox --tools-file "tools.yaml" --test-connections

# 详细错误信息
./toolbox --tools-file "tools.yaml" --log-level debug
```

## ✅ 部署检查清单

- [ ] WSL环境准备完成
- [ ] Go环境安装配置
- [ ] Toolbox二进制文件下载/编译
- [ ] 数据库连接配置
- [ ] tools.yaml配置文件创建
- [ ] 服务启动成功
- [ ] 健康检查通过
- [ ] Agent-Zero集成配置
- [ ] 工具功能测试

## 🎯 预期结果

部署成功后，您将拥有：
- ✅ 运行在WSL中的Google AI Toolbox服务
- ✅ 与Agent-Zero的MCP集成
- ✅ 强大的数据库操作工具集
- ✅ 可扩展的工具配置系统
