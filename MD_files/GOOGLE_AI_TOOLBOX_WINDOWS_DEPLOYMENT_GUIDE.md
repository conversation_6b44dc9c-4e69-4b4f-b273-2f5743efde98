# Google AI Toolbox Windows部署指南

## 🎯 部署架构

```
Windows 环境:
├── Google AI Toolbox (端口5000)
├── PostgreSQL 数据库 (端口5432)
└── 防火墙配置

WSL 环境:
├── Agent-Zero (端口50001)
└── MCP客户端连接到Windows Toolbox
```

## 📋 Windows部署步骤

### 步骤1: 下载和安装Toolbox

**下载预编译版本**：
```powershell
# 创建工作目录
New-Item -ItemType Directory -Path "C:\google-ai-toolbox" -Force
Set-Location "C:\google-ai-toolbox"

# 下载最新版本 (v0.9.0)
$VERSION = "0.9.0"
$URL = "https://storage.googleapis.com/genai-toolbox/v$VERSION/windows/amd64/toolbox.exe"
Invoke-WebRequest -Uri $URL -OutFile "toolbox.exe"

# 验证下载
.\toolbox.exe --version
```

### 步骤2: 创建配置文件

**创建 `tools.yaml`**：
```yaml
# PostgreSQL数据库配置
sources:
  postgres-main:
    kind: postgres
    host: localhost  # Windows本地PostgreSQL
    port: 5432
    database: your_database
    user: your_username
    password: your_password
    
  # 如果有远程数据库
  postgres-remote:
    kind: postgres
    host: remote-server.example.com
    port: 5432
    database: remote_db
    user: remote_user
    password: ${REMOTE_DB_PASSWORD}  # 环境变量

# 数据库操作工具
tools:
  list-schemas:
    kind: postgres-sql
    source: postgres-main
    description: List all database schemas
    statement: |
      SELECT schema_name, schema_owner
      FROM information_schema.schemata
      WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY schema_name;
      
  list-tables:
    kind: postgres-sql
    source: postgres-main
    description: List all tables in a schema
    parameters:
      - name: schema_name
        type: string
        description: Schema name (default 'public')
        default: public
    statement: |
      SELECT table_name, table_type, table_schema
      FROM information_schema.tables 
      WHERE table_schema = COALESCE(NULLIF($1, ''), 'public')
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
      
  describe-table:
    kind: postgres-sql
    source: postgres-main
    description: Get detailed table structure
    parameters:
      - name: table_name
        type: string
        description: Name of the table to describe
      - name: schema_name
        type: string
        description: Schema name (default 'public')
        default: public
    statement: |
      SELECT 
        column_name,
        data_type,
        character_maximum_length,
        is_nullable,
        column_default,
        ordinal_position
      FROM information_schema.columns 
      WHERE table_name = $1 
        AND table_schema = COALESCE(NULLIF($2, ''), 'public')
      ORDER BY ordinal_position;
      
  execute-query:
    kind: postgres-sql
    source: postgres-main
    description: Execute custom SQL query (SELECT only for safety)
    parameters:
      - name: query
        type: string
        description: SQL SELECT query to execute
    statement: $1
    
  table-row-count:
    kind: postgres-sql
    source: postgres-main
    description: Get row count for a table
    parameters:
      - name: table_name
        type: string
        description: Name of the table
      - name: schema_name
        type: string
        description: Schema name (default 'public')
        default: public
    statement: |
      SELECT COUNT(*) as row_count 
      FROM "{schema_name}"."{table_name}";

# 工具集定义
toolsets:
  database-explorer:
    - list-schemas
    - list-tables
    - describe-table
    - table-row-count
    
  database-admin:
    - list-schemas
    - list-tables
    - describe-table
    - execute-query
    
  all-tools:
    - list-schemas
    - list-tables
    - describe-table
    - execute-query
    - table-row-count
```

### 步骤3: 创建启动脚本

**创建 `start-toolbox.bat`**：
```batch
@echo off
echo 🚀 启动 Google AI Toolbox...

REM 检查配置文件
if not exist "tools.yaml" (
    echo ❌ 配置文件 tools.yaml 不存在
    pause
    exit /b 1
)

REM 设置环境变量（如果需要）
REM set REMOTE_DB_PASSWORD=your_remote_password

echo 📡 启动MCP服务器在端口5000...
echo 🌐 服务将绑定到 0.0.0.0:5000 以允许WSL访问
echo.

REM 启动服务，绑定到所有接口以允许WSL访问
toolbox.exe --tools-file "tools.yaml" --port 5000 --host 0.0.0.0

echo.
echo ✅ Google AI Toolbox 已停止
pause
```

**创建 `start-toolbox.ps1` (PowerShell版本)**：
```powershell
Write-Host "🚀 启动 Google AI Toolbox..." -ForegroundColor Green

# 检查配置文件
if (-not (Test-Path "tools.yaml")) {
    Write-Host "❌ 配置文件 tools.yaml 不存在" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 设置环境变量（如果需要）
# $env:REMOTE_DB_PASSWORD = "your_remote_password"

Write-Host "📡 启动MCP服务器在端口5000..." -ForegroundColor Yellow
Write-Host "🌐 服务将绑定到 0.0.0.0:5000 以允许WSL访问" -ForegroundColor Cyan
Write-Host ""

try {
    # 启动服务
    .\toolbox.exe --tools-file "tools.yaml" --port 5000 --host 0.0.0.0
}
catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
    Write-Host ""
    Write-Host "✅ Google AI Toolbox 已停止" -ForegroundColor Green
    Read-Host "按任意键退出"
}
```

### 步骤4: 配置Windows防火墙

**方法1: 通过Windows设置**
1. 打开"Windows安全中心"
2. 选择"防火墙和网络保护"
3. 点击"允许应用通过防火墙"
4. 添加端口5000的入站规则

**方法2: 通过PowerShell (管理员权限)**
```powershell
# 添加防火墙规则
New-NetFirewallRule -DisplayName "Google AI Toolbox" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow

# 验证规则
Get-NetFirewallRule -DisplayName "Google AI Toolbox"
```

**方法3: 通过命令行 (管理员权限)**
```cmd
netsh advfirewall firewall add rule name="Google AI Toolbox" dir=in action=allow protocol=TCP localport=5000
```

### 步骤5: 启动和验证

**启动服务**：
```powershell
# 进入工作目录
Set-Location "C:\google-ai-toolbox"

# 启动服务
.\start-toolbox.bat
# 或
.\start-toolbox.ps1
```

**验证服务**：
```powershell
# 检查服务状态
Invoke-RestMethod -Uri "http://localhost:5000/health"

# 检查可用工具
Invoke-RestMethod -Uri "http://localhost:5000/tools"

# 检查工具集
Invoke-RestMethod -Uri "http://localhost:5000/toolsets"
```

## 🔧 WSL中的Agent-Zero配置

### 获取Windows主机IP

**在WSL中执行**：
```bash
# 获取Windows主机IP
cat /etc/resolv.conf | grep nameserver | awk '{print $2}'
# 或
ip route show default | awk '{print $3}'
```

### 配置Agent-Zero MCP客户端

**更新 `tmp/settings.json`**：
```json
{
    "name": "google-ai-toolbox",
    "description": "Google AI Toolbox for Database Operations",
    "url": "http://************:5000/mcp",
    "timeout": 30,
    "sse_read_timeout": 300
}
```

### 测试连接

**在WSL中测试**：
```bash
# 测试基本连接
curl http://************:5000/health

# 测试工具列表
curl http://************:5000/tools

# 测试MCP端点
curl -H "Accept: text/event-stream" http://************:5000/mcp
```

## 🚀 高级配置

### 环境变量配置

**创建 `.env` 文件**：
```env
# 数据库连接
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database
DB_USER=your_username
DB_PASSWORD=your_password

# 远程数据库
REMOTE_DB_PASSWORD=remote_password

# 服务配置
TOOLBOX_PORT=5000
TOOLBOX_HOST=0.0.0.0
LOG_LEVEL=info
```

**修改启动脚本使用环境变量**：
```powershell
# 加载环境变量
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^=]+)=(.*)$") {
        [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
    }
}

# 启动服务
.\toolbox.exe --tools-file "tools.yaml" --port $env:TOOLBOX_PORT --host $env:TOOLBOX_HOST
```

### 多数据库配置

```yaml
sources:
  postgres-local:
    kind: postgres
    host: localhost
    port: 5432
    database: local_db
    user: ${DB_USER}
    password: ${DB_PASSWORD}
    
  mysql-analytics:
    kind: mysql
    host: localhost
    port: 3306
    database: analytics
    user: mysql_user
    password: ${MYSQL_PASSWORD}
    
  sqlite-cache:
    kind: sqlite
    path: ./cache.db
```

## 🔍 故障排除

### 常见问题

1. **WSL无法连接到Windows服务**
   - 检查防火墙设置
   - 确认服务绑定到0.0.0.0而不是127.0.0.1
   - 验证Windows主机IP地址

2. **数据库连接失败**
   - 检查PostgreSQL是否允许本地连接
   - 验证pg_hba.conf配置
   - 确认数据库服务正在运行

3. **端口冲突**
   - 更改端口：`--port 5001`
   - 检查端口占用：`netstat -an | findstr :5000`

### 调试命令

**Windows中**：
```powershell
# 检查端口占用
netstat -an | findstr :5000

# 测试本地连接
Test-NetConnection -ComputerName localhost -Port 5000

# 查看防火墙规则
Get-NetFirewallRule -DisplayName "*toolbox*"
```

**WSL中**：
```bash
# 测试连接
telnet ************ 5000

# 检查路由
ip route show

# 测试DNS解析
nslookup ************
```

## ✅ 部署检查清单

- [ ] Windows环境准备
- [ ] Toolbox下载和配置
- [ ] 数据库连接配置
- [ ] 防火墙规则添加
- [ ] 服务启动成功
- [ ] WSL连接测试
- [ ] Agent-Zero MCP配置
- [ ] 端到端功能验证

## 🎯 预期结果

部署成功后：
- ✅ Windows中运行Google AI Toolbox
- ✅ WSL中的Agent-Zero可以访问数据库工具
- ✅ 跨环境MCP通信正常
- ✅ 强大的数据库操作能力
