# 启动脚本数据源检查功能说明

## 📋 **功能概述**

**功能名称**: 启动时金融数据源健康检查  
**实现时间**: 2025-07-08  
**适用脚本**: quick_start.sh  
**检查模块**: check_financial_datasource.py  

---

## 🎯 **功能目标**

### **主要目标**
- 在系统启动时自动检查金融数据源状态
- 提供清晰的警示信息，但不中断启动流程
- 帮助用户及时发现和解决数据源问题
- 提供详细的故障排除指导

### **设计原则**
- **非阻塞**: 数据源问题不影响系统启动
- **信息丰富**: 提供详细的检查结果和解决建议
- **用户友好**: 清晰的状态提示和错误说明
- **可选详细**: 支持简化和详细两种检查模式

---

## 🔧 **实现方案**

### **1. 检查模块 (check_financial_datasource.py)**

#### **核心功能**
```python
async def check_financial_datasource(verbose=True):
    """检查金融数据源状态"""
    
    # 1. 检查Token配置
    refresh_token = os.getenv('IFIND_REFRESH_TOKEN')
    if not refresh_token:
        return False
    
    # 2. 初始化API客户端
    client = FinancialAPIClient()
    
    # 3. 测试Token获取
    current_token = client._get_access_token()
    
    # 4. 测试API连接
    result = await client.get_real_time_quotation('000001.SZ', 'latest')
    
    return result.get('errorcode') == 0
```

#### **检查项目**
1. **环境变量检查**: 验证IFIND_REFRESH_TOKEN是否配置
2. **API客户端初始化**: 测试客户端是否能正常创建
3. **Token管理**: 验证refresh_token能否获取access_token
4. **API连接**: 测试实际的数据获取功能

#### **运行模式**
- **详细模式** (默认): 显示完整的检查过程和结果
- **简化模式** (--quiet): 只显示最终状态，适合启动脚本

### **2. 启动脚本集成 (quick_start.sh)**

#### **集成位置**
```bash
# 第二步：检查金融数据源
echo ""
echo "💰 第二步：检查金融数据源状态..."

if [ -f "check_financial_datasource.py" ]; then
    echo "🔍 正在检查同花顺iFinD数据源..."
    
    # 使用简化模式检查
    python check_financial_datasource.py --quiet
    datasource_status=$?
    
    if [ $datasource_status -eq 0 ]; then
        echo "✅ 金融数据源检查完成"
    else
        echo "⚠️  金融数据源存在问题，但不影响系统启动"
        echo "📝 金融功能可能受限，请查看上述提示进行修复"
    fi
else
    echo "ℹ️  未找到金融数据源检查模块，跳过检查"
fi
```

#### **启动流程**
1. **第一步**: 启动SearXNG服务 (后台)
2. **第二步**: 检查金融数据源状态 (新增)
3. **第三步**: 启动Agent-Zero服务 (前台)

---

## 📊 **检查结果说明**

### **✅ 正常状态**
```
🔍 正在检查同花顺iFinD数据源...
✅ API连接: 数据获取成功
✅ 金融数据源: 正常
✅ 金融数据源检查完成
```

**含义**: 
- Token配置正确
- API连接正常
- 数据获取成功
- 金融功能完全可用

### **⚠️ 异常状态**
```
🔍 正在检查同花顺iFinD数据源...
❌ Token管理: ACCESS_TOKEN获取失败 - Token已过期
⚠️  金融数据源: 状态异常
⚠️  金融数据源存在问题，但不影响系统启动
📝 金融功能可能受限，请查看上述提示进行修复
```

**含义**:
- 存在配置或连接问题
- 金融功能可能不可用
- 系统其他功能正常
- 需要用户手动修复

---

## 🛠️ **使用方法**

### **1. 正常启动 (包含数据源检查)**
```bash
./quick_start.sh
```

**执行流程**:
1. 启动SearXNG
2. 检查金融数据源 (简化模式)
3. 启动Agent-Zero

### **2. 检查所有服务状态**
```bash
./quick_start.sh --check
```

**检查内容**:
- SearXNG服务状态
- Agent-Zero服务状态
- 金融数据源状态 (详细模式)
- 系统进程信息

### **3. 单独检查金融数据源**
```bash
# 详细模式
python check_financial_datasource.py

# 简化模式
python check_financial_datasource.py --quiet
```

---

## ⚠️ **常见问题和解决方案**

### **1. Token未配置**
**问题**: `❌ 金融数据源: REFRESH_TOKEN未配置`

**解决方案**:
```bash
# 编辑.env文件
nano .env

# 添加配置
IFIND_REFRESH_TOKEN=你的refresh_token
```

### **2. Token过期**
**问题**: `❌ Token管理: ACCESS_TOKEN获取失败 - Token已过期`

**解决方案**:
1. 登录同花顺iFinD平台
2. 获取新的refresh_token
3. 更新.env文件中的配置
4. 重启Agent-Zero服务

### **3. 网络连接问题**
**问题**: `❌ API连接: 请求异常 - 网络错误`

**解决方案**:
1. 检查网络连接
2. 确认防火墙设置
3. 检查代理配置
4. 联系网络管理员

### **4. 权限问题**
**问题**: `❌ API连接: 数据获取失败 - 权限不足`

**解决方案**:
1. 确认账户有API访问权限
2. 检查IP白名单设置
3. 联系同花顺技术支持
4. 验证账户状态

---

## 📋 **故障排除指南**

### **详细检查步骤**
```bash
# 1. 检查环境变量
echo $IFIND_REFRESH_TOKEN

# 2. 检查配置文件
cat .env | grep IFIND

# 3. 详细检查数据源
python check_financial_datasource.py

# 4. 检查网络连接
curl -I https://ft.10jqka.com.cn

# 5. 检查Python依赖
pip list | grep dotenv
```

### **日志分析**
```bash
# 查看启动日志
./quick_start.sh 2>&1 | tee startup.log

# 分析错误信息
grep -i error startup.log
grep -i "金融数据源" startup.log
```

---

## 🔧 **配置选项**

### **环境变量**
```bash
# 必需配置
IFIND_REFRESH_TOKEN=你的refresh_token

# 可选配置
IFIND_ACCESS_TOKEN=自动获取
IFIND_API_TIMEOUT=30
IFIND_MAX_RETRIES=3
```

### **检查参数**
```bash
# 详细模式 (默认)
python check_financial_datasource.py

# 简化模式
python check_financial_datasource.py --quiet
python check_financial_datasource.py -q
```

---

## 📈 **监控和维护**

### **定期检查建议**
- **每日**: 通过`./quick_start.sh --check`检查状态
- **每周**: 详细检查Token有效期
- **每月**: 验证API权限和配额

### **预防性维护**
- **Token管理**: 在过期前1周更新refresh_token
- **网络监控**: 定期检查API连接稳定性
- **日志分析**: 定期分析错误日志和性能指标

### **自动化建议**
```bash
# 创建定时检查脚本
#!/bin/bash
cd /path/to/agent-zero
python check_financial_datasource.py --quiet
if [ $? -ne 0 ]; then
    echo "金融数据源异常，请检查" | mail -s "Agent-Zero Alert" <EMAIL>
fi
```

---

## 🎯 **功能价值**

### **用户体验提升**
- **主动发现**: 启动时主动检查，避免使用时才发现问题
- **清晰提示**: 明确的状态信息和解决建议
- **非阻塞**: 不影响系统正常启动和使用

### **运维效率提升**
- **快速诊断**: 一键检查所有相关组件状态
- **问题定位**: 详细的错误信息和排查指导
- **预防维护**: 提前发现潜在问题

### **系统稳定性**
- **健康监控**: 持续监控数据源状态
- **故障隔离**: 数据源问题不影响其他功能
- **快速恢复**: 提供明确的修复步骤

---

## 📚 **相关文档**

- **金融数据工具实现指南**: `FINANCIAL_DATA_TOOL_IMPLEMENTATION_GUIDE.md`
- **集成完成报告**: `FINANCIAL_DATA_TOOL_INTEGRATION_REPORT.md`
- **同花顺API文档**: `HTTP20230404用户手册.txt`

---

## 🔄 **版本历史**

### **v1.0 (2025-07-08)**
- ✅ 实现基础数据源检查功能
- ✅ 集成到启动脚本
- ✅ 支持详细和简化两种模式
- ✅ 提供完整的故障排除指导

### **未来计划**
- **v1.1**: 添加更多数据源支持
- **v1.2**: 实现自动修复功能
- **v1.3**: 集成监控告警系统

---

**文档版本**: v1.0  
**最后更新**: 2025-07-08  
**维护团队**: Agent-Zero开发团队
