# PocketFlow 扩展开发快速参考

## 🚀 5分钟快速开始

### 1. 创建新工具（自动化）
```bash
# 使用脚本创建（推荐）
./scripts/create_new_tool.sh my_tool "我的自定义工具"

# 手动创建
cp templates/pocketflow_tool_template.py python/tools/my_tool.py
cp templates/system_prompt_template.md prompts/default/agent.system.tool.my_tool.md
```

### 2. 核心文件结构
```
python/tools/my_tool.py              # 工具实现
prompts/default/agent.system.tool.my_tool.md  # 系统提示
examples/test_my_tool.py             # 测试脚本
```

### 3. 基本工具模板
```python
from python.helpers.tool import Tool, Response
from python.helpers.pocketflow_adapter import AgentZeroLLMAdapter

class MyTool(Tool):
    async def execute(self, input_data="", **kwargs):
        llm_adapter = AgentZeroLLMAdapter(self.agent)
        
        # 您的处理逻辑
        result = await self.process_data(input_data, llm_adapter)
        
        return Response(message=result, break_loop=False)
```

## 📋 开发检查清单

### ✅ 必需步骤
- [ ] 创建工具文件 `python/tools/your_tool.py`
- [ ] 创建系统提示 `prompts/default/agent.system.tool.your_tool.md`
- [ ] 注册到 `prompts/default/agent.system.tools.md`
- [ ] 创建测试脚本 `examples/test_your_tool.py`
- [ ] 测试工具功能

### ✅ 可选优化
- [ ] 添加错误处理和重试机制
- [ ] 实现批处理功能
- [ ] 添加进度显示
- [ ] 创建详细文档

## 🛠️ 常用代码片段

### LLM 调用 ⭐⭐⭐
```python
# ✅ 推荐方式
llm_adapter = AgentZeroLLMAdapter(self.agent)
response = await llm_adapter.call_llm(prompt, system_prompt)

# ✅ 直接调用（更稳定）
response = await self.agent.call_utility_model(
    system="你是一个有用的AI助手",
    message=prompt
)
```

### YAML 解析
```python
from python.helpers.pocketflow_adapter import YAMLParser

yaml_str = YAMLParser.extract_yaml_from_response(response)
result = YAMLParser.safe_load(yaml_str)
```

### 批处理节点
```python
class MyBatchNode(BatchNode):
    def prep(self, shared):
        return [(item, config) for item in shared["data_list"]]
    
    def exec(self, task_input):
        item, config = task_input
        return self.process_item(item, config)
    
    def post(self, shared, prep_res, exec_res_list):
        shared["results"] = exec_res_list
```

### 错误处理
```python
try:
    result = await self.process_data(data)
    shared["result"] = result
except Exception as e:
    shared["error"] = str(e)
    print(f"❌ 处理失败: {e}")
```

## 🎯 常见应用模式

### 1. 简单处理工具
```python
输入 → LLM处理 → 格式化输出
```

### 2. 批量处理工具
```python
输入 → 数据分割 → 并行处理 → 结果合并
```

### 3. 多步骤工作流
```python
输入 → 验证 → 处理1 → 处理2 → 输出
```

### 4. 条件分支工具
```python
输入 → 分析 → 条件判断 → 不同处理路径
```

## 📊 性能优化技巧

### 1. 批处理优化
- 使用 `BatchNode` 处理大量数据
- 合理设置批次大小
- 实现并行处理

### 2. 内存管理
- 及时清理大型数据对象
- 使用生成器处理大文件
- 避免在共享状态中存储大量数据

### 3. 错误恢复
- 实现重试机制
- 保存中间结果
- 提供降级处理方案

## 🔧 调试技巧

### 1. 添加调试输出
```python
print(f"🔍 调试: {shared}")
import json
with open("debug.json", "w") as f:
    json.dump(shared, f, indent=2, ensure_ascii=False)
```

### 2. 分步测试
```python
# 单独测试每个节点
node = MyNode()
await node.run_async(test_shared)
```

### 3. 模拟数据测试
```python
test_data = {
    "input_data": "测试数据",
    "llm_adapter": MockLLMAdapter()
}
```

### 4. 超时问题调试 ⚠️
```python
# 如果出现 30 秒超时警告：
# 1. 检查 LLM 调用方式
# 2. 简化异步调用链
# 3. 添加进度输出
print(f"🚀 开始步骤1...")
await step1()
print(f"✅ 步骤1完成")
```

## 📚 示例工具类型

### 1. 数据处理类
- 文本分析工具
- 数据清洗工具
- 格式转换工具

### 2. 内容生成类
- 文章写作工具
- 代码生成工具
- 报告生成工具

### 3. 分析决策类
- 情感分析工具
- 风险评估工具
- 推荐系统工具

### 4. 自动化流程类
- 批量处理工具
- 工作流编排工具
- 监控报警工具

## ⚡ 快速命令

### 测试工具
```bash
# 独立测试
python examples/test_my_tool.py

# 在 Agent Zero 中测试
# 启动服务后发送: "请使用 my_tool 处理数据"
```

### 检查注册
```bash
grep -r "my_tool" prompts/default/
```

### 查看日志
```bash
# 查看 Agent Zero 日志
tail -f logs/agent.log
```

## 🎯 最佳实践总结

1. **从简单开始**：先实现基本功能，再添加复杂特性
2. **避免超时问题**：使用简化的工作流，避免复杂异步调用链 ⚠️
3. **统一 LLM 调用**：优先使用 `agent.call_utility_model()` ⭐
4. **充分测试**：创建全面的测试用例
5. **错误处理**：始终包含异常处理逻辑
6. **用户友好**：提供清晰的进度提示和错误信息
7. **文档完整**：编写详细的使用说明
8. **性能考虑**：优化处理大量数据的场景
9. **模块化设计**：保持代码结构清晰可维护
10. **调试友好**：添加详细的日志输出

## 🔗 相关文档

- [完整开发指南](POCKETFLOW_EXTENSION_GUIDE.md)
- [故障排除指南](POCKETFLOW_TROUBLESHOOTING_GUIDE.md) ⚠️
- [扩展可能性分析](MD_files/POCKETFLOW_EXTENSION_POSSIBILITIES.md)
- [代码模板](templates/)
- [示例代码](examples/)

## 🆘 遇到问题？

1. **超时问题**：查看 [故障排除指南](POCKETFLOW_TROUBLESHOOTING_GUIDE.md#1-超时问题)
2. **LLM 调用失败**：查看 [LLM 调用最佳实践](POCKETFLOW_TROUBLESHOOTING_GUIDE.md#2-llm-调用失败)
3. **代码执行问题**：查看 [代码执行解决方案](POCKETFLOW_TROUBLESHOOTING_GUIDE.md#3-代码执行问题)

---

💡 **提示**: 遇到问题时，先检查 [故障排除指南](POCKETFLOW_TROUBLESHOOTING_GUIDE.md)，然后参考现有的代码生成工具实现！
